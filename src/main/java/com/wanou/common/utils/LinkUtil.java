package com.wanou.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.wanou.common.constant.Constants;
import com.wanou.framework.config.properties.LinkProperties;
import com.wanou.framework.redis.RedisCache;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.PageDomain;
import com.wanou.framework.web.page.TableSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @ClassName LinkUtil
 * @Description 物联网平台工具类
 * <AUTHOR>
 * @Date 2024-02-23 14:22
 */
@Component
@Slf4j
public class LinkUtil {
    @Resource
    private LinkProperties linkProperties;
    @Resource
    private RedisCache redisCache;

    /**
     * 登录物联网平台
     */
    private String login(){
        JSONObject loginParams = new JSONObject();
        loginParams.put("username", linkProperties.getUsername());
        loginParams.put("password", linkProperties.getPassword());
        loginParams.put("uuid", linkProperties.getUuid());

        String res = HttpRequest.post(linkProperties.getUrl() + Constants.LINK_LOGIN_URL)
                .body(loginParams.toJSONString()).execute().body();
        if(StrUtil.isBlank(res)){
            log.error("物联网平台登录失败");
            throw new RuntimeException("获取数据失败");
        }
        AjaxResult ajaxResult = JSONObject.parseObject(res, AjaxResult.class);
        if(!"200".equals(ajaxResult.get(AjaxResult.CODE_TAG).toString())){
            log.error("物联网平台登录失败：{}",ajaxResult);
            throw new RuntimeException(ajaxResult.get(AjaxResult.MSG_TAG).toString());
        }

        String token = ajaxResult.get("token").toString();
        if(StrUtil.isBlank(token)){
            log.error("物联网平台登录失败：未获取到token---{}",ajaxResult);
            throw new RuntimeException("获取数据失败");
        }

        //保存token（30秒刷新）
        redisCache.setObjectBoundExpire(Constants.LINK_TOKEN_CACHE_KEY, token,60*30);

        return token;
    }

    /**
     * 获取token
     */
    private String getToken() {
        Object tokenCacheObj = redisCache.getObjectBound(Constants.LINK_TOKEN_CACHE_KEY);
        if(tokenCacheObj == null){
            //token不存在，登录获取token
            return login();
        }
        return tokenCacheObj.toString();
    }

    private <T> T execute(HttpRequest httpRequest,Class<T> clazz){
        String res = httpRequest.header("Authorization", "Bearer " + getToken())
                .execute().body();
        if(StrUtil.isBlank(res)){
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(res, JSONObject.class);
        if(jsonObject.getInteger("code") == 401 || jsonObject.getInteger("code") == 403){
            login();
        }

        //清理排序参数
        removeOrderByParams();

        return JSONObject.parseObject(res, clazz);
    }

    /**
     * 创建参数对象
     * @return
     */
    public JSONObject createParams(){
        return new JSONObject();
    }

    /**
     * 创建分页参数对象
     * @return
     */
    public JSONObject createParamsStartPage(){
        JSONObject params = createParams();
        //分页
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if(pageNum != null && pageSize != null){
            params.put("pageNum",pageNum);
            params.put("pageSize",pageSize);
        }
        if(StrUtil.isNotBlank(pageDomain.getOrderBy())){
            params.put("orderByColumn",pageDomain.getOrderByColumn());
        }
        if(StrUtil.isNotBlank(pageDomain.getIsAsc())){
            params.put("isAsc",pageDomain.getIsAsc());
        }

        return params;
    }

    /**
     * 获取物联网平台数据 GET请求
     * @return
     * @param <T>
     */
    public <T> T getLinkDataDoGet(String url, Class<T> clazz, JSONObject params){
        return execute(HttpRequest.get(linkProperties.getUrl() + url +
                (CollUtil.isEmpty(params) ? "" : StrUtil.format("?{}", HttpUtil.toParams(params)))),clazz);
    }

    private void removeOrderByParams(){
        HttpServletRequest request = ServletUtils.getRequest();
        request.removeAttribute("orderByColumn");
        request.removeAttribute("isAsc");
    }
}
