package com.wanou.common.utils;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;

/**
 * @ClassName DesensitizedUtils
 * @Description 脱敏工具类
 * <AUTHOR>
 * @Date 2024-01-29 9:04
 */
public class DesensitizedUtils {

    public static String desensitized(String src,String type) {
        if(type.contains("name") || type.contains("Name") || type.contains("NAME")){
            return DesensitizedUtil.chineseName(src);
        }
        if(type.contains("phone") || type.contains("Phone") || type.contains("PHONE")){
            return DesensitizedUtil.mobilePhone(src);
        }
        if(type.contains("idcard") || type.contains("idCard") || type.contains("IdCard") || type.contains("Idcard")){
            return DesensitizedUtil.idCardNum(src,4,4);
        }
        if(type.contains("email") || type.contains("Email") || type.contains("EMAIL")){
            return DesensitizedUtil.email(src);
        }
        return src;
    }
}
