package com.wanou.common.utils;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

import java.nio.charset.StandardCharsets;

/**
 * @ClassName AesUtil
 * @Description 内部aes加解密工具类
 * <AUTHOR>
 * @Date 2024-01-27 14:58
 */
public class AesUtil {

    private static final String KEY = "G6u8defIUPRXu49vZuf-jDis";
    private static final AES aes;
    static {
        aes = new AES(Mode.ECB, Padding.PKCS5Padding,KEY.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * aes加密
     * @param content
     * @return
     */
    public static String encryptToHex(String content) {
        return aes.encryptHex(content,StandardCharsets.UTF_8);
    }

    /**
     * aes解密
     * @param content
     * @return
     */
    public static String decryptFromHex(String content) {
        return aes.decryptStr(content,StandardCharsets.UTF_8);
    }
}
