package com.wanou.project.common;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wanou.framework.redis.RedisCache;
import com.wanou.project.common.domain.District;
import com.wanou.project.common.service.IDistrictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.StringUtils;
import com.wanou.common.utils.file.FileUploadUtils;
import com.wanou.common.utils.file.FileUtils;
import com.wanou.framework.config.RuoYiConfig;
import com.wanou.framework.config.ServerConfig;
import com.wanou.framework.web.domain.AjaxResult;

import java.util.List;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;
    @Resource
    private RedisCache redisCache;
    @Resource
    private IDistrictService districtService;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 获取地区信息
     * @return
     */
    @GetMapping("/common/getDistrictInfo")
    public AjaxResult getDistrictInfo(String level,String adcode) {
        if("country".equals(level)){
            //获取国家下的省
            return AjaxResult.success(redisCache.getCacheMapBound(Constants.DISTRICTS_PROVINCE,adcode));
        }else if("province".equals(level)){
            //获取省下的市
            return AjaxResult.success(redisCache.getCacheMapBound(Constants.DISTRICTS_CITY,adcode));
        }else if("city".equals(level)){
            //获取市下的区
            return AjaxResult.success(redisCache.getCacheMapBound(Constants.DISTRICTS_DISTRICT,adcode));
        }else {
            return AjaxResult.error("获取地区信息参数异常");
        }
    }

    /**
     * 获取省市区列表
     * @param district
     * @return
     */
    @GetMapping("/common/getDistrict")
    public AjaxResult getDistrict(District district){
        List<District> districts = districtService.getDistrict(district);
        return AjaxResult.success(districts);
    }
}
