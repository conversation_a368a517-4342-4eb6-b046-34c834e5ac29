package com.wanou.project.common.service.impl;

import cn.hutool.http.HttpRequest;
import com.wanou.project.common.domain.District;
import com.wanou.project.common.mapper.DistrictMapper;
import com.wanou.project.common.service.IDistrictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName DistrictServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024-01-26 16:12
 */
@Service
public class DistrictServiceImpl implements IDistrictService {
    @Resource
    private DistrictMapper districtMapper;

    @Override
    public void batchInsertOrUpdate(List<District> districts) {
        districtMapper.batchInsertOrUpdate(districts);
    }

    @Override
    public List<District> getDistrict(District district) {
        return districtMapper.getDistrict(district);
    }
}
