package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.UricWarning;
import com.wanou.project.system.service.IUricWarningService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 尿酸预警Controller
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Api(tags = "尿酸预警管理")
@RestController
@RequestMapping("/system/uricWarning")
public class UricWarningController extends BaseController {
    @Resource
    private IUricWarningService uricWarningService;

    /**
     * 查询尿酸预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:list')")
    @GetMapping("/list")
    public TableDataInfo list(UricWarning uricWarning) {
        startPage();
        List<UricWarning> list = uricWarningService.selectUricWarningList(uricWarning);
        return getDataTable(list);
    }

    /**
     * 导出尿酸预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:export')")
    @Log(title = "尿酸预警", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(UricWarning uricWarning) {
        List<UricWarning> list = uricWarningService.selectUricWarningList(uricWarning);
        ExcelUtil<UricWarning> util = new ExcelUtil<UricWarning>(UricWarning.class);
        return util.exportExcel(list, "尿酸预警数据");
    }

    /**
     * 获取尿酸预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(uricWarningService.selectUricWarningById(id));
    }

    /**
     * 新增尿酸预警
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:add')")
    @Log(title = "尿酸预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UricWarning uricWarning) {
        return toAjax(uricWarningService.insertUricWarning(uricWarning));
    }

    /**
     * 修改尿酸预警
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:edit')")
    @Log(title = "尿酸预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UricWarning uricWarning) {
        return toAjax(uricWarningService.updateUricWarning(uricWarning));
    }

    /**
     * 删除尿酸预警
     */
    @PreAuthorize("@ss.hasPermi('system:uricWarning:remove')")
    @Log(title = "尿酸预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(uricWarningService.deleteUricWarningByIds(ids));
    }
}
