package com.wanou.project.system.controller;

import com.wanou.common.constant.Constants;
import com.wanou.common.constant.HttpStatus;
import com.wanou.framework.web.domain.AjaxResult;

import com.wanou.project.system.domain.MiniUserInfo;
import com.wanou.project.system.domain.mini.MiniUserLogin;
import com.wanou.project.system.domain.mini.MiniUserLoginResponse;
import com.wanou.project.system.service.MiniUserInfoService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/5/9 11:44
 */
@Api(tags = "小程序用户登录")
@RestController
@RequestMapping("/miniApi")
public class MiniUserLoginController {
    @Autowired
    private MiniUserInfoService miniUserInfoService;

    @PostMapping("/login")
    public AjaxResult login(@RequestBody MiniUserLogin miniUserLogin){
        MiniUserLoginResponse response = miniUserInfoService.login(miniUserLogin);
        if(response.getToken() == null){
            return AjaxResult.error(HttpStatus.MINI_USER_NO_BIND,"未绑定");
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, response.getToken());
        return ajax;
    }

    //根据token获取用户信息
    @PostMapping("/getUserInfo")
    public AjaxResult getUserInfo( @RequestBody String token){
        return AjaxResult.success(
                miniUserInfoService.getUserInfo(token));
    }
    //修改用户信息
    @PutMapping("/updateUserInfo")
    public AjaxResult updateUserInfo(@RequestBody MiniUserInfo miniUserInfo){
        miniUserInfoService.updateUserInfo(miniUserInfo);
        return AjaxResult.success();
    }
    //根据小程序用户信息
        @PutMapping("/getMiniUserInfo")
    public AjaxResult getMiniUserInfoById(){
        return AjaxResult.success(miniUserInfoService.getMiniUserInfo());
    }
}
