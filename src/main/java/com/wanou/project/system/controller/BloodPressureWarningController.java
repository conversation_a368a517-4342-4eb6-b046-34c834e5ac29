package com.wanou.project.system.controller;

import java.util.List;

import com.wanou.project.system.domain.dto.BloodPressureWarningDto;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.BloodPressureWarning;
import com.wanou.project.system.service.IBloodPressureWarningService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 血压预警Controller
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Api(tags = "血压预警")
@RestController
@RequestMapping("/system/bloodPressureWarning")
public class BloodPressureWarningController extends BaseController {
    @Resource
    private IBloodPressureWarningService bloodPressureWarningService;

    /**
     * 查询血压预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:list')")
    @GetMapping("/list")
    public TableDataInfo list(BloodPressureWarningDto bloodPressureWarning) {
        return bloodPressureWarningService.selectBloodPressureWarningList(bloodPressureWarning);
    }

    /**
     * 导出血压预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:export')")
    @Log(title = "血压预警", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BloodPressureWarning bloodPressureWarning) {
        /*List<BloodPressureWarning> list = bloodPressureWarningService.selectBloodPressureWarningList(bloodPressureWarning);
        ExcelUtil<BloodPressureWarning> util = new ExcelUtil<BloodPressureWarning>(BloodPressureWarning.class);
        return util.exportExcel(list, "血压预警数据");*/
        return null;
    }

    /**
     * 获取血压预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bloodPressureWarningService.selectBloodPressureWarningById(id));
    }

    /**
     * 新增血压预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:add')")
    @Log(title = "血压预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BloodPressureWarning bloodPressureWarning) {
        return toAjax(bloodPressureWarningService.insertBloodPressureWarning(bloodPressureWarning));
    }

    /**
     * 修改血压预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:edit')")
    @Log(title = "血压预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BloodPressureWarning bloodPressureWarning) {
        return toAjax(bloodPressureWarningService.updateBloodPressureWarning(bloodPressureWarning));
    }

    /**
     * 删除血压预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureWarning:remove')")
    @Log(title = "血压预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bloodPressureWarningService.deleteBloodPressureWarningByIds(ids));
    }
}
