package com.wanou.project.system.controller;


import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.DeviceStore;
import com.wanou.project.system.service.IDeviceStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@RequestMapping("/devicestore")
public class DevicestoreController extends BaseController {
    @Autowired
    private IDeviceStoreService devicestoreService;

    /**
     * 查询设备信息
     * @return
     */
    @GetMapping("/queryDevice")
    public AjaxResult queryDevice(){
        List<DeviceStore>  list = devicestoreService.queryDevice();
        return  AjaxResult.success(list);
    }

    @GetMapping("/{id}")
    public AjaxResult queryDeviceById(@PathVariable  Long id){
        DeviceStore devicestore = devicestoreService.queryDeviceById(id);
        return  AjaxResult.success(devicestore);
    }

    /**
     * 查询设备商城列表
     */
    @PreAuthorize("@ss.hasPermi('system:store:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceStore deviceStore) {
        startPage();
        List<DeviceStore> list = devicestoreService.selectDeviceStoreList(deviceStore);
        return getDataTable(list);
    }

    /**
     * 新增设备商城
     */
    @PreAuthorize("@ss.hasPermi('system:store:add')")
    @Log(title = "设备商城", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceStore deviceStore) {
        return toAjax(devicestoreService.insertDeviceStore(deviceStore));
    }

    /**
     * 修改设备商城
     */
    @PreAuthorize("@ss.hasPermi('system:store:edit')")
    @Log(title = "设备商城", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceStore deviceStore) {
        return toAjax(devicestoreService.updateDeviceStore(deviceStore));
    }

    /**
     * 删除设备商城
     */
    @PreAuthorize("@ss.hasPermi('system:store:remove')")
    @Log(title = "设备商城", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(devicestoreService.deleteDeviceStoreByIds(ids));
    }

    /**
     * 根据分类查询设备
     */
    @GetMapping("/queryDeviceByCategory/{category}")
    public AjaxResult queryDeviceByCategory(@PathVariable  String category){
        List<DeviceStore>  list = devicestoreService.queryDeviceByCategory(category);
        return  AjaxResult.success(list);
    }

}
