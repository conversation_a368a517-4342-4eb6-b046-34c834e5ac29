package com.wanou.project.system.controller;

import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.Address;
import com.wanou.project.system.service.IAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@RestController
@RequestMapping("/address")
public class AddressController extends BaseController {
    @Autowired
    private IAddressService addressService;
    /**
     * 添加地址
     * @param address
     * @return
     */
    @PostMapping("/addAddress")
    public AjaxResult addAddress(@RequestBody Address address){
        addressService.addAddress(address);
        return   AjaxResult.success();
    }
    @GetMapping("/getAddress")
    public AjaxResult getAddress(){
        return AjaxResult.success(addressService.getAddress());
    }

    /**
     * 地址列表
     *
     * @param address
     * @return R
     */
    @PreAuthorize("@ss.hasPermi('system:address:list')")
    @GetMapping("list")
    public TableDataInfo list(Address address) {
        startPage();
        List<Address> list = addressService.selectAddressListApp(address);
        return getDataTable(list);
    }
}
