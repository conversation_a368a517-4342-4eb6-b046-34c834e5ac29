package com.wanou.project.system.controller;


import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.HomeCareProgram;
import com.wanou.project.system.service.IHomeCareProgramService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.wanou.common.utils.poi.ExcelUtil;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@RestController
@RequestMapping("/homeCareProgram")
public class HomeCareProgramController extends BaseController {
    @Autowired
    private IHomeCareProgramService homeCareProgramService;

    /**
     * 查询所有项目列表 *
     * @param
     * @return
     */
    @GetMapping
    public AjaxResult getHomeCareProgramList(){
        startPage();
        List<HomeCareProgram> list= homeCareProgramService.getList();
        return AjaxResult.success(list);
    }

    /**
     * 查询家庭护理栏目列表
     */
    @PreAuthorize("@ss.hasPermi('system:program:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeCareProgram homeCareProgram) {
        startPage();
        List<HomeCareProgram> list = homeCareProgramService.selectHomeCareProgramList(homeCareProgram);
        return getDataTable(list);
    }

    /**
     * 导出家庭护理栏目列表
     */
    @PreAuthorize("@ss.hasPermi('system:program:export')")
    @Log(title = "家庭护理栏目", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(HomeCareProgram homeCareProgram) {
        List<HomeCareProgram> list = homeCareProgramService.selectHomeCareProgramList(homeCareProgram);
        ExcelUtil<HomeCareProgram> util = new ExcelUtil<HomeCareProgram>(HomeCareProgram.class);
        return util.exportExcel(list, "家庭护理栏目数据");
    }

    /**
     * 获取家庭护理栏目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:program:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(homeCareProgramService.selectHomeCareProgramById(id));
    }

    /**
     * 新增家庭护理栏目
     */
    @PreAuthorize("@ss.hasPermi('system:program:add')")
    @Log(title = "家庭护理栏目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeCareProgram homeCareProgram) {
        return toAjax(homeCareProgramService.insertHomeCareProgram(homeCareProgram));
    }

    /**
     * 修改家庭护理栏目
     */
    @PreAuthorize("@ss.hasPermi('system:program:edit')")
    @Log(title = "家庭护理栏目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeCareProgram homeCareProgram) {
        return toAjax(homeCareProgramService.updateHomeCareProgram(homeCareProgram));
    }

    /**
     * 删除家庭护理栏目
     */
    @PreAuthorize("@ss.hasPermi('system:program:remove')")
    @Log(title = "家庭护理栏目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeCareProgramService.deleteHomeCareProgramByIds(ids));
    }
}
