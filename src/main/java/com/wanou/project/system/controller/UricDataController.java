package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.UricData;
import com.wanou.project.system.service.IUricDataService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 尿酸数据Controller
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Api(tags = "尿酸数据")
@RestController
@RequestMapping("/system/uricData")
public class UricDataController extends BaseController {
    @Resource
    private IUricDataService uricDataService;

    /**
     * 查询尿酸数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:list')")
    @GetMapping("/list")
    public TableDataInfo list(UricData uricData) {
        startPage();
        List<UricData> list = uricDataService.selectUricDataList(uricData);
        return getDataTable(list);
    }

    /**
     * 导出尿酸数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:export')")
    @Log(title = "尿酸数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(UricData uricData) {
        List<UricData> list = uricDataService.selectUricDataList(uricData);
        ExcelUtil<UricData> util = new ExcelUtil<UricData>(UricData.class);
        return util.exportExcel(list, "尿酸数据数据");
    }

    /**
     * 获取尿酸数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(uricDataService.selectUricDataById(id));
    }

    /**
     * 新增尿酸数据
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:add')")
    @Log(title = "尿酸数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UricData uricData) {
        return toAjax(uricDataService.insertUricData(uricData));
    }

    /**
     * 修改尿酸数据
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:edit')")
    @Log(title = "尿酸数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UricData uricData) {
        return toAjax(uricDataService.updateUricData(uricData));
    }

    /**
     * 删除尿酸数据
     */
    @PreAuthorize("@ss.hasPermi('system:uricData:remove')")
    @Log(title = "尿酸数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(uricDataService.deleteUricDataByIds(ids));
    }
}
