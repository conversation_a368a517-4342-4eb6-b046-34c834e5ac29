package com.wanou.project.system.controller;

import java.util.List;

import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.BloodPressureData;
import com.wanou.project.system.service.IBloodPressureDataService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 血压数据Controller
 *
 * <AUTHOR>
 * @date 2024-02-22
 */
@Api(tags = "血压数据管理")
@RestController
@RequestMapping("/system/bloodPressureData")
public class BloodPressureDataController extends BaseController {
    @Resource
    private IBloodPressureDataService bloodPressureDataService;

    /**
     * 查询血压数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:list')")
    @GetMapping("/list")
    @Decrypt(field = {"elderPhone"},isDesensitization = true)
    public TableDataInfo list(BloodPressureData bloodPressureData) {
        return bloodPressureDataService.selectBloodPressureDataList(bloodPressureData);
    }

    /**
     * 导出血压数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:export')")
    @Log(title = "血压数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BloodPressureData bloodPressureData) {
        /*List<BloodPressureData> list = bloodPressureDataService.selectBloodPressureDataList(bloodPressureData);
        ExcelUtil<BloodPressureData> util = new ExcelUtil<BloodPressureData>(BloodPressureData.class);
        return util.exportExcel(list, "血压数据数据");*/
        return null;
    }

    /**
     * 获取血压数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bloodPressureDataService.selectBloodPressureDataById(id));
    }

    /**
     * 新增血压数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:add')")
    @Log(title = "血压数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BloodPressureData bloodPressureData) {
        return toAjax(bloodPressureDataService.insertBloodPressureData(bloodPressureData));
    }

    /**
     * 修改血压数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:edit')")
    @Log(title = "血压数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BloodPressureData bloodPressureData) {
        return toAjax(bloodPressureDataService.updateBloodPressureData(bloodPressureData));
    }

    /**
     * 删除血压数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodPressureData:remove')")
    @Log(title = "血压数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bloodPressureDataService.deleteBloodPressureDataByIds(ids));
    }
}
