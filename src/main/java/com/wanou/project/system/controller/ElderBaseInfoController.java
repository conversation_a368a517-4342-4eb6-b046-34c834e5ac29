package com.wanou.project.system.controller;

import java.util.List;

import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import com.wanou.framework.aspectj.lang.annotation.Encrypt;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.ElderBaseInfo;
import com.wanou.project.system.service.IElderBaseInfoService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

/**
 * 长者基本信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Api(tags = "长者基本信息")
@RestController
@RequestMapping("/system/elderBaseInfo")
public class ElderBaseInfoController extends BaseController
{
    @Autowired
    private IElderBaseInfoService elderBaseInfoService;

    /**
     * 查询长者基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:list')")
    @GetMapping("/list")
    @Decrypt(field = {"idCard","phone"},isDesensitization = true)
    public TableDataInfo list(ElderBaseInfo elderBaseInfo)
    {
        startPage();
        List<ElderBaseInfo> list = elderBaseInfoService.selectElderBaseInfoList(elderBaseInfo);
        TableDataInfo dataTable = getDataTable(list);
        return dataTable;
    }

    /**
     * 导出长者基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:export')")
    @Log(title = "长者基本信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ElderBaseInfo elderBaseInfo)
    {
        List<ElderBaseInfo> list = elderBaseInfoService.selectElderBaseInfoList(elderBaseInfo);
        ExcelUtil<ElderBaseInfo> util = new ExcelUtil<ElderBaseInfo>(ElderBaseInfo.class);
        return util.exportExcel(list, "长者基本信息数据");
    }

    /**
     * 获取长者基本信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:query')")
    @GetMapping(value = "/{id}")
    @Decrypt(field = {"idCard","phone"},isDesensitization = false)
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(elderBaseInfoService.selectElderBaseInfoById(id));
    }

    /**
     * 新增长者基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:add')")
    @Log(title = "长者基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    @Encrypt(field = {"idCard","phone"})
    public AjaxResult add(@RequestBody ElderBaseInfo elderBaseInfo)
    {
        return toAjax(elderBaseInfoService.insertElderBaseInfo(elderBaseInfo));
    }

    /**
     * 修改长者基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:edit')")
    @Log(title = "长者基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Encrypt(field = {"idCard","phone"})
    public AjaxResult edit(@RequestBody ElderBaseInfo elderBaseInfo)
    {
        return toAjax(elderBaseInfoService.updateElderBaseInfo(elderBaseInfo));
    }

    /**
     * 删除长者基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:elderBaseInfo:remove')")
    @Log(title = "长者基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(elderBaseInfoService.deleteElderBaseInfoByIds(ids));
    }

    /**
     * 统计长者信息
     */
    @GetMapping("/totalElderData")
    public AjaxResult totalElderData(){
        return elderBaseInfoService.totalElderData();
    }
}
