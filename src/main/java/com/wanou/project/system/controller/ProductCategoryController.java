package com.wanou.project.system.controller;

import com.wanou.common.api.CommonPage;
import com.wanou.common.api.CommonResult;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.ProductCategory;
import com.wanou.project.system.service.IProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品分类管理
 * Created by djf on 2025/2/21
 */
@RestController
@Api(tags = "ProductCategoryController")
@Tag(name = "ProductCategoryController", description = "商品分类管理")
@RequestMapping("/system/productCategory")
public class ProductCategoryController extends BaseController {
    @Autowired
    private IProductCategoryService productCategoryService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public TableDataInfo getProductCategoryListApp(ProductCategory productCategory){
        startPage();
        List<ProductCategory> list= productCategoryService.getProductCategoryListApp(productCategory);
        return getDataTable(list);
    }

    @ApiOperation("分页查询商品分类")
    @RequestMapping(value = "/list/{parentId}", method = RequestMethod.GET)
    public CommonResult<CommonPage<ProductCategory>> getList(@PathVariable Long parentId,
                                                             @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        List<ProductCategory> productCategoryList = productCategoryService.getList(parentId, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(productCategoryList));
    }

    @ApiOperation("根据id获取商品分类")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public CommonResult<ProductCategory> getItem(@PathVariable Long id) {
        ProductCategory productCategory = productCategoryService.getItem(id);
        return CommonResult.success(productCategory);
    }

    @ApiOperation("添加商品分类")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public CommonResult create(@Validated @RequestBody ProductCategory productCategory) {
        int count = productCategoryService.insertProductCategory(productCategory);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("修改商品分类")
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public CommonResult update(@PathVariable Long id,
                               @Validated
                               @RequestBody ProductCategory productCategory) {
        int count = productCategoryService.updateProductCategory(id, productCategory);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("删除商品分类")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult delete(@PathVariable Long id) {
        int count = productCategoryService.deleteProductCategory(id);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("修改显示状态")
    @RequestMapping(value = "/update/isAvailable", method = RequestMethod.POST)
    public CommonResult update(@RequestParam("ids") List<Long> ids, @RequestParam("isAvailable") Integer isAvailable) {
        int count = productCategoryService.updateIsAvailable(ids, isAvailable);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed();
        }
    }
}
