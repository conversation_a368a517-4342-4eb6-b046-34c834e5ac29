package com.wanou.project.system.controller;

import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.project.system.service.IServiceCommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@RestController
@RequestMapping("/service-comments")
public class ServiceCommentsController {
    @Autowired
    private IServiceCommentsService serviceCommentsService;
    //根据服务id查询服务评论
    @GetMapping("/list")
    public AjaxResult list(String serviceId){
        return AjaxResult.success(serviceCommentsService.list(serviceId));
    }
    //通过服务id查询有多少条评论
    @GetMapping("/count")
    public AjaxResult count(String serviceId){
        return AjaxResult.success(serviceCommentsService.count(serviceId));
        }

}
