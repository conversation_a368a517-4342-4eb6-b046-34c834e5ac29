package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.BloodGlucoseWarning;
import com.wanou.project.system.service.IBloodGlucoseWarningService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 血糖预警Controller
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Api(tags = "血糖预警")
@RestController
@RequestMapping("/system/bloodGlucoseWarning")
public class
BloodGlucoseWarningController extends BaseController {
    @Resource
    private IBloodGlucoseWarningService bloodGlucoseWarningService;

    /**
     * 查询血糖预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:list')")
    @GetMapping("/list")
    public TableDataInfo list(BloodGlucoseWarning bloodGlucoseWarning) {
        return bloodGlucoseWarningService.selectBloodGlucoseWarningList(bloodGlucoseWarning);
    }

    /**
     * 导出血糖预警列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:export')")
    @Log(title = "血糖预警", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BloodGlucoseWarning bloodGlucoseWarning) {
        /*List<BloodGlucoseWarning> list = bloodGlucoseWarningService.selectBloodGlucoseWarningList(bloodGlucoseWarning);
        ExcelUtil<BloodGlucoseWarning> util = new ExcelUtil<BloodGlucoseWarning>(BloodGlucoseWarning.class);
        return util.exportExcel(list, "血糖预警数据");*/
        return null;
    }

    /**
     * 获取血糖预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bloodGlucoseWarningService.selectBloodGlucoseWarningById(id));
    }

    /**
     * 新增血糖预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:add')")
    @Log(title = "血糖预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BloodGlucoseWarning bloodGlucoseWarning) {
        return toAjax(bloodGlucoseWarningService.insertBloodGlucoseWarning(bloodGlucoseWarning));
    }

    /**
     * 修改血糖预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:edit')")
    @Log(title = "血糖预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BloodGlucoseWarning bloodGlucoseWarning) {
        return toAjax(bloodGlucoseWarningService.updateBloodGlucoseWarning(bloodGlucoseWarning));
    }

    /**
     * 删除血糖预警
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseWarning:remove')")
    @Log(title = "血糖预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bloodGlucoseWarningService.deleteBloodGlucoseWarningByIds(ids));
    }
}
