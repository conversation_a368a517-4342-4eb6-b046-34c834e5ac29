package com.wanou.project.system.controller;

import java.util.List;

import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.EquipmentAlarmInformation;
import com.wanou.project.system.service.IEquipmentAlarmInformationService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 设备报警信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-17
 */
@Api(tags = "设备报警信息")
@RestController
@RequestMapping("/system/equipmentAlarmInformation")
public class EquipmentAlarmInformationController extends BaseController {
    @Resource
    private IEquipmentAlarmInformationService equipmentAlarmInformationService;

    /**
     * 查询设备报警信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:list')")
    @GetMapping("/list")
    @Decrypt(field = {"elderPhone"},isDesensitization = true)
    public TableDataInfo list(EquipmentAlarmInformation equipmentAlarmInformation) {
        startPage();
        return equipmentAlarmInformationService.selectEquipmentAlarmInformationList(equipmentAlarmInformation);
    }

    /**
     * 导出设备报警信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:export')")
    @Log(title = "设备报警信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(EquipmentAlarmInformation equipmentAlarmInformation) {
        /*List<EquipmentAlarmInformation> list = equipmentAlarmInformationService.selectEquipmentAlarmInformationList(equipmentAlarmInformation);
        ExcelUtil<EquipmentAlarmInformation> util = new ExcelUtil<EquipmentAlarmInformation>(EquipmentAlarmInformation.class);
        return util.exportExcel(list, "设备报警信息数据");*/
        return null;
    }

    /**
     * 获取设备报警信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(equipmentAlarmInformationService.selectEquipmentAlarmInformationById(id));
    }

    /**
     * 新增设备报警信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:add')")
    @Log(title = "设备报警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EquipmentAlarmInformation equipmentAlarmInformation) {
        return toAjax(equipmentAlarmInformationService.insertEquipmentAlarmInformation(equipmentAlarmInformation));
    }

    /**
     * 处理设备报警信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:dispose')")
    @Log(title = "设备报警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult dispose(@RequestBody EquipmentAlarmInformation equipmentAlarmInformation) {
        return toAjax(equipmentAlarmInformationService.disposeEquipmentAlarmInformation(equipmentAlarmInformation));
    }

    /**
     * 删除设备报警信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipmentAlarmInformation:remove')")
    @Log(title = "设备报警信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(equipmentAlarmInformationService.deleteEquipmentAlarmInformationByIds(ids));
    }
}
