package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.ElderFamilyMembers;
import com.wanou.project.system.service.IElderFamilyMembersService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 长者家属信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Api(tags = "长者家属信息")
@RestController
@RequestMapping("/system/elderFamilyMembers")
public class ElderFamilyMembersController extends BaseController {
    @Resource
    private IElderFamilyMembersService elderFamilyMembersService;

    /**
     * 查询长者家属信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:list')")
    @GetMapping("/list")
    public TableDataInfo list(ElderFamilyMembers elderFamilyMembers) {
        startPage();
        List<ElderFamilyMembers> list = elderFamilyMembersService.selectElderFamilyMembersList(elderFamilyMembers);
        return getDataTable(list);
    }

    /**
     * 导出长者家属信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:export')")
    @Log(title = "长者家属信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ElderFamilyMembers elderFamilyMembers) {
        List<ElderFamilyMembers> list = elderFamilyMembersService.selectElderFamilyMembersList(elderFamilyMembers);
        ExcelUtil<ElderFamilyMembers> util = new ExcelUtil<ElderFamilyMembers>(ElderFamilyMembers.class);
        return util.exportExcel(list, "长者家属信息数据");
    }

    /**
     * 获取长者家属信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(elderFamilyMembersService.selectElderFamilyMembersById(id));
    }

    /**
     * 新增长者家属信息
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:add')")
    @Log(title = "长者家属信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ElderFamilyMembers elderFamilyMembers) {
        return toAjax(elderFamilyMembersService.insertElderFamilyMembers(elderFamilyMembers));
    }

    /**
     * 修改长者家属信息
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:edit')")
    @Log(title = "长者家属信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ElderFamilyMembers elderFamilyMembers) {
        return toAjax(elderFamilyMembersService.updateElderFamilyMembers(elderFamilyMembers));
    }

    /**
     * 删除长者家属信息
     */
    @PreAuthorize("@ss.hasPermi('system:elderFamilyMembers:remove')")
    @Log(title = "长者家属信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(elderFamilyMembersService.deleteElderFamilyMembersByIds(ids));
    }
}
