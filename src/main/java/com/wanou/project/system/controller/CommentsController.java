package com.wanou.project.system.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.Comments;
import com.wanou.project.system.service.ICommentsService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 商品评价Controller
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/system/comments")
public class CommentsController extends BaseController {
    @Resource
    private ICommentsService commentsService;

    /**
     * App端查询商品评价列表
     */
    @GetMapping("/appList")
    public TableDataInfo appList(Comments comments) {
        startPage();
        List<Comments> list = commentsService.selectCommentsListApp(comments);
        return getDataTable(list);
    }

    /**
     * 查询商品评价列表
     */
    // 使用@PreAuthorize注解进行权限验证，只有拥有system:comments:list权限的用户才能访问该方法
    @PreAuthorize("@ss.hasPermi('system:comments:list')")
    // 使用@GetMapping注解指定该方法为GET请求，请求路径为/list
    @GetMapping("/list")
    // 定义一个方法，接收一个Comments类型的参数comments，返回一个TableDataInfo类型的结果
    public TableDataInfo list(Comments comments) {
        // 调用startPage()方法，开启分页查询
        startPage();
        // 调用commentsService的selectCommentsList方法，查询商品评价列表，并将结果赋值给list
        List<Comments> list = commentsService.selectCommentsList(comments);
        // 调用getDataTable方法，将查询结果封装成TableDataInfo类型，并返回
        return getDataTable(list);
    }

    /**
     * 导出商品评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:comments:export')")
    @Log(title = "商品评价", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Comments comments) {
        List<Comments> list = commentsService.selectCommentsList(comments);
        ExcelUtil<Comments> util = new ExcelUtil<Comments>(Comments.class);
        return util.exportExcel(list, "商品评价数据");
    }

    /**
     * 获取商品评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:comments:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(commentsService.selectCommentsById(id));
    }

    /**
     * 新增商品评价
     */
//    @PreAuthorize("@ss.hasPermi('system:comments:add')")
//    @Log(title = "商品评价", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Comments comments) {
        return toAjax(commentsService.insertComments(comments));
    }

    /**
     * 修改商品评价
     */
    // 使用@PreAuthorize注解，判断用户是否有修改商品评价的权限
    @PreAuthorize("@ss.hasPermi('system:comments:edit')")
    // 使用@Log注解，记录操作日志
    @Log(title = "商品评价", businessType = BusinessType.UPDATE)
    // 使用@PutMapping注解，表示这是一个PUT请求
    @PutMapping
    // 接收一个Comments类型的参数，并返回一个AjaxResult类型的结果
    public AjaxResult edit(@RequestBody Comments comments) {
        // 调用commentsService的updateComments方法，更新商品评价，并返回结果
        return toAjax(commentsService.updateComments(comments));
    }

    /**
     * 删除商品评价
     */
    @PreAuthorize("@ss.hasPermi('system:comments:remove')")
    @Log(title = "商品评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(commentsService.deleteCommentsByIds(ids));
    }
}
