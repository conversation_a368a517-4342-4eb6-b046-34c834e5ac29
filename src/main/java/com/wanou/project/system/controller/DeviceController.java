package com.wanou.project.system.controller;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;
import io.swagger.annotations.Api;
import io.swagger.models.auth.In;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.Device;
import com.wanou.project.system.service.IDeviceService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 设备基本信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
@Api(tags = "设备基本信息")
@RestController
@RequestMapping("/system/device")
public class DeviceController extends BaseController {
    @Resource
    private IDeviceService deviceService;


    @GetMapping("/listDeviceElder")
   public TableDataInfo listDeviceElder(Integer elderId) {
       return deviceService.selectDeviceByElderId(elderId);
   }
    /**
     * 查询设备基本信息列表
     */
//        @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(Device device ) {
        return deviceService.selectDeviceList(device);
    }

    /**
     * 统计设备数量
     */
    //    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/getDeviceTotal")
    public AjaxResult getDeviceTotal() {
        JSONObject result = deviceService.getDeviceTotal();
        return AjaxResult.success(result);
    }

    /**
     * 导出设备基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "设备基本信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Device device) {
        List<Device> list = (List<Device>) deviceService.selectDeviceList(device).getRows();
        ExcelUtil<Device> util = new ExcelUtil<Device>(Device.class);
        return util.exportExcel(list, "设备基本信息数据");
    }

    /**
     * 获取设备基本信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {

        return deviceService.selectDeviceById(id);
    }

    /**
     * 新增设备基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "设备基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Device device) {
        return toAjax(deviceService.insertDevice(device));
    }

    /**
     * 修改设备基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "设备基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Device device) {
        return toAjax(deviceService.bindingDevice(device));
    }

    /**
     * 删除设备基本信息
     */
//    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "设备基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }

    /**
     * 设备绑定长者
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('system:device:bindElder')")
    @Log(title = "设备绑定长者", businessType = BusinessType.UPDATE)
    @PostMapping("/bindElder")
    public AjaxResult bindElder(@RequestBody Device device){
        deviceService.bindElder(device);

        return AjaxResult.success();
    }

    /**
     * 统计设备信息
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('system:device:statistics')")
    @GetMapping("/statistics")
    public AjaxResult deviceStatistics() {
        return AjaxResult.success(deviceService.deviceStatistics());
    }

    /**
     * 换绑设备
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('system:device:updateBind')")
    @Log(title = "设备换绑", businessType = BusinessType.UPDATE)
    @PostMapping("/updateElderDeviceBind")
    public AjaxResult updateElderDeviceBind(@RequestBody DeviceElderBindingDto deviceElderBinding){
        deviceService.updateElderDeviceBind(deviceElderBinding);
        return AjaxResult.success();
    }

    //老人解绑设备
    @Log(title = "设备解绑", businessType = BusinessType.UPDATE)
    @PutMapping("/unBindElder")
    public AjaxResult unBindElder(@RequestBody UnBingDto unBingDto){
        deviceService.unBindElder(unBingDto);
        return AjaxResult.success();
    }
    //查询属于老人的设备但是未绑定
    @GetMapping("/getbelongDevice")
    public TableDataInfo getBelongDevice( String elderId){
        return deviceService.getBelongDevice(elderId);
    }
}
