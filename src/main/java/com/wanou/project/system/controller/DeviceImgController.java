package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.DeviceImg;
import com.wanou.project.system.service.IDeviceImgService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 设备图片Controller
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Api(tags = "设备图片")
@RestController
@RequestMapping("/system/deviceImg")
public class DeviceImgController extends BaseController {
    @Resource
    private IDeviceImgService deviceImgService;

    /**
     * 查询设备图片列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceImg:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceImg deviceImg) {
        startPage();
        List<DeviceImg> list = deviceImgService.selectDeviceImgList(deviceImg);
        return getDataTable(list);
    }

    /**
     * 导出设备图片列表
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:export')")
    @Log(title = "设备图片", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DeviceImg deviceImg) {
        List<DeviceImg> list = deviceImgService.selectDeviceImgList(deviceImg);
        ExcelUtil<DeviceImg> util = new ExcelUtil<DeviceImg>(DeviceImg.class);
        return util.exportExcel(list, "设备图片数据");
    }

    /**
     * 获取设备图片详细信息(从养老平台直接获取的)
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(deviceImgService.selectDeviceImgById(id));
    }

    /**
     * 获取设备图片详细信息(从物联网平台获取的)
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:query')")
    @GetMapping(value = "/woLink/{id}")
    public AjaxResult getInfoToWoLink(@PathVariable("id") Long id) {
        return AjaxResult.success(deviceImgService.selectDeviceImgByWolink(id));
    }

    /**
     * 新增设备图片
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:add')")
    @Log(title = "设备图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceImg deviceImg) {
        return toAjax(deviceImgService.insertDeviceImg(deviceImg));
    }

    /**
     * 修改设备图片
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:edit')")
    @Log(title = "设备图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceImg deviceImg) {
        return toAjax(deviceImgService.updateDeviceImg(deviceImg));
    }

    /**
     * 删除设备图片
     */
//    @PreAuthorize("@ss.hasPermi('system:deviceImg:remove')")
    @Log(title = "设备图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(deviceImgService.deleteDeviceImgByIds(ids));
    }
}
