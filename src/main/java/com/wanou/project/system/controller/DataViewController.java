package com.wanou.project.system.controller;

import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.project.system.domain.dto.DataViewDto;
import com.wanou.project.system.service.IDataViewService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName DataViewController
 * @Description 数据大屏接口
 * <AUTHOR>
 * @Date 2024-02-28 9:58
 */
@Api(tags = "数据大屏接口")
@RestController
@RequestMapping("/dataView")
public class DataViewController {
    @Resource
    private IDataViewService dataViewService;

    /**
     * 获取大屏数据
     * @param dataViewDto
     * @return
     */
    @GetMapping("/getData")
    public AjaxResult getData(DataViewDto dataViewDto){
        return AjaxResult.success(dataViewService.getData(dataViewDto));
    }
}
