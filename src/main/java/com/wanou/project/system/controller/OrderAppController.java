package com.wanou.project.system.controller;

import com.wanou.common.api.CommonPage;
import com.wanou.common.api.CommonResult;
import com.wanou.project.system.domain.dto.OrderDetail;
import com.wanou.project.system.service.IOrderAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 订单管理
 * Created by djf on 2025/2/10
 */
@Controller
@Api(tags = "OrderAppController")
@Tag(name = "OrderAppController", description = "订单管理")
@RequestMapping("/order")
public class OrderAppController {
    @Autowired
    private IOrderAppService orderAppService;

    @ApiOperation("按状态分页获取用户订单列表")
    @ApiImplicitParam(name = "status", value = "订单状态：-1->全部；0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭",
            defaultValue = "-1", allowableValues = "-1,0,1,2,3,4", paramType = "query", dataType = "int")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<CommonPage<OrderDetail>> list(@RequestParam Integer status,
                                                      @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                      @RequestParam(required = false, defaultValue = "5") Integer pageSize) {
        CommonPage<OrderDetail> orderPage = orderAppService.list(status,pageNum,pageSize);
        return CommonResult.success(orderPage);
    }

    @ApiOperation("根据订单ID获取订单详情")
    @RequestMapping(value = "/detail/{orderId}", method = RequestMethod.GET)
    @ResponseBody
    public CommonResult<OrderDetail> detail(@PathVariable Long orderId) {
        OrderDetail orderDetail = orderAppService.detail(orderId);
        return CommonResult.success(orderDetail);
    }

    @ApiOperation("用户取消订单")
    @RequestMapping(value = "/cancelOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult cancelOrder(Long orderId) {
        orderAppService.cancelOrder(orderId);
        return CommonResult.success(null);
    }

    @ApiOperation("用户支付成功的回调")
    @RequestMapping(value = "/paySuccess", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult paySuccess(@RequestParam Long orderId,@RequestParam Integer payType) {
        Integer count = orderAppService.paySuccess(orderId,payType);
        return CommonResult.success(count, "支付成功");
    }

    @ApiOperation("用户确认收货")
    @RequestMapping(value = "/confirmReceiveOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult confirmReceiveOrder(Long orderId) {
        orderAppService.confirmReceiveOrder(orderId);
        return CommonResult.success(null);
    }

    @ApiOperation("用户删除订单")
    @RequestMapping(value = "/deleteOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult deleteOrder(Long orderId) {
        orderAppService.deleteOrder(orderId);
        return CommonResult.success(null);
    }
    /**
     * 添加订单
     */
    @ApiOperation("添加订单")
    @RequestMapping(value = "/addOrder", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult addOrder(@RequestBody  OrderDetail orderDetail) {
        Long aLong = orderAppService.addOrder(orderDetail);
        return CommonResult.success( aLong);
    }
}
