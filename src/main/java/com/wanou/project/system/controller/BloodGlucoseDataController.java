package com.wanou.project.system.controller;

import java.util.List;

import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.BloodGlucoseData;
import com.wanou.project.system.service.IBloodGlucoseDataService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 血糖数据Controller
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Api(tags = "血糖数据")
@RestController
@RequestMapping("/system/bloodGlucoseData")
public class BloodGlucoseDataController extends BaseController {
    @Resource
    private IBloodGlucoseDataService bloodGlucoseDataService;

    /**
     * 查询血糖数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:list')")
    @GetMapping("/list")
    @Decrypt(field = {"elderPhone"},isDesensitization = true)
    public TableDataInfo list(BloodGlucoseData bloodGlucoseData) {
        return bloodGlucoseDataService.selectBloodGlucoseDataList(bloodGlucoseData);
    }

    /**
     * 导出血糖数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:export')")
    @Log(title = "血糖数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(BloodGlucoseData bloodGlucoseData) {
        /*List<BloodGlucoseData> list = bloodGlucoseDataService.selectBloodGlucoseDataList(bloodGlucoseData);
        ExcelUtil<BloodGlucoseData> util = new ExcelUtil<BloodGlucoseData>(BloodGlucoseData.class);
        return util.exportExcel(list, "血糖数据数据");*/
        return null;
    }

    /**
     * 获取血糖数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(bloodGlucoseDataService.selectBloodGlucoseDataById(id));
    }

    /**
     * 新增血糖数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:add')")
    @Log(title = "血糖数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BloodGlucoseData bloodGlucoseData) {
        return toAjax(bloodGlucoseDataService.insertBloodGlucoseData(bloodGlucoseData));
    }

    /**
     * 修改血糖数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:edit')")
    @Log(title = "血糖数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BloodGlucoseData bloodGlucoseData) {
        return toAjax(bloodGlucoseDataService.updateBloodGlucoseData(bloodGlucoseData));
    }

    /**
     * 删除血糖数据
     */
    @PreAuthorize("@ss.hasPermi('system:bloodGlucoseData:remove')")
    @Log(title = "血糖数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(bloodGlucoseDataService.deleteBloodGlucoseDataByIds(ids));
    }
}
