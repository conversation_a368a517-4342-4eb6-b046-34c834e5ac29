package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.ElderlyHealthTestingInformation;
import com.wanou.project.system.service.IElderlyHealthTestingInformationService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 长者健康信息检测Controller
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
@Api(tags = "长者健康信息检测")
@RestController
@RequestMapping("/system/elderlyTestingInformation")
public class ElderlyHealthTestingInformationController extends BaseController {
    @Resource
    private IElderlyHealthTestingInformationService elderlyHealthTestingInformationService;

    /**
     * 查询长者健康信息检测列表
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:list')")
    @GetMapping("/list")
    public TableDataInfo list(ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        startPage();
        List<ElderlyHealthTestingInformation> list = elderlyHealthTestingInformationService.selectElderlyHealthTestingInformationList(elderlyHealthTestingInformation);
        return getDataTable(list);
    }

    /**
     * 导出长者健康信息检测列表
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:export')")
    @Log(title = "长者健康信息检测", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        List<ElderlyHealthTestingInformation> list = elderlyHealthTestingInformationService.selectElderlyHealthTestingInformationList(elderlyHealthTestingInformation);
        ExcelUtil<ElderlyHealthTestingInformation> util = new ExcelUtil<ElderlyHealthTestingInformation>(ElderlyHealthTestingInformation.class);
        return util.exportExcel(list, "长者健康信息检测数据");
    }

    /**
     * 获取长者健康信息检测详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:query')")
    @GetMapping(value = "/{elderId}")
    public AjaxResult getInfo(@PathVariable("elderId") Long elderId) {
        return AjaxResult.success(elderlyHealthTestingInformationService.selectElderlyHealthTestingInformationByElderId(elderId));
    }

    /**
     * 新增长者健康信息检测
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:add')")
    @Log(title = "长者健康信息检测", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        return toAjax(elderlyHealthTestingInformationService.insertElderlyHealthTestingInformation(elderlyHealthTestingInformation));
    }

    /**
     * 修改长者健康信息检测
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:edit')")
    @Log(title = "长者健康信息检测", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        return toAjax(elderlyHealthTestingInformationService.updateElderlyHealthTestingInformation(elderlyHealthTestingInformation));
    }

    /**
     * 删除长者健康信息检测
     */
    @PreAuthorize("@ss.hasPermi('system:elderlyTestingInformation:remove')")
    @Log(title = "长者健康信息检测", businessType = BusinessType.DELETE)
	@DeleteMapping("/{elderIds}")
    public AjaxResult remove(@PathVariable Long[] elderIds) {
        return toAjax(elderlyHealthTestingInformationService.deleteElderlyHealthTestingInformationByElderIds(elderIds));
    }
}
