package com.wanou.project.system.controller;

import java.util.List;

import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.project.system.domain.SleepDetectionPadData;
import com.wanou.project.system.service.ISleepDetectionPadDataService;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.common.utils.poi.ExcelUtil;
import com.wanou.framework.web.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 睡眠检测垫数据Controller
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Api(tags = "睡眠检测垫数据")
@RestController
@RequestMapping("/system/sleepDetectionPadData")
public class SleepDetectionPadDataController extends BaseController {
    @Resource
    private ISleepDetectionPadDataService sleepDetectionPadDataService;

    /**
     * 查询睡眠检测垫数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:list')")
    @GetMapping("/list")
    public TableDataInfo list(SleepDetectionPadData sleepDetectionPadData) {
        startPage();
        List<SleepDetectionPadData> list = sleepDetectionPadDataService.selectSleepDetectionPadDataList(sleepDetectionPadData);
        return getDataTable(list);
    }

    /**
     * 导出睡眠检测垫数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:export')")
    @Log(title = "睡眠检测垫数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SleepDetectionPadData sleepDetectionPadData) {
        List<SleepDetectionPadData> list = sleepDetectionPadDataService.selectSleepDetectionPadDataList(sleepDetectionPadData);
        ExcelUtil<SleepDetectionPadData> util = new ExcelUtil<SleepDetectionPadData>(SleepDetectionPadData.class);
        return util.exportExcel(list, "睡眠检测垫数据数据");
    }

    /**
     * 获取睡眠检测垫数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sleepDetectionPadDataService.selectSleepDetectionPadDataById(id));
    }

    /**
     * 新增睡眠检测垫数据
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:add')")
    @Log(title = "睡眠检测垫数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SleepDetectionPadData sleepDetectionPadData) {
        return toAjax(sleepDetectionPadDataService.insertSleepDetectionPadData(sleepDetectionPadData));
    }

    /**
     * 修改睡眠检测垫数据
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:edit')")
    @Log(title = "睡眠检测垫数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SleepDetectionPadData sleepDetectionPadData) {
        return toAjax(sleepDetectionPadDataService.updateSleepDetectionPadData(sleepDetectionPadData));
    }

    /**
     * 删除睡眠检测垫数据
     */
    @PreAuthorize("@ss.hasPermi('system:sleepDetectionPadData:remove')")
    @Log(title = "睡眠检测垫数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sleepDetectionPadDataService.deleteSleepDetectionPadDataByIds(ids));
    }
}
