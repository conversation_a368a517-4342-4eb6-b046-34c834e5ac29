package com.wanou.project.system.controller;


import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.aspectj.lang.annotation.Log;
import com.wanou.framework.aspectj.lang.enums.BusinessType;
import com.wanou.framework.web.controller.BaseController;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.HomeCareServices;
import com.wanou.project.system.service.IHomeCareServicesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.wanou.common.utils.poi.ExcelUtil;
import java.util.List;

/**
 * <p>
 * 家庭护理服务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Api(tags = "家庭护理服务相关接口")
@RestController
@RequestMapping("miniApi/HomeCareServices")
public class HomeCareServicesController  extends BaseController {
    @Autowired
    private IHomeCareServicesService homeCareProgramService;
    @Autowired
    private IHomeCareServicesService homeCareServicesService;

    /**
     * 通过项目id查询服务列表 *
     * @param id
     * @return
     */
    @ApiOperation(value = "通过项目id查询服务列表")
    @GetMapping("/{id}")
    public TableDataInfo getHomeCareProgramById(@PathVariable String id){
        startPage();
        List<HomeCareServices> list= homeCareProgramService.getPage(id);
        return getDataTable(list);
    }
    @ApiOperation(value = "通过服务id查询服务详情")
    @GetMapping("/detail/{id}")
    public AjaxResult getHomeCareServiceDetail(@PathVariable String id){
        HomeCareServices homeCareServices= homeCareProgramService.getById(id);
        return AjaxResult.success(homeCareServices);
    }


    /**
     * 查询家庭护理服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:services:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeCareServices homeCareServices) {
        startPage();
        List<HomeCareServices> list = homeCareServicesService.selectHomeCareServicesList(homeCareServices);
        return getDataTable(list);
    }

    /**
     * 导出家庭护理服务列表
     */
    @PreAuthorize("@ss.hasPermi('system:services:export')")
    @Log(title = "家庭护理服务", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(HomeCareServices homeCareServices) {
        List<HomeCareServices> list = homeCareServicesService.selectHomeCareServicesList(homeCareServices);
        ExcelUtil<HomeCareServices> util = new ExcelUtil<HomeCareServices>(HomeCareServices.class);
        return util.exportExcel(list, "家庭护理服务数据");
    }

//    /**
//     * 获取家庭护理服务详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('system:services:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(homeCareServicesService.selectHomeCareServicesById(id));
//    }

    /**
     * 新增家庭护理服务
     */
    @PreAuthorize("@ss.hasPermi('system:services:add')")
    @Log(title = "家庭护理服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeCareServices homeCareServices) {
        return toAjax(homeCareServicesService.insertHomeCareServices(homeCareServices));
    }

    /**
     * 修改家庭护理服务
     */
    @PreAuthorize("@ss.hasPermi('system:services:edit')")
    @Log(title = "家庭护理服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeCareServices homeCareServices) {
        return toAjax(homeCareServicesService.updateHomeCareServices(homeCareServices));
    }

    /**
     * 删除家庭护理服务
     */
    @PreAuthorize("@ss.hasPermi('system:services:remove')")
    @Log(title = "家庭护理服务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(homeCareServicesService.deleteHomeCareServicesByIds(ids));
    }
}
