package com.wanou.project.system.service;

import com.wanou.common.api.CommonPage;
import com.wanou.project.system.domain.dto.OrderDetail;
import org.springframework.transaction.annotation.Transactional;

public interface IOrderAppService {
    /**
     * 分页获取用户订单
     */
    CommonPage<OrderDetail> list(Integer status, Integer pageNum, Integer pageSize);

    /**
     * 根据订单ID获取订单详情
     */
    OrderDetail detail(Long orderId);

    /**
     * 取消订单
     */
    @Transactional
    void cancelOrder(Long orderId);

    @Transactional
    Integer paySuccess(Long orderId, Integer payType);

    /**
     * 确认收货
     */
    void confirmReceiveOrder(Long orderId);

    /**
     * 根据订单ID删除订单
     */
    void deleteOrder(Long orderId);

    Long addOrder(OrderDetail orderDetail);
}
