package com.wanou.project.system.service;

import java.util.List;
import com.wanou.project.system.domain.SleepDetectionPadData;

/**
 * 睡眠检测垫数据Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface ISleepDetectionPadDataService {
    /**
     * 查询睡眠检测垫数据
     *
     * @param id 睡眠检测垫数据主键
     * @return 睡眠检测垫数据
     */
    public SleepDetectionPadData selectSleepDetectionPadDataById(Long id);

    /**
     * 查询睡眠检测垫数据列表
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 睡眠检测垫数据集合
     */
    public List<SleepDetectionPadData> selectSleepDetectionPadDataList(SleepDetectionPadData sleepDetectionPadData);

    /**
     * 新增睡眠检测垫数据
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 结果
     */
    public int insertSleepDetectionPadData(SleepDetectionPadData sleepDetectionPadData);

    /**
     * 修改睡眠检测垫数据
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 结果
     */
    public int updateSleepDetectionPadData(SleepDetectionPadData sleepDetectionPadData);

    /**
     * 批量删除睡眠检测垫数据
     *
     * @param ids 需要删除的睡眠检测垫数据主键集合
     * @return 结果
     */
    public int deleteSleepDetectionPadDataByIds(Long[] ids);

    /**
     * 删除睡眠检测垫数据信息
     *
     * @param id 睡眠检测垫数据主键
     * @return 结果
     */
    public int deleteSleepDetectionPadDataById(Long id);
}
