package com.wanou.project.system.service.impl;

import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.web.page.PageDomain;
import com.wanou.project.system.domain.DeviceStore;
import com.wanou.project.system.mapper.DeviceStoreMapper;
import com.wanou.project.system.service.IDeviceStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Service
public class DeviceStoreServiceImpl implements IDeviceStoreService {
    @Autowired
    private DeviceStoreMapper devicestoreMapper;

    @Override
    public List<DeviceStore> queryDevice ( ) {
        List<DeviceStore> list=  devicestoreMapper.listDevice();
        return list;
    }

    @Override
    public DeviceStore queryDeviceById(Long id) {
        DeviceStore devicestore=  devicestoreMapper.getDeviceById(id);
        return devicestore;
    }

    /**
     * 查询设备商城列表
     *
     * @param deviceStore 设备商城
     * @return 设备商城
     */
    @Override
    public List<DeviceStore> selectDeviceStoreList(DeviceStore deviceStore) {
        return devicestoreMapper.selectDeviceStoreList(deviceStore);
    }

    /**
     * 新增设备
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    @Override
    public int insertDeviceStore(DeviceStore deviceStore) {
        deviceStore.setCreatedBy(SecurityUtils.getUsername());
        deviceStore.setUpdatedBy(SecurityUtils.getUsername());
        return devicestoreMapper.insertDeviceStore(deviceStore);
    }

    /**
     * 修改设备
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    @Override
    public int updateDeviceStore(DeviceStore deviceStore) {
        deviceStore.setUpdatedBy(SecurityUtils.getUsername());
        return devicestoreMapper.updateDeviceStore(deviceStore);
    }

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备商城主键
     * @return 结果
     */
    @Override
    public int deleteDeviceStoreByIds(Long[] ids) {
        return devicestoreMapper.deleteDeviceStoreByIds(ids);
    }

    /**
     * 删除设备信息
     *
     * @param id 设备商城主键
     * @return 结果
     */
    @Override
    public int deleteDeviceStoreById(Long id) {
        return devicestoreMapper.deleteDeviceStoreById(id);
    }

    /**
     * @param category
     * @return
     */
    @Override
    public List<DeviceStore> queryDeviceByCategory(String category) {


        return devicestoreMapper.listCategroy(category);
    }
}
