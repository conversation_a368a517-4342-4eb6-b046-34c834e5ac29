package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.LinkUtil;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.IDeviceElderBindingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.BloodGlucoseDataMapper;
import com.wanou.project.system.domain.BloodGlucoseData;
import com.wanou.project.system.service.IBloodGlucoseDataService;
import javax.annotation.Resource;

/**
 * 血糖数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Service
public class BloodGlucoseDataServiceImpl implements IBloodGlucoseDataService {
    @Resource
    private BloodGlucoseDataMapper bloodGlucoseDataMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;

    /**
     * 查询血糖数据
     *
     * @param id 血糖数据主键
     * @return 血糖数据
     */
    @Override
    public BloodGlucoseData selectBloodGlucoseDataById(Long id) {
        return bloodGlucoseDataMapper.selectBloodGlucoseDataById(id);
    }

    /**
     * 查询血糖数据列表
     *
     * @param bloodGlucoseData 血糖数据
     * @return 血糖数据
     */
    @Override
    public TableDataInfo selectBloodGlucoseDataList(BloodGlucoseData bloodGlucoseData) {
        JSONObject params = linkUtil.createParamsStartPage();
        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        deviceElderBindingDto.setElderId(bloodGlucoseData.getElderId());
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        List<Long> deviceIds = deviceElderBindingVos.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        if(CollUtil.isEmpty(deviceIds)){
            return new TableDataInfo(new ArrayList<>(1),0);
        }
        params.put("inDeviceIds",deviceIds);

        if(bloodGlucoseData.getDeviceId() != null){
            //设备筛选
            params.put("deviceId",bloodGlucoseData.getDeviceId());
        }
        if(bloodGlucoseData.getDetectionResults() != null){
            if(bloodGlucoseData.getDetectionResults() == 1){
                //正常
                params.put("alarmFlag",false);
            }else {
                params.put("alarmFlag",true);
            }
        }
        List<Long> queryDeviceTypes = new ArrayList<>();
        queryDeviceTypes.add(8L);
        params.put("deviceTypes",queryDeviceTypes);

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_SENSOR_HISTORY_LIST_URL, TableDataInfo.class, params);
        List<BloodGlucoseData> list = tableDataInfo.getRows().stream().map(item -> {
            JSONObject bean = BeanUtil.toBean(item, JSONObject.class);
            BloodGlucoseData data = new BloodGlucoseData();
            data.setId(bean.getLong("id"));
            deviceElderBindingVos.stream().filter(deviceElderBindingVo -> deviceElderBindingVo.getDeviceId()
                            .equals(bean.getLong("deviceId")))
                    .findFirst().ifPresent(match -> {
                        data.setElderId(match.getElderId());
                        data.setElderPhone(match.getElderPhone());
                        data.setElderName(match.getElderName());
                    });
            data.setGlucose(NumberUtil.toBigDecimal(bean.getString("value")));
            data.setDetectionTime(bean.getDate("reportTime"));
            data.setDetectionResults(!bean.getBoolean("alarmFlag") ? 1L : 2L);
            data.setDeviceName(bean.getString("deviceName"));
            data.setDeviceId(bean.getLong("deviceId"));

            return data;
        }).collect(Collectors.toList());
        tableDataInfo.setRows(list);
        tableDataInfo.setTotal(list.size());
        return tableDataInfo;
    }

    /**
     * 新增血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    @Override
    public int insertBloodGlucoseData(BloodGlucoseData bloodGlucoseData) {
        return bloodGlucoseDataMapper.insertBloodGlucoseData(bloodGlucoseData);
    }

    /**
     * 修改血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    @Override
    public int updateBloodGlucoseData(BloodGlucoseData bloodGlucoseData) {
        return bloodGlucoseDataMapper.updateBloodGlucoseData(bloodGlucoseData);
    }

    /**
     * 批量删除血糖数据
     *
     * @param ids 需要删除的血糖数据主键
     * @return 结果
     */
    @Override
    public int deleteBloodGlucoseDataByIds(Long[] ids) {
        return bloodGlucoseDataMapper.deleteBloodGlucoseDataByIds(ids);
    }

    /**
     * 删除血糖数据信息
     *
     * @param id 血糖数据主键
     * @return 结果
     */
    @Override
    public int deleteBloodGlucoseDataById(Long id) {
        return bloodGlucoseDataMapper.deleteBloodGlucoseDataById(id);
    }
}
