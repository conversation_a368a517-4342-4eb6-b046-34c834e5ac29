package com.wanou.project.system.service;

import java.util.List;
import com.wanou.project.system.domain.Comments;

/**
 * 商品评价Service接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface ICommentsService {
    /**
     * 查询商品评价
     *
     * @param id 商品评价主键
     * @return 商品评价
     */
    public Comments selectCommentsById(Long id);

    /**
     * 查询商品评价子评论
     *
     * @param id 商品评价主键
     * @return 商品评价
     */
    //public List<Comments> getCommentsTreeById(Long id);

    /**
     * 查询商品评价列表小程序
     *
     * @param comments 商品评价
     * @return 商品评价集合
     */
    public List<Comments> selectCommentsListApp(Comments comments);

    /**
     * 查询商品评价列表
     *
     * @param comments 商品评价
     * @return 商品评价集合
     */
    public List<Comments> selectCommentsList(Comments comments);

    /**
     * 新增商品评价
     *
     * @param comments 商品评价
     * @return 结果
     */
    public int insertComments(Comments comments);

    /**
     * 修改商品评价
     *
     * @param comments 商品评价
     * @return 结果
     */
    public int updateComments(Comments comments);

    /**
     * 批量删除商品评价
     *
     * @param ids 需要删除的商品评价主键集合
     * @return 结果
     */
    public int deleteCommentsByIds(Long[] ids);

    /**
     * 删除商品评价信息
     *
     * @param id 商品评价主键
     * @return 结果
     */
    public int deleteCommentsById(Long id);
}
