package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.exception.CustomException;
import com.wanou.common.utils.DateUtils;
import com.wanou.common.utils.LinkUtil;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.config.properties.LinkProperties;
import com.wanou.framework.security.LoginUser;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.PageDomain;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.framework.web.page.TableSupport;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.EquipmentAlarmInformation;
import com.wanou.project.system.domain.SysDictData;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.domain.vo.DeviceVo;
import com.wanou.project.system.mapper.DeviceElderBindingMapper;
import com.wanou.project.system.mapper.SysDictDataMapper;
import com.wanou.project.system.service.IDeviceElderBindingService;
import com.wanou.project.system.service.IEquipmentAlarmInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.DeviceMapper;
import com.wanou.project.system.domain.Device;
import com.wanou.project.system.service.IDeviceService;
import javax.annotation.Resource;

/**
 * 设备基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
@Service
public class DeviceServiceImpl implements IDeviceService {
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private SysDictDataMapper sysDictDataMapper;
    @Resource
    private DeviceElderBindingMapper deviceElderBindingMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;
    @Resource
    private IEquipmentAlarmInformationService equipmentAlarmInformationService;



    /**
     * 查询设备基本信息
     *
     * @param id 设备基本信息主键
     * @return 设备基本信息
     */
    @Override
    public AjaxResult selectDeviceById(Long id) {
        return linkUtil.getLinkDataDoGet(StrUtil.format("{}/{}",Constants.LINK_GET_DEVICE_URL,id), AjaxResult.class,null);
    }

    /**
     * 查询设备基本信息列表
     *
     * @param device 设备基本信息
     * @return 设备基本信息
     */
    @Override
    public TableDataInfo selectDeviceList(Device device) {
        //从物联网平台获取设备列表
        JSONObject deviceParams = linkUtil.createParamsStartPage();
        //筛选
        DeviceElderBindingDto deviceElderBindingParam = new DeviceElderBindingDto();
        //长者筛选
        deviceElderBindingParam.setElderId(device.getElderId());
        List<DeviceElderBindingVo> deviceElderBindingList = deviceElderBindingService.list(deviceElderBindingParam);
        if(CollUtil.isNotEmpty(deviceElderBindingList)){
            if((device.getBindingStatus() != null && device.getBindingStatus() == 1) || device.getElderId() != null){
                deviceParams.put("inIds",deviceElderBindingList.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList()));
            }
            if(device.getBindingStatus() != null && device.getBindingStatus() == 2){
                deviceParams.put("notInIds",deviceElderBindingList.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList()));
            }
        }else {
            //无任何绑定设备 不查询
            if((device.getBindingStatus() != null && device.getBindingStatus() == 1) || device.getElderId() != null){
                return new TableDataInfo(new ArrayList<>(1),0);
            }
        }

        /*if(device.getBindingStatus() != null && device.getBindingStatus() == 2){
            //筛选未绑定设备
            DeviceElderBindingDto deviceElderBindingParam = new DeviceElderBindingDto();
            List<DeviceElderBindingVo> deviceElderBindingList = deviceElderBindingService.list(deviceElderBindingParam);
            if(CollUtil.isNotEmpty(deviceElderBindingList)){
                deviceParams.put("notInIds",deviceElderBindingList.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList()));
            }
        }*/
        deviceParams.put("deviceName", device.getDeviceName());

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_DEVICE_LIST_URL, TableDataInfo.class,deviceParams);
        if(CollUtil.isNotEmpty(tableDataInfo.getRows())){
            //查询设备绑定信息
            List<JSONObject> devices = (List<JSONObject>) tableDataInfo.getRows();
            //List<Long> ids = devices.stream().map(item -> item.getLong("id")).collect(Collectors.toList());
            //List<DeviceElderBindingVo> deviceElderBindingList = deviceElderBindingService.selectDeviceElderBindingListByIds(ids);
            if(CollUtil.isNotEmpty(deviceElderBindingList)){
                devices.forEach(deviceItem -> {
                    DeviceElderBindingVo matchDeviceElderBinding = deviceElderBindingList.stream().filter(deviceElderBindingItem -> deviceItem.getLong("id").equals(deviceElderBindingItem.getDeviceId()))
                            .findFirst().orElse(null);
                    if(matchDeviceElderBinding != null){
                        deviceItem.put("elderName", matchDeviceElderBinding.getElderName());
                        deviceItem.put("bindingStatus",1);
                        deviceItem.put("bindingTime", DateUtil.format(matchDeviceElderBinding.getBindingTime(),"yyyy-MM-dd HH:mm:ss"));
                    }else {
                        deviceItem.put("bindingStatus",2);
                    }
                });
            }
        }
        return tableDataInfo;
    }

    /**
     * 新增设备基本信息
     *
     * @param device 设备基本信息
     * @return 结果
     */
    @Override
    public int insertDevice(Device device) {
        if (CollectionUtil.isNotEmpty(device.getAlarmPushTypeValue())){
            String alarmPush = "";
            for (int i = 0; i < device.getAlarmPushTypeValue().size(); i++) {
                if (StrUtil.isBlank(alarmPush)){
                    alarmPush = StrUtil.toString(device.getAlarmPushTypeValue().get(i));
                }else {
                    alarmPush = alarmPush+"," + device.getAlarmPushTypeValue().get(i);
                }
            }
            device.setAlarmPushType(alarmPush);
        }
        if (device.getElderId() != null){
            device.setBindingStatus(1l);
        }
        device.setCreateTime(DateUtils.getNowDate());
        device.setCreatePerson(SecurityUtils.getUsername());
        return deviceMapper.insertDevice(device);
    }

    /**
     * 修改设备基本信息
     *
     * @param device 设备基本信息
     * @return 结果
     */
    @Override
    public int bindingDevice(Device device) {
        int i = 0;
        if (device != null){
            if (device.getElderId() == null){
                //如果绑定长者id为空，则删除当前设备的绑定信息
                i = deviceElderBindingMapper.deleteDeviceElderBindingByDeviceId(device.getId());
            }else {
                //根据设备id查询绑定表中是否有该设备，如果有则更新绑定，没有则新增绑定表
                DeviceElderBinding deviceElderBindingByDeviceId = deviceElderBindingMapper.selectDeviceElderBindingByDeviceId(device.getId());
                if (deviceElderBindingByDeviceId != null){
                    deviceElderBindingByDeviceId.setDeviceId(device.getId());
                    deviceElderBindingByDeviceId.setBindingTime(DateUtils.getNowDate());
                    deviceElderBindingByDeviceId.setCreateBy(SecurityUtils.getUsername());
                    i = deviceElderBindingMapper.updateDeviceElderBinding(deviceElderBindingByDeviceId);
                }else {
                    DeviceElderBinding deviceElderBinding = new DeviceElderBinding();
                    deviceElderBinding.setElderId(device.getElderId());
                    deviceElderBinding.setDeviceId(device.getId());
                    deviceElderBinding.setBindingTime(DateUtils.getNowDate());
                    deviceElderBinding.setCreateBy(SecurityUtils.getUsername());
                    i = deviceElderBindingMapper.insertDeviceElderBinding(deviceElderBinding);
                }
            }
        }
        return i;
    }

    /**
     * 批量删除设备基本信息
     *
     * @param ids 需要删除的设备基本信息主键
     * @return 结果
     */
    @Override
    public int deleteDeviceByIds(Long[] ids) {
        return deviceMapper.deleteDeviceByIds(ids);
    }

    /**
     * 删除设备基本信息信息
     *
     * @param id 设备基本信息主键
     * @return 结果
     */
    @Override
    public int deleteDeviceById(Long id) {
        return deviceMapper.deleteDeviceById(id);
    }

    /**
     * 绑定长者
     * @param device
     */
    @Override
    public void bindElder(Device device) {
        DeviceElderBinding deviceElderBinding = new DeviceElderBinding();
        deviceElderBinding.setDeviceId(device.getId());
        deviceElderBinding.setElderId(device.getElderId());
        deviceElderBinding.setCreateBy(SecurityUtils.getUsername());
        deviceElderBinding.setBindingTime(DateUtil.date());
        deviceElderBindingService.bind(deviceElderBinding);
    }

    /**
     * 查询设备统计
     * @return
     */
    @Override
    public JSONObject deviceStatistics() {
        JSONObject result = new JSONObject(); //返回结果
        result.put("deviceTotal",0); //设备总数
        result.put("totalBinding", 0); //总绑定数
        result.put("unbinding", 0); //未绑定数
        result.put("todayBinding", 0); //今日绑定数
        result.put("todayAlarm", 0); //今日报警数

        //查询设备列表
        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_DEVICE_LIST_URL, TableDataInfo.class,null);
        if(tableDataInfo != null && CollUtil.isNotEmpty(tableDataInfo.getRows())){
            result.put("deviceTotal",tableDataInfo.getTotal()); //设备总数

            List<Long> deviceIds = tableDataInfo.getRows().stream().map(deviceItem -> {
                JSONObject deviceBean = BeanUtil.toBean(deviceItem, JSONObject.class);
                return deviceBean.getLong("id");
            }).collect(Collectors.toList());

            //查询已绑定设备
            DeviceElderBindingDto deviceElderBindingQuery = new DeviceElderBindingDto();
            deviceElderBindingQuery.setDeptId(SecurityUtils.getDeptId());
            List<DeviceElderBindingVo> bindingVoList = deviceElderBindingService.list(deviceElderBindingQuery);
            List<DeviceElderBindingVo> deviceElderBindingList = bindingVoList.stream().filter(bindingItem -> deviceIds.contains(bindingItem.getDeviceId())).collect(Collectors.toList());

            result.put("totalBinding", deviceElderBindingList.size()); //总绑定数
            result.put("unbinding", result.getLong("deviceTotal") - result.getLong("totalBinding")); //未绑定数

            DateTime nowDate = DateUtil.date();
            long todayBinding = deviceElderBindingList.stream().filter(bindItem -> DateUtil.isSameDay(bindItem.getBindingTime(), nowDate))
                    .count();
            result.put("todayBinding", todayBinding); //今日绑定数

            //查询今日报警数
            EquipmentAlarmInformation alarmQuery = new EquipmentAlarmInformation();
            alarmQuery.setStartTime(DateUtil.beginOfDay(nowDate));
            alarmQuery.setEndTime(DateUtil.endOfDay(nowDate));
            TableDataInfo alarmTableDataInfo = equipmentAlarmInformationService.selectEquipmentAlarmInformationList(alarmQuery);
            if(alarmTableDataInfo != null && CollUtil.isNotEmpty(alarmTableDataInfo.getRows())){
                result.put("todayAlarm", alarmTableDataInfo.getTotal());
            }

            //设备类型统计
            List<JSONObject> rowJSONBeans = tableDataInfo.getRows().stream().map(rowItem -> BeanUtil.toBean(rowItem, JSONObject.class)).collect(Collectors.toList());
            Map<String,List<JSONObject>> deviceMap = new HashMap<>();
            rowJSONBeans.forEach(rowItem -> {
                String deviceTypeName = rowItem.getString("deviceTypeName");
                List<JSONObject> itemList = deviceMap.get(deviceTypeName);
                if(itemList == null){
                    itemList = new ArrayList<>();
                }
                itemList.add(rowItem);
                deviceMap.put(deviceTypeName, itemList);
            });
            List<List<JSONObject>> groupByField = new ArrayList<>(deviceMap.values());
            List<JSONObject> deviceTypeStatistics = groupByField.stream().map(groupArrItem -> {
                Object o = groupArrItem.get(0);
                JSONObject bean = BeanUtil.toBean(o, JSONObject.class);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", bean.getString("deviceTypeName"));
                jsonObject.put("value", groupArrItem.size());
                return jsonObject;
            }).collect(Collectors.toList());
            result.put("deviceTypeStatistics", deviceTypeStatistics);

            //近7天绑定数趋势
            List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.offsetDay(nowDate, -6), nowDate, DateField.DAY_OF_MONTH);
            DeviceElderBindingDto newBindTrendQuery = new DeviceElderBindingDto();
            newBindTrendQuery.setQueryStartTime(DateUtil.beginOfDay(dateTimes.get(0)));
            newBindTrendQuery.setQueryEndTime(DateUtil.endOfDay(dateTimes.get(dateTimes.size()-1)));
            List<DeviceElderBindingVo> newBindList = deviceElderBindingService.list(newBindTrendQuery);
            JSONObject newBindingData = new JSONObject(); //近日新增绑定趋势数据
            List<String> labels = new ArrayList<>(dateTimes.size());
            List<Long> values = new ArrayList<>(dateTimes.size());
            dateTimes.forEach(dateItem -> {
                labels.add(DateUtil.format(dateItem,"yyyy-MM-dd"));
                long matchCount = newBindList.stream().filter(newBindItem -> DateUtil.isSameDay(newBindItem.getBindingTime(), dateItem))
                        .count();
                values.add(matchCount);
            });
            newBindingData.put("labels",labels);
            newBindingData.put("values",values);
            result.put("newBindingData", newBindingData);
        }

        return result;
    }

    @Override
    public JSONObject getDeviceTotal() {
        JSONObject result = new JSONObject();
        TableDataInfo tableDataInfo = selectDeviceList(new Device());
        if(tableDataInfo != null && CollUtil.isNotEmpty(tableDataInfo.getRows())){
            List<?> tableDataInfoRows = tableDataInfo.getRows();
            List<JSONObject> devices = (List<JSONObject>) tableDataInfoRows;
            //拿到devices中已绑定的设备
            List<JSONObject> bindingDevices = devices.stream().filter(item -> item.getInteger("bindingStatus") == 1)
                    .collect(Collectors.toList());
            //进行分组
            Map<Integer,Integer> deviceTypeCount = new HashMap<>();
            bindingDevices.forEach(item -> {
                Integer deviceType = item.getInteger("deviceType");
                Integer count = deviceTypeCount.get(deviceType);
                if (count == null){
                    count = 0;
                }
                deviceTypeCount.put(deviceType,count + 1);
            });
            result.put("deviceTotalList",deviceTypeCount);
            //拿到绑定时间为昨日的所有设备
            DateTime yesterday = DateUtil.offsetDay(DateUtil.date(), -1);
            List<JSONObject> yesterdayBindingDevices = bindingDevices.stream().filter(item -> DateUtil.isSameDay(item.getDate("bindingTime"),yesterday))
                    .collect(Collectors.toList());
            //将yesterdayBindingDevices进行分组
            Map<Integer,Integer> yesterdayBindingCount = new HashMap<>();
            yesterdayBindingDevices.forEach(item -> {
                Integer deviceType = item.getInteger("deviceType");
                Integer count = yesterdayBindingCount.get(deviceType);
                if (count == null){
                    count = 0;
                }
                yesterdayBindingCount.put(deviceType,count + 1);
            });
            result.put("yesterdayDeviceBinding",yesterdayBindingCount);
            /*devices.forEach(item -> {
                Integer bindingStatus = item.getInteger("bindingStatus");
                Integer deviceType = item.getInteger("deviceType");
                if (bindingStatus == 1 && deviceType == 1){
                    //燃气设备总数
                    result.put("gasDeviceTotal",result.getLong("gasDeviceTotal") + 1);
                    //昨日燃气设备绑定数
                    DateTime yesterday = DateUtil.offsetDay(DateUtil.date(), -1);
                    yesterday = DateUtil.parse(DateUtil.format(yesterday, "yyyy-MM-dd"), "yyyy-MM-dd");
                    DateTime parse = DateUtil.parse(item.getString("bindingTime"), "yyyy-MM-dd");
                    if (yesterday == parse){
                        result.put("yesterdayGasDeviceBinding",result.getLong("yesterdayGasDeviceBinding") + 1);
                    }
                }
            });*/
        }
        return result;
    }

    /**
     * 更新设备绑定信息
     * @param deviceElderBinding
     */
    @Override
    public void updateElderDeviceBind(DeviceElderBindingDto deviceElderBinding) {
        if(deviceElderBinding.getElderId() == null || deviceElderBinding.getDeviceId() == null){
            throw new CustomException("参数不能为空");
        }
        //查询绑定信息
        DeviceElderBindingVo elderBindingVo = deviceElderBindingService.selectDeviceElderBinding(deviceElderBinding.getDeviceId());
        if(elderBindingVo == null){
            DateTime nowDate = DateUtil.date();
            deviceElderBinding.setUpdateTime(nowDate);
            deviceElderBinding.setUpdateBy(SecurityUtils.getUsername());
            deviceElderBinding.setBindingTime(nowDate);
            deviceElderBinding.setCreateBy(SecurityUtils.getUsername());
            deviceElderBindingService.bind(deviceElderBinding);
        }else {
            DateTime nowDate = DateUtil.date();
            deviceElderBinding.setUpdateTime(nowDate);
            deviceElderBinding.setUpdateBy(SecurityUtils.getUsername());
            deviceElderBinding.setBindingTime(nowDate);
            deviceElderBinding.setId(elderBindingVo.getId());
            deviceElderBindingService.update(deviceElderBinding);
        }

    }

    @Override
    public TableDataInfo selectDeviceByElderId(Integer elderId) {
        //通过老人id查询到设备id
        List<DeviceElderBinding> deviceElderBinding = deviceElderBindingMapper.selectDeviceElderBindingByElderId(elderId);
        //获得里面的设备id集合
        List<Long> deviceIds = deviceElderBinding.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        TableDataInfo tableDataInfo1 = selectDeviceList(new Device());
        List<?> rows =(List<JSONObject>) tableDataInfo1.getRows();
        //将rows集合转成Device集合
        List<DeviceVo> devices1 = BeanUtil.copyToList(rows, DeviceVo.class);
        //获得devices1中包含设备id是deviceIds的设备
        List<DeviceVo> devices = devices1.stream().filter(item -> deviceIds.contains(item.getId())).collect(Collectors.toList());
        //封装数据到TableDataInfo中
        TableDataInfo tableDataInfo = new TableDataInfo(devices, devices.size());
        return tableDataInfo;
    }

    @Override
    public void unBindElder(UnBingDto unBingDto) {
        //根据老人的id和设备id进行解绑操作
        deviceElderBindingService.unbind(unBingDto);


    }

    @Override
    public TableDataInfo getBelongDevice(String elderId) {

         //直接通过老人id查询到设备id
        //从物联网平台获取设备列表
        JSONObject deviceParams = linkUtil.createParamsStartPage();
        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_DEVICE_LIST_URL, TableDataInfo.class,deviceParams);
        List<JSONObject> rows =(List<JSONObject>) tableDataInfo.getRows();
        //将rows集合转成Device集合
        List<DeviceVo> devices1 = BeanUtil.copyToList(rows, DeviceVo.class);
        //找出devices1集合中belong和elderId相等的设备
        List<DeviceVo> devices = devices1.stream().filter(item -> ObjectUtil.isNotEmpty(item.getBelong()) &&item.getBelong().equals(elderId)).collect(Collectors.toList());
        //查询老人已经穿戴的设备从devices中去除
        List<DeviceElderBinding> deviceElderBinding = deviceElderBindingMapper.selectDeviceElderBindingByElderId(Integer.valueOf(elderId));
        List<Long> deviceIds = deviceElderBinding.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        devices = devices.stream().filter(item -> !deviceIds.contains(item.getId())).collect(Collectors.toList());
        //封装数据到TableDataInfo中
        TableDataInfo tableDataInfo1 = new TableDataInfo(devices, devices.size());
        return tableDataInfo1;
    }
}
