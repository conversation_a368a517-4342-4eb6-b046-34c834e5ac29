package com.wanou.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.SleepDetectionPadDataMapper;
import com.wanou.project.system.domain.SleepDetectionPadData;
import com.wanou.project.system.service.ISleepDetectionPadDataService;
import javax.annotation.Resource;

/**
 * 睡眠检测垫数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class SleepDetectionPadDataServiceImpl implements ISleepDetectionPadDataService {
    @Resource
    private SleepDetectionPadDataMapper sleepDetectionPadDataMapper;

    /**
     * 查询睡眠检测垫数据
     *
     * @param id 睡眠检测垫数据主键
     * @return 睡眠检测垫数据
     */
    @Override
    public SleepDetectionPadData selectSleepDetectionPadDataById(Long id) {
        return sleepDetectionPadDataMapper.selectSleepDetectionPadDataById(id);
    }

    /**
     * 查询睡眠检测垫数据列表
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 睡眠检测垫数据
     */
    @Override
    public List<SleepDetectionPadData> selectSleepDetectionPadDataList(SleepDetectionPadData sleepDetectionPadData) {
        return sleepDetectionPadDataMapper.selectSleepDetectionPadDataList(sleepDetectionPadData);
    }

    /**
     * 新增睡眠检测垫数据
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 结果
     */
    @Override
    public int insertSleepDetectionPadData(SleepDetectionPadData sleepDetectionPadData) {
        return sleepDetectionPadDataMapper.insertSleepDetectionPadData(sleepDetectionPadData);
    }

    /**
     * 修改睡眠检测垫数据
     *
     * @param sleepDetectionPadData 睡眠检测垫数据
     * @return 结果
     */
    @Override
    public int updateSleepDetectionPadData(SleepDetectionPadData sleepDetectionPadData) {
        return sleepDetectionPadDataMapper.updateSleepDetectionPadData(sleepDetectionPadData);
    }

    /**
     * 批量删除睡眠检测垫数据
     *
     * @param ids 需要删除的睡眠检测垫数据主键
     * @return 结果
     */
    @Override
    public int deleteSleepDetectionPadDataByIds(Long[] ids) {
        return sleepDetectionPadDataMapper.deleteSleepDetectionPadDataByIds(ids);
    }

    /**
     * 删除睡眠检测垫数据信息
     *
     * @param id 睡眠检测垫数据主键
     * @return 结果
     */
    @Override
    public int deleteSleepDetectionPadDataById(Long id) {
        return sleepDetectionPadDataMapper.deleteSleepDetectionPadDataById(id);
    }
}
