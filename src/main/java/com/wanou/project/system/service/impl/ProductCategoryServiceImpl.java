package com.wanou.project.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.project.system.domain.ProductCategory;
import com.wanou.project.system.mapper.ProductCategoryMapper;
import com.wanou.project.system.service.IProductCategoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 商品分类管理Service实现类
 * Created by djf on 2025/2/21
 */
@Service
public class ProductCategoryServiceImpl implements IProductCategoryService {
    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Override
    public List<ProductCategory> getProductCategoryListApp(ProductCategory productCategory) {
        return productCategoryMapper.selectProductCategoryListApp(productCategory);
    }

    @Override
    public List<ProductCategory> getList(Long parentId, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        ProductCategory productCategory = new ProductCategory();
        productCategory.setParentId(parentId);
        return productCategoryMapper.getList(productCategory);
    }

    @Override
    public ProductCategory getItem(Long id) {
        return productCategoryMapper.selectById(id);
    }

    @Override
    public int insertProductCategory(ProductCategory productCategory) {
        productCategory.setCreateBy(SecurityUtils.getUsername());
        productCategory.setUpdateBy(SecurityUtils.getUsername());
        //没有父分类时为一级分类
        setCategoryLevel(productCategory);
        int count = productCategoryMapper.insertProductCategory(productCategory);
        return count;
    }

    /**
     * 根据分类的parentId设置分类的级别
     */
    private void setCategoryLevel(ProductCategory productCategory) {
        //没有父分类时为一级分类
        if (productCategory.getParentId() == 0) {
            productCategory.setCategoryLevel(0);
        } else {
            //有父分类时选择根据父分类level设置
            ProductCategory parentCategory = productCategoryMapper.selectById(productCategory.getParentId());
            if (parentCategory != null) {
                productCategory.setCategoryLevel(parentCategory.getCategoryLevel() + 1);
            } else {
                productCategory.setCategoryLevel(0);
            }
        }
    }

    @Override
    public int updateProductCategory(Long id, ProductCategory productCategory) {
        productCategory.setId(id);
        productCategory.setUpdateBy(SecurityUtils.getUsername());
        setCategoryLevel(productCategory);
        //更新商品分类时要更新商品中的名称

        return productCategoryMapper.updateProductCategory(productCategory);
    }

    @Override
    public int deleteProductCategory(Long id) {
        return productCategoryMapper.deleteProductCategory(id);
    }

    @Override
    public int updateIsAvailable(List<Long> ids, Integer isAvailable) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("ID列表不能为空");
        }
        return productCategoryMapper.updateIsAvailable(ids,isAvailable);
    }
}
