package com.wanou.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.UricWarningMapper;
import com.wanou.project.system.domain.UricWarning;
import com.wanou.project.system.service.IUricWarningService;
import javax.annotation.Resource;

/**
 * 尿酸预警Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Service
public class UricWarningServiceImpl implements IUricWarningService {
    @Resource
    private UricWarningMapper uricWarningMapper;

    /**
     * 查询尿酸预警
     *
     * @param id 尿酸预警主键
     * @return 尿酸预警
     */
    @Override
    public UricWarning selectUricWarningById(Long id) {
        return uricWarningMapper.selectUricWarningById(id);
    }

    /**
     * 查询尿酸预警列表
     *
     * @param uricWarning 尿酸预警
     * @return 尿酸预警
     */
    @Override
    public List<UricWarning> selectUricWarningList(UricWarning uricWarning) {
        return uricWarningMapper.selectUricWarningList(uricWarning);
    }

    /**
     * 新增尿酸预警
     *
     * @param uricWarning 尿酸预警
     * @return 结果
     */
    @Override
    public int insertUricWarning(UricWarning uricWarning) {
        return uricWarningMapper.insertUricWarning(uricWarning);
    }

    /**
     * 修改尿酸预警
     *
     * @param uricWarning 尿酸预警
     * @return 结果
     */
    @Override
    public int updateUricWarning(UricWarning uricWarning) {
        return uricWarningMapper.updateUricWarning(uricWarning);
    }

    /**
     * 批量删除尿酸预警
     *
     * @param ids 需要删除的尿酸预警主键
     * @return 结果
     */
    @Override
    public int deleteUricWarningByIds(Long[] ids) {
        return uricWarningMapper.deleteUricWarningByIds(ids);
    }

    /**
     * 删除尿酸预警信息
     *
     * @param id 尿酸预警主键
     * @return 结果
     */
    @Override
    public int deleteUricWarningById(Long id) {
        return uricWarningMapper.deleteUricWarningById(id);
    }
}
