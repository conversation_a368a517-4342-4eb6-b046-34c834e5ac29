package com.wanou.project.system.service;

import java.util.List;
import com.wanou.project.system.domain.ElderFamilyMembers;

/**
 * 长者家属信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface IElderFamilyMembersService {
    /**
     * 查询长者家属信息
     *
     * @param id 长者家属信息主键
     * @return 长者家属信息
     */
    public ElderFamilyMembers selectElderFamilyMembersById(Long id);

    /**
     * 查询长者家属信息列表
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 长者家属信息集合
     */
    public List<ElderFamilyMembers> selectElderFamilyMembersList(ElderFamilyMembers elderFamilyMembers);

    /**
     * 新增长者家属信息
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 结果
     */
    public int insertElderFamilyMembers(ElderFamilyMembers elderFamilyMembers);

    /**
     * 修改长者家属信息
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 结果
     */
    public int updateElderFamilyMembers(ElderFamilyMembers elderFamilyMembers);

    /**
     * 批量删除长者家属信息
     *
     * @param ids 需要删除的长者家属信息主键集合
     * @return 结果
     */
    public int deleteElderFamilyMembersByIds(Long[] ids);

    /**
     * 删除长者家属信息信息
     *
     * @param id 长者家属信息主键
     * @return 结果
     */
    public int deleteElderFamilyMembersById(Long id);
}
