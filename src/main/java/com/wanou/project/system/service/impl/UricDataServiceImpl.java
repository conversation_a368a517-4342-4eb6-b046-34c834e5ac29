package com.wanou.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.UricDataMapper;
import com.wanou.project.system.domain.UricData;
import com.wanou.project.system.service.IUricDataService;
import javax.annotation.Resource;

/**
 * 尿酸数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class UricDataServiceImpl implements IUricDataService {
    @Resource
    private UricDataMapper uricDataMapper;

    /**
     * 查询尿酸数据
     *
     * @param id 尿酸数据主键
     * @return 尿酸数据
     */
    @Override
    public UricData selectUricDataById(Long id) {
        return uricDataMapper.selectUricDataById(id);
    }

    /**
     * 查询尿酸数据列表
     *
     * @param uricData 尿酸数据
     * @return 尿酸数据
     */
    @Override
    public List<UricData> selectUricDataList(UricData uricData) {
        return uricDataMapper.selectUricDataList(uricData);
    }

    /**
     * 新增尿酸数据
     *
     * @param uricData 尿酸数据
     * @return 结果
     */
    @Override
    public int insertUricData(UricData uricData) {
        return uricDataMapper.insertUricData(uricData);
    }

    /**
     * 修改尿酸数据
     *
     * @param uricData 尿酸数据
     * @return 结果
     */
    @Override
    public int updateUricData(UricData uricData) {
        return uricDataMapper.updateUricData(uricData);
    }

    /**
     * 批量删除尿酸数据
     *
     * @param ids 需要删除的尿酸数据主键
     * @return 结果
     */
    @Override
    public int deleteUricDataByIds(Long[] ids) {
        return uricDataMapper.deleteUricDataByIds(ids);
    }

    /**
     * 删除尿酸数据信息
     *
     * @param id 尿酸数据主键
     * @return 结果
     */
    @Override
    public int deleteUricDataById(Long id) {
        return uricDataMapper.deleteUricDataById(id);
    }
}
