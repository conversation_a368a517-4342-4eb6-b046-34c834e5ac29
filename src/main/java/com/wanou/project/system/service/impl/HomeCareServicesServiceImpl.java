package com.wanou.project.system.service.impl;

import com.wanou.common.utils.SecurityUtils;
import com.wanou.project.system.domain.HomeCareServices;
import com.wanou.project.system.mapper.HomeCareServicesMapper;
import com.wanou.project.system.service.IHomeCareServicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 家庭护理服务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Service
public class HomeCareServicesServiceImpl  implements IHomeCareServicesService {
   @Autowired
    private HomeCareServicesMapper homeCareServicesMapper;
    /**
     * 通过项目id查询服务列表*
     *
     * @param id
     * @return
     */
    @Override
    public List<HomeCareServices> getPage(String id) {
        //通过项目id查询所有的服务列表
        List<HomeCareServices> serviceByProjectId = homeCareServicesMapper.getServiceByProjectId(id);
        return serviceByProjectId;

    }

    /**
     * * 通过id查询服务*
     *
     * @param id
     * @return
     */
    @Override
    public HomeCareServices getById(String id) {
        //通过id查询服务
        HomeCareServices serviceById = homeCareServicesMapper.getServiceById(id);
        return serviceById;
    }

    /**
     * 查询家庭护理服务
     *
     * @param id 家庭护理服务主键
     * @return 家庭护理服务
     */
    @Override
    public HomeCareServices selectHomeCareServicesById(Long id) {
        return homeCareServicesMapper.selectHomeCareServicesById(id);
    }

    /**
     * 查询家庭护理服务列表
     *
     * @param homeCareServices 家庭护理服务
     * @return 家庭护理服务
     */
    @Override
    public List<HomeCareServices> selectHomeCareServicesList(HomeCareServices homeCareServices) {
        return homeCareServicesMapper.selectHomeCareServicesList(homeCareServices);
    }

    /**
     * 新增家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    @Override
    public int insertHomeCareServices(HomeCareServices homeCareServices) {
        homeCareServices.setCreateBy(SecurityUtils.getUsername());
        homeCareServices.setUpdateBy(SecurityUtils.getUsername());
        return homeCareServicesMapper.insertHomeCareServices(homeCareServices);
    }

    /**
     * 修改家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    @Override
    public int updateHomeCareServices(HomeCareServices homeCareServices) {
        homeCareServices.setUpdateBy(SecurityUtils.getUsername());
        return homeCareServicesMapper.updateHomeCareServices(homeCareServices);
    }

    /**
     * 批量删除家庭护理服务
     *
     * @param ids 需要删除的家庭护理服务主键
     * @return 结果
     */
    @Override
    public int deleteHomeCareServicesByIds(Long[] ids) {
        return homeCareServicesMapper.deleteHomeCareServicesByIds(ids);
    }

    /**
     * 删除家庭护理服务信息
     *
     * @param id 家庭护理服务主键
     * @return 结果
     */
    @Override
    public int deleteHomeCareServicesById(Long id) {
        return homeCareServicesMapper.deleteHomeCareServicesById(id);
    }
}
