package com.wanou.project.system.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.project.system.domain.ElderBaseInfo;

/**
 * 长者基本信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IElderBaseInfoService
{
    /**
     * 查询长者基本信息
     *
     * @param id 长者基本信息主键
     * @return 长者基本信息
     */
    public ElderBaseInfo selectElderBaseInfoById(Long id);

    /**
     * 查询长者基本信息列表
     *
     * @param elderBaseInfo 长者基本信息
     * @return 长者基本信息集合
     */
    public List<ElderBaseInfo> selectElderBaseInfoList(ElderBaseInfo elderBaseInfo);

    /**
     * 新增长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    public int insertElderBaseInfo(ElderBaseInfo elderBaseInfo);

    /**
     * 修改长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    public int updateElderBaseInfo(ElderBaseInfo elderBaseInfo);

    /**
     * 批量删除长者基本信息
     *
     * @param ids 需要删除的长者基本信息主键集合
     * @return 结果
     */
    public int deleteElderBaseInfoByIds(Long[] ids);

    /**
     * 删除长者基本信息信息
     *
     * @param id 长者基本信息主键
     * @return 结果
     */
    public int deleteElderBaseInfoById(Long id);

    AjaxResult totalElderData();

    JSONObject dataViewStatistics(Long deptId);
}
