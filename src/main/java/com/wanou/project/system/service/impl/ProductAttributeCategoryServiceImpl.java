package com.wanou.project.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.wanou.project.system.domain.ProductAttributeCategory;
import com.wanou.project.system.mapper.ProductAttributeCategoryMapper;
import com.wanou.project.system.service.IProductAttributeCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品属性分类管理Service实现类
 * Created by djf on 2025/3/5
 */
@Service
public class ProductAttributeCategoryServiceImpl implements IProductAttributeCategoryService {
    @Autowired
    private ProductAttributeCategoryMapper productAttributeCategoryMapper;
//    @Autowired
//    private ProductAttributeCategoryDao productAttributeCategoryDao;

    @Override
    public ProductAttributeCategory getItem(Long id) {
        return productAttributeCategoryMapper.selectById(id);
    }

    @Override
    public List<ProductAttributeCategory> getList(Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum,pageSize);
        return productAttributeCategoryMapper.selectProductAttributeCategory(new ProductAttributeCategory());
    }

    @Override
    public int create(String name) {
        ProductAttributeCategory productAttributeCategory = new ProductAttributeCategory();
        productAttributeCategory.setName(name);
        return productAttributeCategoryMapper.insertProductAttributeCategory(productAttributeCategory);
    }

    @Override
    public int update(Long id, String name) {
        ProductAttributeCategory productAttributeCategory = new ProductAttributeCategory();
        productAttributeCategory.setName(name);
        productAttributeCategory.setId(id);
        return productAttributeCategoryMapper.updateById(productAttributeCategory);
    }

    @Override
    public int delete(Long id) {
        return productAttributeCategoryMapper.deleteById(id);
    }
}
