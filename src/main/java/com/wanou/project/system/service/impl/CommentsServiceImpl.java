package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.CommentsMapper;
import com.wanou.project.system.domain.Comments;
import com.wanou.project.system.service.ICommentsService;
import javax.annotation.Resource;

/**
 * 商品评价Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class CommentsServiceImpl implements ICommentsService {
    @Resource
    private CommentsMapper commentsMapper;

    /**
     * 查询商品评价列表小程序
     *
     * @param comments 商品评价
     * @return 商品评价
     */
    @Override
    public List<Comments> selectCommentsListApp(Comments comments) {
        return commentsMapper.selectCommentsListApp(comments);
    }

    /**
     * 查询商品评价
     *
     * @param id 商品评价主键
     * @return 商品评价
     */
    @Override
    public Comments selectCommentsById(Long id) {
        return commentsMapper.selectCommentsById(id);
    }

    /**
     * 查询商品评价列表
     *
     * @param comments 商品评价
     * @return 商品评价
     */
    @Override
    public List<Comments> selectCommentsList(Comments comments) {
        return commentsMapper.selectCommentsList(comments);
    }

    /**
     * 新增商品评价
     *
     * @param comments 商品评价
     * @return 结果
     */
    @Override
    public int insertComments(Comments comments) {
        return commentsMapper.insertComments(comments);
    }

    /**
     * 修改商品评价
     *
     * @param comments 商品评价
     * @return 结果
     */
    @Override
    public int updateComments(Comments comments) {
        return commentsMapper.updateComments(comments);
    }

    /**
     * 批量删除商品评价
     *
     * @param ids 需要删除的商品评价主键
     * @return 结果
     */
    @Override
    public int deleteCommentsByIds(Long[] ids) {
        return commentsMapper.deleteCommentsByIds(ids);
    }

    /**
     * 删除商品评价信息
     *
     * @param id 商品评价主键
     * @return 结果
     */
    @Override
    public int deleteCommentsById(Long id) {
        return commentsMapper.deleteCommentsById(id);
    }
}
