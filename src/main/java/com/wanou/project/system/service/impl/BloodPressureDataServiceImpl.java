package com.wanou.project.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.LinkUtil;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodPressureWarning;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.BloodPressureDataVo;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.IDeviceElderBindingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.BloodPressureDataMapper;
import com.wanou.project.system.domain.BloodPressureData;
import com.wanou.project.system.service.IBloodPressureDataService;
import javax.annotation.Resource;

/**
 * 血压数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-22
 */
@Service
public class BloodPressureDataServiceImpl implements IBloodPressureDataService {
    @Resource
    private BloodPressureDataMapper bloodPressureDataMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;

    /**
     * 查询血压数据
     *
     * @param id 血压数据主键
     * @return 血压数据
     */
    @Override
    public BloodPressureData selectBloodPressureDataById(Long id) {
        return bloodPressureDataMapper.selectBloodPressureDataById(id);
    }

    /**
     * 查询血压数据列表
     *
     * @param bloodPressureData 血压数据
     * @return 血压数据
     */
    @Override
    public TableDataInfo selectBloodPressureDataList(BloodPressureData bloodPressureData) {
        JSONObject params = linkUtil.createParamsStartPage();
        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        deviceElderBindingDto.setElderId(bloodPressureData.getElderId());
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        List<Long> deviceIds = deviceElderBindingVos.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        params.put("inDeviceIds",deviceIds);

        if(bloodPressureData.getDeviceId() != null){
            //设备筛选
            params.put("deviceId",bloodPressureData.getDeviceId());
        }
        if(bloodPressureData.getBloodPressureTestResults() != null){
            //预警等级筛选
            if(bloodPressureData.getBloodPressureTestResults() == 0){
                //正常
                params.put("isAlarm",false);
            }else {
                params.put("isAlarm",true);
                params.put("alarmLevel",bloodPressureData.getBloodPressureTestResults());
            }
        }

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_BLOOD_PRESSURE_ALARM_LIST_URL, TableDataInfo.class, params);
        List<?> rows = tableDataInfo.getRows();
        if(CollUtil.isNotEmpty(rows)){
            //转换
            List<JSONObject> rowArr = rows.stream().map(rowItem -> BeanUtil.toBean(rowItem, JSONObject.class)).collect(Collectors.toList());
            List<DeviceElderBindingVo> deviceElderBindingVoList = deviceElderBindingService.selectDeviceElderBindingListByIds(deviceIds);
            List<BloodPressureDataVo> collection = rowArr.stream().map(rowItem -> {
                BloodPressureDataVo pressureData =  new BloodPressureDataVo();
                pressureData.setId(rowItem.getLong("id"));
                deviceElderBindingVoList.stream().filter(deviceElderBindingVo -> deviceElderBindingVo.getDeviceId().equals(rowItem.getLong("deviceId")))
                        .findFirst().ifPresent(match -> {
                            pressureData.setElderId(match.getElderId());
                            pressureData.setElderPhone(match.getElderPhone());
                            pressureData.setElderName(match.getElderName());
                        });
                pressureData.setDeviceId(rowItem.getLong("deviceId"));
                pressureData.setHighPressure(rowItem.getLong("high"));
                pressureData.setLowPressure(rowItem.getLong("low"));
                pressureData.setPulse(rowItem.getLong("heartRate"));
                pressureData.setDetectionTime(rowItem.getDate("createTime"));
                pressureData.setBloodPressureTestResults(rowItem.getLong("alarmLevel"));
                return pressureData;
            }).collect(Collectors.toList());
            tableDataInfo.setRows(collection);
        }

        return tableDataInfo;
    }

    /**
     * 新增血压数据
     *
     * @param bloodPressureData 血压数据
     * @return 结果
     */
    @Override
    public int insertBloodPressureData(BloodPressureData bloodPressureData) {
        return bloodPressureDataMapper.insertBloodPressureData(bloodPressureData);
    }

    /**
     * 修改血压数据
     *
     * @param bloodPressureData 血压数据
     * @return 结果
     */
    @Override
    public int updateBloodPressureData(BloodPressureData bloodPressureData) {
        return bloodPressureDataMapper.updateBloodPressureData(bloodPressureData);
    }

    /**
     * 批量删除血压数据
     *
     * @param ids 需要删除的血压数据主键
     * @return 结果
     */
    @Override
    public int deleteBloodPressureDataByIds(Long[] ids) {
        return bloodPressureDataMapper.deleteBloodPressureDataByIds(ids);
    }

    /**
     * 删除血压数据信息
     *
     * @param id 血压数据主键
     * @return 结果
     */
    @Override
    public int deleteBloodPressureDataById(Long id) {
        return bloodPressureDataMapper.deleteBloodPressureDataById(id);
    }
}
