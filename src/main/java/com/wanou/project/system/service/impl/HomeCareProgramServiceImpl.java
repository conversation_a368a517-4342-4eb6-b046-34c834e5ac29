package com.wanou.project.system.service.impl;

import com.wanou.common.utils.DateUtils;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.web.domain.TreeSelect;
import com.wanou.project.system.domain.HomeCareProgram;
import com.wanou.project.system.domain.HomeCareServices;
import com.wanou.project.system.domain.SysDept;
import com.wanou.project.system.mapper.HomeCareProgramMapper;
import com.wanou.project.system.service.IHomeCareProgramService;
import com.wanou.project.system.service.IHomeCareServicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Service
public class HomeCareProgramServiceImpl  implements IHomeCareProgramService {
    @Autowired
    private HomeCareProgramMapper homeCareProgramMapper;

    @Override
    public List<HomeCareProgram> getList() {
        List<HomeCareProgram>  list=   homeCareProgramMapper.listAll();
        return list;
    }

    /**
     * 查询家庭护理栏目
     *
     * @param id 家庭护理栏目主键
     * @return 家庭护理栏目
     */
    @Override
    public HomeCareProgram selectHomeCareProgramById(Long id) {
        return homeCareProgramMapper.selectHomeCareProgramById(id);
    }

    /**
     * 查询家庭护理栏目列表
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 家庭护理栏目
     */
    @Override
    public List<HomeCareProgram> selectHomeCareProgramList(HomeCareProgram homeCareProgram) {
        return homeCareProgramMapper.selectHomeCareProgramList(homeCareProgram);
    }

    /**
     * 新增家庭护理栏目
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 结果
     */
    @Override
    public int insertHomeCareProgram(HomeCareProgram homeCareProgram) {
        homeCareProgram.setCreateBy(SecurityUtils.getUsername());
        homeCareProgram.setUpdateBy(SecurityUtils.getUsername());
        return homeCareProgramMapper.insertHomeCareProgram(homeCareProgram);
    }

    /**
     * 修改家庭护理栏目
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 结果
     */
    @Override
    public int updateHomeCareProgram(HomeCareProgram homeCareProgram) {
        homeCareProgram.setUpdateBy(SecurityUtils.getUsername());
        return homeCareProgramMapper.updateHomeCareProgram(homeCareProgram);
    }

    /**
     * 批量删除家庭护理栏目
     *
     * @param ids 需要删除的家庭护理栏目主键
     * @return 结果
     */
    @Override
    public int deleteHomeCareProgramByIds(Long[] ids) {
        return homeCareProgramMapper.deleteHomeCareProgramByIds(ids);
    }

    /**
     * 删除家庭护理栏目信息
     *
     * @param id 家庭护理栏目主键
     * @return 结果
     */
    @Override
    public int deleteHomeCareProgramById(Long id) {
        return homeCareProgramMapper.deleteHomeCareProgramById(id);
    }
}
