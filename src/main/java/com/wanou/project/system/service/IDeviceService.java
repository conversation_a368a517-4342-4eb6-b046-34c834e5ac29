package com.wanou.project.system.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.Device;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;

/**
 * 设备基本信息Service接口
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
public interface IDeviceService {
    /**
     * 查询设备基本信息
     *
     * @param id 设备基本信息主键
     * @return 设备基本信息
     */
    public AjaxResult selectDeviceById(Long id);

    /**
     * 查询设备基本信息列表
     *
     * @param device 设备基本信息
     * @return 设备基本信息集合
     */
    public TableDataInfo selectDeviceList(Device device);

    /**
     * 新增设备基本信息
     *
     * @param device 设备基本信息
     * @return 结果
     */
    public int insertDevice(Device device);

    /**
     * 修改设备基本信息
     *
     * @param device 设备基本信息
     * @return 结果
     */
    public int bindingDevice(Device device);

    /**
     * 批量删除设备基本信息
     *
     * @param ids 需要删除的设备基本信息主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 删除设备基本信息信息
     *
     * @param id 设备基本信息主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);

    /**
     * 绑定长者
     * @param device
     */
    void bindElder(Device device);

    /**
     * 统计
     * @return
     */
    JSONObject deviceStatistics();

    JSONObject getDeviceTotal();

    void updateElderDeviceBind(DeviceElderBindingDto deviceElderBinding);

    TableDataInfo selectDeviceByElderId(Integer elderId);

    void unBindElder(UnBingDto unBingDto);

    TableDataInfo getBelongDevice(String elderId);
}
