package com.wanou.project.system.service.impl;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.DateUtils;
import com.wanou.common.utils.LinkUtil;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.DeviceImgMapper;
import com.wanou.project.system.domain.DeviceImg;
import com.wanou.project.system.service.IDeviceImgService;
import javax.annotation.Resource;

/**
 * 设备图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Service
public class DeviceImgServiceImpl implements IDeviceImgService {
    @Resource
    private DeviceImgMapper deviceImgMapper;
    @Autowired
    private LinkUtil linkUtil;

    /**
     * 查询设备图片
     *
     * @param id 设备图片主键
     * @return 设备图片
     */
    @Override
    public DeviceImg selectDeviceImgById(Long id) {
        return deviceImgMapper.selectDeviceImgById(id);
    }

    /**
     * 查询设备图片列表
     *
     * @param deviceImg 设备图片
     * @return 设备图片
     */
    @Override
    public List<DeviceImg> selectDeviceImgList(DeviceImg deviceImg) {
        return deviceImgMapper.selectDeviceImgList(deviceImg);
    }

    /**
     * 新增设备图片
     *
     * @param deviceImg 设备图片
     * @return 结果
     */
    @Override
    public int insertDeviceImg(DeviceImg deviceImg) {
        deviceImg.setCreateTime(DateUtils.getNowDate());
        deviceImg.setCreatePy(SecurityUtils.getUsername());
        return deviceImgMapper.insertDeviceImg(deviceImg);
    }

    /**
     * 修改设备图片
     *
     * @param deviceImg 设备图片
     * @return 结果
     */
    @Override
    public int updateDeviceImg(DeviceImg deviceImg) {
        return deviceImgMapper.updateDeviceImg(deviceImg);
    }

    /**
     * 批量删除设备图片
     *
     * @param ids 需要删除的设备图片主键
     * @return 结果
     */
    @Override
    public int deleteDeviceImgByIds(Long[] ids) {
        return deviceImgMapper.deleteDeviceImgByIds(ids);
    }

    /**
     * 删除设备图片信息
     *
     * @param id 设备图片主键
     * @return 结果
     */
    @Override
    public int deleteDeviceImgById(Long id) {
        return deviceImgMapper.deleteDeviceImgById(id);
    }

    @Override
    public AjaxResult selectDeviceImgByWolink(Long id) {
        return   linkUtil.getLinkDataDoGet(StrUtil.format("{}/{}", Constants.LINK_GET_DEVICE_IMAGE,id), AjaxResult.class,null);

    }
}
