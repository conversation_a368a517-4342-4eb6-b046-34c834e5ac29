package com.wanou.project.system.service;

import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;

import java.util.List;

/**
 * @ClassName IDeviceElderBindingService
 * @Description 设备-长者绑定Service接口
 * <AUTHOR>
 * @Date 2024-02-24 10:17
 */
public interface IDeviceElderBindingService {
    void bind(DeviceElderBinding deviceElderBinding);

    List<DeviceElderBindingVo> selectDeviceElderBindingListByIds(List<Long> deviceIds);

    List<DeviceElderBindingVo> list(DeviceElderBindingDto deviceElderBinding);

    void update(DeviceElderBinding deviceElderBinding);

    DeviceElderBindingVo selectDeviceElderBinding(Long deviceId);


    void unbind(UnBingDto unBingDto);
}
