package com.wanou.project.system.service;

import java.util.List;

import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodPressureData;

/**
 * 血压数据Service接口
 *
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IBloodPressureDataService {
    /**
     * 查询血压数据
     *
     * @param id 血压数据主键
     * @return 血压数据
     */
    public BloodPressureData selectBloodPressureDataById(Long id);

    /**
     * 查询血压数据列表
     *
     * @param bloodPressureData 血压数据
     * @return 血压数据集合
     */
    public TableDataInfo selectBloodPressureDataList(BloodPressureData bloodPressureData);

    /**
     * 新增血压数据
     *
     * @param bloodPressureData 血压数据
     * @return 结果
     */
    public int insertBloodPressureData(BloodPressureData bloodPressureData);

    /**
     * 修改血压数据
     *
     * @param bloodPressureData 血压数据
     * @return 结果
     */
    public int updateBloodPressureData(BloodPressureData bloodPressureData);

    /**
     * 批量删除血压数据
     *
     * @param ids 需要删除的血压数据主键集合
     * @return 结果
     */
    public int deleteBloodPressureDataByIds(Long[] ids);

    /**
     * 删除血压数据信息
     *
     * @param id 血压数据主键
     * @return 结果
     */
    public int deleteBloodPressureDataById(Long id);
}
