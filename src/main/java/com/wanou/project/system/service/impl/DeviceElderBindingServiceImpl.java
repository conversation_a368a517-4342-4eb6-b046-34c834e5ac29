package com.wanou.project.system.service.impl;

import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.mapper.DeviceElderBindingMapper;
import com.wanou.project.system.service.IDeviceElderBindingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName DeviceElderBindingServiceImpl
 * @Description 设备-长者绑定Service实现类
 * <AUTHOR>
 * @Date 2024-02-24 10:18
 */
@Service
public class DeviceElderBindingServiceImpl implements IDeviceElderBindingService {
    @Resource
    private DeviceElderBindingMapper deviceElderBindingMapper;

    @Override
    public void bind(DeviceElderBinding deviceElderBinding) {
        deviceElderBindingMapper.insertDeviceElderBinding(deviceElderBinding);
    }

    /**
     * 根据设备id查询长者绑定信息
     * @param deviceIds 设备id列表
     * @return
     */
    @Override
    public List<DeviceElderBindingVo> selectDeviceElderBindingListByIds(List<Long> deviceIds) {
        return deviceElderBindingMapper.selectDeviceElderBindingListByIds(deviceIds);
    }

    @Override
    public List<DeviceElderBindingVo> list(DeviceElderBindingDto deviceElderBinding) {
        return deviceElderBindingMapper.selectDeviceElderBindingList(deviceElderBinding);
    }

    @Override
    public void update(DeviceElderBinding deviceElderBinding) {
        deviceElderBindingMapper.updateDeviceElderBinding(deviceElderBinding);
    }

    @Override
    public DeviceElderBindingVo selectDeviceElderBinding(Long deviceId) {
        return deviceElderBindingMapper.selectDeviceElderBindingByDeviceId(deviceId);
    }

    @Override
    public void unbind(UnBingDto unBingDto) {
        deviceElderBindingMapper.unbind(unBingDto);
    }
}
