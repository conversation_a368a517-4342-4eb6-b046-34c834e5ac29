package com.wanou.project.system.service;

import com.wanou.project.system.domain.HomeCareProgram;
import com.wanou.project.system.domain.HomeCareServices;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface IHomeCareProgramService  {

    List<HomeCareProgram> getList();

    /**
     * 查询家庭护理栏目
     *
     * @param id 家庭护理栏目主键
     * @return 家庭护理栏目
     */
    public HomeCareProgram selectHomeCareProgramById(Long id);

    /**
     * 查询家庭护理栏目列表
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 家庭护理栏目集合
     */
    public List<HomeCareProgram> selectHomeCareProgramList(HomeCareProgram homeCareProgram);

    /**
     * 新增家庭护理栏目
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 结果
     */
    public int insertHomeCareProgram(HomeCareProgram homeCareProgram);

    /**
     * 修改家庭护理栏目
     *
     * @param homeCareProgram 家庭护理栏目
     * @return 结果
     */
    public int updateHomeCareProgram(HomeCareProgram homeCareProgram);

    /**
     * 批量删除家庭护理栏目
     *
     * @param ids 需要删除的家庭护理栏目主键集合
     * @return 结果
     */
    public int deleteHomeCareProgramByIds(Long[] ids);

    /**
     * 删除家庭护理栏目信息
     *
     * @param id 家庭护理栏目主键
     * @return 结果
     */
    public int deleteHomeCareProgramById(Long id);
}
