package com.wanou.project.system.service.impl;

import java.util.List;

import cn.hutool.core.util.DesensitizedUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.ElderFamilyMembersMapper;
import com.wanou.project.system.domain.ElderFamilyMembers;
import com.wanou.project.system.service.IElderFamilyMembersService;
import javax.annotation.Resource;

/**
 * 长者家属信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class ElderFamilyMembersServiceImpl implements IElderFamilyMembersService {
    @Resource
    private ElderFamilyMembersMapper elderFamilyMembersMapper;

    /**
     * 查询长者家属信息
     *
     * @param id 长者家属信息主键
     * @return 长者家属信息
     */
    @Override
    public ElderFamilyMembers selectElderFamilyMembersById(Long id) {
        return elderFamilyMembersMapper.selectElderFamilyMembersById(id);
    }

    /**
     * 查询长者家属信息列表
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 长者家属信息
     */
    @Override
    public List<ElderFamilyMembers> selectElderFamilyMembersList(ElderFamilyMembers elderFamilyMembers) {
        List<ElderFamilyMembers> elderFamilyMembersList = elderFamilyMembersMapper.selectElderFamilyMembersList(elderFamilyMembers);
        elderFamilyMembersList.forEach(elderFamilyMembersInfo -> {
            elderFamilyMembersInfo.setPhone(DesensitizedUtil.mobilePhone(elderFamilyMembersInfo.getPhone()));
        });
        return elderFamilyMembersList;
    }

    /**
     * 新增长者家属信息
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 结果
     */
    @Override
    public int insertElderFamilyMembers(ElderFamilyMembers elderFamilyMembers) {
        return elderFamilyMembersMapper.insertElderFamilyMembers(elderFamilyMembers);
    }

    /**
     * 修改长者家属信息
     *
     * @param elderFamilyMembers 长者家属信息
     * @return 结果
     */
    @Override
    public int updateElderFamilyMembers(ElderFamilyMembers elderFamilyMembers) {
        return elderFamilyMembersMapper.updateElderFamilyMembers(elderFamilyMembers);
    }

    /**
     * 批量删除长者家属信息
     *
     * @param ids 需要删除的长者家属信息主键
     * @return 结果
     */
    @Override
    public int deleteElderFamilyMembersByIds(Long[] ids) {
        return elderFamilyMembersMapper.deleteElderFamilyMembersByIds(ids);
    }

    /**
     * 删除长者家属信息信息
     *
     * @param id 长者家属信息主键
     * @return 结果
     */
    @Override
    public int deleteElderFamilyMembersById(Long id) {
        return elderFamilyMembersMapper.deleteElderFamilyMembersById(id);
    }
}
