package com.wanou.project.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.wanou.project.system.domain.ProductAttribute;
import com.wanou.project.system.domain.ProductAttributeCategory;
import com.wanou.project.system.mapper.ProductAttributeCategoryMapper;
import com.wanou.project.system.mapper.ProductAttributeMapper;
import com.wanou.project.system.service.IProductAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品属性管理Service实现类
 * Created by djf on 2025/3/7.
 */
@Service
public class ProductAttributeServiceImpl implements IProductAttributeService {
    @Autowired
    private ProductAttributeMapper productAttributeMapper;
    @Autowired
    private ProductAttributeCategoryMapper productAttributeCategoryMapper;

    @Override
    public List<ProductAttribute> getList(Long cid, Integer type, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        ProductAttribute productAttribute = new ProductAttribute();
        productAttribute.setProductAttributeCategoryId(cid);
        productAttribute.setType(type);
        return productAttributeMapper.selectProductAttribute(productAttribute);
    }

    @Override
    public int create(ProductAttribute productAttribute) {
        int count = productAttributeMapper.insertProductAttribute(productAttribute);
        //新增商品属性以后需要更新商品属性分类数量
        ProductAttributeCategory ProductAttributeCategory = productAttributeCategoryMapper.selectById(productAttribute.getProductAttributeCategoryId());
        if(productAttribute.getType()==0){
            ProductAttributeCategory.setAttributeCount(ProductAttributeCategory.getAttributeCount()+1);
        }else if(productAttribute.getType()==1){
            ProductAttributeCategory.setParamCount(ProductAttributeCategory.getParamCount()+1);
        }
        productAttributeCategoryMapper.updateById(ProductAttributeCategory);
        return count;
    }

    @Override
    public int update(Long id, ProductAttribute productAttribute) {
        productAttribute.setId(id);
        return productAttributeMapper.updateById(productAttribute);
    }

    @Override
    public ProductAttribute getItem(Long id) {
        return productAttributeMapper.selectById(id);
    }

    @Override
    public int delete(List<Long> ids) {
        //获取分类
        ProductAttribute productAttribute = productAttributeMapper.selectById(ids.get(0));
        Integer type = productAttribute.getType();
        ProductAttributeCategory productAttributeCategory = productAttributeCategoryMapper.selectById(productAttribute.getProductAttributeCategoryId());
        int count = productAttributeMapper.deleteById(productAttribute);
        //删除完成后修改数量
        if(type==0){
            if(productAttributeCategory.getAttributeCount()>=count){
                productAttributeCategory.setAttributeCount(productAttributeCategory.getAttributeCount()-count);
            }else{
                productAttributeCategory.setAttributeCount(0);
            }
        }else if(type==1){
            if(productAttributeCategory.getParamCount()>=count){
                productAttributeCategory.setParamCount(productAttributeCategory.getParamCount()-count);
            }else{
                productAttributeCategory.setParamCount(0);
            }
        }
        productAttributeCategoryMapper.updateById(productAttributeCategory);
        return count;
    }

    @Override
    public List<ProductAttribute> getProductAttrInfo(Long productCategoryId) {
        return productAttributeMapper.getProductAttrInfo(productCategoryId);
    }
}
