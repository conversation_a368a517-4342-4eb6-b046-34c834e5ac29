package com.wanou.project.system.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.MonthDay;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mchange.lang.LongUtils;
import com.wanou.common.utils.AesUtil;
import com.wanou.common.utils.DateUtils;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.project.system.mapper.ElderFamilyMembersMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.ElderBaseInfoMapper;
import com.wanou.project.system.domain.ElderBaseInfo;
import com.wanou.project.system.service.IElderBaseInfoService;

/**
 * 长者基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class ElderBaseInfoServiceImpl implements IElderBaseInfoService
{
    @Autowired
    private ElderBaseInfoMapper elderBaseInfoMapper;

    @Autowired
    private ElderFamilyMembersMapper elderFamilyMembersMapper;

    /**
     * 查询长者基本信息
     *
     * @param id 长者基本信息主键
     * @return 长者基本信息
     */
    @Override
    public ElderBaseInfo selectElderBaseInfoById(Long id)
    {
        return elderBaseInfoMapper.selectElderBaseInfoById(id);
    }

    /**
     * 查询长者基本信息列表
     *
     * @param elderBaseInfo 长者基本信息
     * @return 长者基本信息
     */
    @Override
    public List<ElderBaseInfo> selectElderBaseInfoList(ElderBaseInfo elderBaseInfo)
    {
        if (elderBaseInfo.getDeptId() == null){
            elderBaseInfo.setDeptId(SecurityUtils.getDeptId());
        }
        List<ElderBaseInfo> elderBaseInfos = elderBaseInfoMapper.selectElderBaseInfoList(elderBaseInfo);
        elderBaseInfos.forEach(item -> {
            String idCard = AesUtil.decryptFromHex(item.getIdCard());
            long ageByIdCard = IdcardUtil.getAgeByIdCard(idCard);
            item.setAge(ageByIdCard);
        });
        return elderBaseInfos;
    }

    /**
     * 新增长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    @Override
    public int insertElderBaseInfo(ElderBaseInfo elderBaseInfo)
    {
        elderBaseInfo.setCreateTime(DateUtils.getNowDate());
        elderBaseInfo.setCreateBy(SecurityUtils.getUsername());
        return elderBaseInfoMapper.insertElderBaseInfo(elderBaseInfo);
    }

    /**
     * 修改长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    @Override
    public int updateElderBaseInfo(ElderBaseInfo elderBaseInfo)
    {
        elderBaseInfo.setUpdateTime(DateUtils.getNowDate());
        elderBaseInfo.setUpdateBy(SecurityUtils.getUsername());
        return elderBaseInfoMapper.updateElderBaseInfo(elderBaseInfo);
    }

    /**
     * 批量删除长者基本信息
     *
     * @param ids 需要删除的长者基本信息主键
     * @return 结果
     */
    @Override
    public int deleteElderBaseInfoByIds(Long[] ids)
    {
        int elderBaseInfoByIds = elderBaseInfoMapper.deleteElderBaseInfoByIds(ids);
        //根据长者ids删除长者家属信息
        elderFamilyMembersMapper.deleteElderFamilyMembersByElderIds(ids);
        return elderBaseInfoByIds;
    }

    /**
     * 删除长者基本信息信息
     *
     * @param id 长者基本信息主键
     * @return 结果
     */
    @Override
    public int deleteElderBaseInfoById(Long id)
    {
        return elderBaseInfoMapper.deleteElderBaseInfoById(id);
    }

    public static LocalDateTime getStartOfMonth() {
        LocalDate now = LocalDate.now();
        return LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0);
    }

    public static LocalDateTime getEndOfMonth() {
        LocalDate now = LocalDate.now();
        MonthDay endOfMonth = MonthDay.of(now.getMonth(), now.getMonth().length(now.isLeapYear()));
        return LocalDateTime.of(now.getYear(), now.getMonth(), endOfMonth.getDayOfMonth(), 23, 59, 59);
    }
    @Override
    public AjaxResult totalElderData() {
        JSONObject jsonObject = new JSONObject();
        //统计长者总数
        LocalDate now = LocalDate.now(); // 当前日期
        DateTime date = DateUtil.date();
        DateTime startOfWeek = DateUtil.beginOfWeek(date);
        DateTime endOfWeek = DateUtil.endOfWeek(date);
        /*LocalDate startOfWeek = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));//周一
        LocalDate endOfWeek = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));//周日*/
        DateTime startOfMonth = DateUtil.beginOfMonth(date);
        DateTime endOfMonth = DateUtil.endOfMonth(date);
        /*LocalDateTime startOfMonth = getStartOfMonth();//本月开始
        LocalDateTime endOfMonth = getEndOfMonth();//本月结束*/
        JSONObject elderBaseInfo = elderBaseInfoMapper.totalElderData(startOfWeek,endOfWeek,startOfMonth,endOfMonth);
        List<JSONObject> jsonObjects = totalElderByAge();
        List<JSONObject> totalElderBySex = elderBaseInfoMapper.totalElderBySex(SecurityUtils.getDeptId());
        elderBaseInfo.put("elderByAge",jsonObjects);
        elderBaseInfo.put("totalElderBySex",totalElderBySex);
        return AjaxResult.success(elderBaseInfo);
    }

    /**
     * 按年龄统计长者信息
     * @return
     */
    private List<JSONObject> totalElderByAge(){
        //获取所有长者身份证
        List<String> columns = new ArrayList<>(1);
        columns.add("id_card");
        List<ElderBaseInfo> elderBaseInfos = elderBaseInfoMapper.selectColumns(columns, SecurityUtils.getDeptId());
        if(CollUtil.isEmpty(elderBaseInfos)){
            return new ArrayList<>(1);
        }
        //解密
        elderBaseInfos.forEach(item -> {
            if(StrUtil.isNotBlank(item.getIdCard())){
                item.setIdCard(AesUtil.decryptFromHex(item.getIdCard()));
            }
        });
        //按年龄段分组
        List<JSONObject> list = new ArrayList<>(5);
        for (int i = 0; i < 5; i++) {
            switch (i){
                case 0:
                    list.add(convertToJSONObject("50岁以下",0,49));
                    break;
                case 1:
                    list.add(convertToJSONObject("50-60岁",50,60));
                    break;
                case 2:
                    list.add(convertToJSONObject("61-70岁",61,70));
                    break;
                case 3:
                    list.add(convertToJSONObject("71-80岁",71,80));
                    break;
                case 4:
                    list.add(convertToJSONObject("80岁以上",81,150));
                    break;
                default:
                    break;
            }
        }
        elderBaseInfos.forEach(item -> {
            if(StrUtil.isNotBlank(item.getIdCard()) && IdcardUtil.isValidCard(item.getIdCard())){
                //计算年龄
                int age = IdcardUtil.getAgeByIdCard(item.getIdCard());
                list.stream().filter(json -> age >= json.getIntValue("start") && age <= json.getIntValue("end"))
                        .findFirst().ifPresent(match -> match.put("value", match.getIntValue("value") + 1));
            }
        });
        return list;
    }

    private JSONObject convertToJSONObject(String text,int start,int end){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", text);
        jsonObject.put("value", 0);
        jsonObject.put("start", start);
        jsonObject.put("end", end);
        return jsonObject;
    }

    /**
     * 数据大屏统计数据
     * @return
     */
    @Override
    public JSONObject dataViewStatistics(Long deptId) {
        if(deptId == null){
            deptId = SecurityUtils.getDeptId();
        }
        return elderBaseInfoMapper.dataViewStatistics(deptId);
    }
}
