package com.wanou.project.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.wanou.common.api.CommonPage;
import com.wanou.project.system.domain.*;
import com.wanou.project.system.domain.dto.OrderDetail;
import com.wanou.project.system.domain.mbg.OrderExample;
import com.wanou.project.system.domain.mbg.OrderItemExample;
import com.wanou.project.system.mapper.MiniUserInfoMapper;
import com.wanou.project.system.mapper.OrderItemMapper;
import com.wanou.project.system.mapper.OrderMapper;
import com.wanou.project.system.service.IOrderAppService;
import com.wanou.project.tool.gen.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序订单管理
 * Created by djf on 2025/2/10
 */
@Slf4j
@Service
public class OrderAppServiceImpl implements IOrderAppService {
    @Autowired
    private MiniUserInfoMapper miniUserInfoMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Override
    /**
     * 分页查询订单列表
     *
     * @param status 订单状态：-1表示查询所有状态，null表示查询所有状态，其他值表示具体状态
     *               订单状态：0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭；5->无效订单
     * @param pageNum 页码，从1开始
     * @param pageSize 每页大小
     * @return 分页的订单详情列表
     */
    public CommonPage<OrderDetail> list(Integer status, Integer pageNum, Integer pageSize) {
        // 参数预处理：将状态值-1转换为null，表示查询所有状态的订单
        if(status == -1){
            status = null;
        }

        // 获取当前登录用户ID
        Long user = UserContext.getUser();

        // 管理员权限判断：如果用户ID为1，则为管理员，可以查询所有用户的订单
        if(user == 1){
            // 启用分页插件，设置分页参数
            PageHelper.startPage(pageNum, pageSize);
            Orders orders = new Orders();
            orders.setStatus(status);  // 设置订单状态过滤条件
            // 执行查询，获取订单基础信息列表
            List<Orders> orderList = orderMapper.selectByExample(orders);
            //非空判断
            if(CollUtil.isEmpty(orderList)){
                return new CommonPage<>();
            }
            //遍历orderList跟据userId获取用户头像
            orderList.forEach(order -> {
                MiniUserInfo miniUserInfo = miniUserInfoMapper.selectByUserId(String.valueOf(order.getUserId()));
                //miniUserInfo.getAvatarUrl()和miniUserInfo做非空判断
                if(miniUserInfo == null || miniUserInfo.getAvatarUrl() == null){
                    miniUserInfo = new MiniUserInfo();
                    miniUserInfo.setAvatarUrl("https://wo-oss-test.oss-cn-shanghai.aliyuncs.com/retirement_manage/mini/NIXzXo0FZN.jpg");
                }
                order.setAvatarUrl(miniUserInfo.getAvatarUrl());
            });
            // 将查询结果包装成分页对象
            CommonPage<Orders> orderPage = CommonPage.restPage(orderList);

            // 构建返回结果的分页对象，类型为OrderDetail（包含订单商品信息的DTO）
            CommonPage<OrderDetail> resultPage = new CommonPage<>();
            resultPage.setPageNum(orderPage.getPageNum());
            resultPage.setPageSize(orderPage.getPageSize());
            resultPage.setTotal(orderPage.getTotal());
            resultPage.setTotalPage(orderPage.getTotalPage());

            // 如果查询结果为空，直接返回空的分页对象
            if(CollUtil.isEmpty(orderList)){
                return resultPage;
            }

            // 批量查询订单商品信息并组装订单详情
            return assembleOrderDetails(orderList, resultPage);
        }

        // 普通用户查询逻辑：只能查询自己的订单
        // 根据用户ID查询用户信息
        MiniUserInfo miniUserInfo = miniUserInfoMapper.selectByUserId(String.valueOf(user));

        // 启用分页插件，设置分页参数
        PageHelper.startPage(pageNum, pageSize);

        // 构建普通用户查询条件
        Orders orders = new Orders();
        orders.setStatus(status);  // 设置订单状态过滤条件
        orders.setUserId(miniUserInfo.getUserId());  // 只查询当前用户的订单

        // 执行查询，获取当前用户的订单列表
        List<Orders> orderList = orderMapper.selectByOrders(orders);
        // 将查询结果包装成分页对象
        CommonPage<Orders> orderPage = CommonPage.restPage(orderList);

        // 构建返回结果的分页对象
        CommonPage<OrderDetail> resultPage = new CommonPage<>();
        resultPage.setPageNum(orderPage.getPageNum());
        resultPage.setPageSize(orderPage.getPageSize());
        resultPage.setTotal(orderPage.getTotal());
        resultPage.setTotalPage(orderPage.getTotalPage());

        // 如果查询结果为空，直接返回空的分页对象
        if(CollUtil.isEmpty(orderList)){
            return resultPage;
        }

        // 批量查询订单商品信息并组装订单详情
        return assembleOrderDetails(orderList, resultPage);
    }

    /**
     * 组装订单详情信息
     * 将订单基础信息和订单商品信息组装成完整的订单详情对象
     *
     * @param orderList 订单基础信息列表
     * @param resultPage 分页结果对象
     * @return 包含完整订单详情的分页对象
     */
    private CommonPage<OrderDetail> assembleOrderDetails(List<Orders> orderList, CommonPage<OrderDetail> resultPage) {
        // 提取所有订单ID，用于批量查询订单商品信息
        List<Long> orderIds = orderList.stream()
                .map(Orders::getId)
                .collect(Collectors.toList());

        // 批量查询所有订单的商品信息，提高查询效率
        List<OrderItem> orderItemList = orderItemMapper.selectByOrderIds(orderIds);

        // 组装订单详情列表
        List<OrderDetail> orderDetailList = new ArrayList<>();
        for (Orders order : orderList) {
            // 创建订单详情对象
            OrderDetail orderDetail = new OrderDetail();
            // 将订单基础信息复制到订单详情对象中
            BeanUtil.copyProperties(order, orderDetail);

            // 筛选出属于当前订单的商品信息
            List<OrderItem> relatedItemList = orderItemList.stream()
                    .filter(item -> item.getOrderId().equals(orderDetail.getId()))
                    .collect(Collectors.toList());

            // 设置订单商品列表
            orderDetail.setOrderItemList(relatedItemList);
            orderDetailList.add(orderDetail);
        }

        // 设置组装完成的订单详情列表到分页结果中
        resultPage.setList(orderDetailList);
        return resultPage;
    }

    @Override
    public OrderDetail detail(Long orderId) {
        Orders Order = orderMapper.selectById(orderId);
        List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
        OrderDetail orderDetail = new OrderDetail();
        BeanUtil.copyProperties(Order,orderDetail);
        orderDetail.setOrderItemList(orderItemList);
            return orderDetail;
    }

    @Override
    public void cancelOrder(Long orderId) {

        //查询未付款的取消订单
        Orders orders =  new Orders();
        orders.setStatus(0);
        orders.setId(orderId);
        List<Orders> cancelOrderList = orderMapper.selectByOrders(orders);
        if (CollectionUtils.isEmpty(cancelOrderList)) {
            return;
        }
        Orders cancelOrder = cancelOrderList.get(0);
        if (cancelOrder != null) {
            //修改订单状态为取消
            cancelOrder.setStatus(4);
            orderMapper.updateById(cancelOrder);
            List<OrderItem> orderItemList = orderItemMapper.selectByOrderId(orderId);
            //解除订单商品库存锁定
            if (!CollectionUtils.isEmpty(orderItemList)) {
                for (OrderItem orderItem : orderItemList) {
                    int count = orderItemMapper.releaseStockBySkuId(orderItem.getProductSkuId(),orderItem.getProductQuantity());
                    if(count==0){
                        throw new RuntimeException("库存不足，无法释放！");
                    }
                }
            }
//            //修改优惠券使用状态
//            updateCouponStatus(cancelOrder.getCouponId(), cancelOrder.getUserId(), 0);
//            //返还使用积分
//            if (cancelOrder.getUseIntegration() != null) {
//                UmsMember member = memberService.getById(cancelOrder.getUserId());
//                memberService.updateIntegration(cancelOrder.getUserId(), member.getIntegration() + cancelOrder.getUseIntegration());
//            }
        }
    }

    @Override
    public Integer paySuccess(Long orderId, Integer payType) {
        //修改订单支付状态
        Orders order = new Orders();
        order.setId(orderId);
        order.setStatus(1);
        order.setPaymentTime(new Date());
        order.setPayType(payType);
        //只修改未付款状态的订单
        int updateCount = orderMapper.updateOrders(order);
        if(updateCount==0){
            throw new RuntimeException("订单不存在或订单状态不是未支付！");
        }
        //恢复所有下单商品的锁定库存，扣减真实库存
        OrderDetail orderDetail = orderMapper.getDetail(orderId);
        int totalCount = 0;
        for (OrderItem orderItem : orderDetail.getOrderItemList()) {
            int count = orderMapper.reduceSkuStock(orderItem.getProductSkuId(),orderItem.getProductQuantity());
            if(count==0){
                throw new RuntimeException("库存不足，无法扣减！");
            }
            totalCount+=count;
        }
        return totalCount;
    }

    @Override
    public void confirmReceiveOrder(Long orderId) {
        Long user = UserContext.getUser();
        MiniUserInfo miniUserInfo = miniUserInfoMapper.selectByUserId(String.valueOf(user));
        Orders order = orderMapper.selectById(orderId);
        if(!miniUserInfo.getUserId().equals(order.getUserId())){
            throw new RuntimeException("不能确认他人订单！");
        }
        if(order.getStatus()!=2){
            throw new RuntimeException("该订单还未发货！");
        }
        order.setStatus(3);
        order.setConfirmStatus(1);
        order.setReceiveTime(new Date());
        orderMapper.updateByPrimaryKey(order);
    }

    @Override
    public void deleteOrder(Long orderId) {
        Long user = UserContext.getUser();
        MiniUserInfo miniUserInfo = miniUserInfoMapper.selectByUserId(String.valueOf(user));
        Orders order = orderMapper.selectById(orderId);
        if(!miniUserInfo.getUserId().equals(order.getUserId())){
            throw new RuntimeException("不能删除他人订单！");
        }
        if(order.getStatus()==3||order.getStatus()==4){
            order.setDeleteStatus(1);
            orderMapper.updateByPrimaryKey(order);
        }else{
            throw new RuntimeException("只能删除已完成或已关闭的订单！");
        }
    }

    @Override
    public Long addOrder(OrderDetail orderDetail) {
       orderDetail.setCreateTime(new Date());
        orderMapper.insert(orderDetail);
        //帮我返回  orderMapper.insert(orderDetail)创建的id
        Long id = orderDetail.getId();
        for (OrderItem orderItem : orderDetail.getOrderItemList()) {
            orderItem.setOrderId(id);
        }
        int i = orderItemMapper.insertList(orderDetail.getOrderItemList());
        return id;

    }
}
