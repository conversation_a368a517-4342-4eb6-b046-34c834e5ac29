package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.DateUtils;
import com.wanou.common.utils.LinkUtil;
import com.wanou.common.utils.SecurityUtils;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.IDeviceElderBindingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.EquipmentAlarmInformationMapper;
import com.wanou.project.system.domain.EquipmentAlarmInformation;
import com.wanou.project.system.service.IEquipmentAlarmInformationService;
import javax.annotation.Resource;

/**
 * 设备报警信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-17
 */
@Service
public class EquipmentAlarmInformationServiceImpl implements IEquipmentAlarmInformationService {
    @Resource
    private EquipmentAlarmInformationMapper equipmentAlarmInformationMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;

    /**
     * 查询设备报警信息
     *
     * @param id 设备报警信息主键
     * @return 设备报警信息
     */
    @Override
    public EquipmentAlarmInformation selectEquipmentAlarmInformationById(Long id) {
        JSONObject params = linkUtil.createParams();
        params.put("id", id);
        JSONObject result = linkUtil.getLinkDataDoGet(Constants.LINK_GET_ALARM_URL, JSONObject.class, params);
        EquipmentAlarmInformation equipmentAlarmInformation = new EquipmentAlarmInformation();
        equipmentAlarmInformation.setId(result.getLong("id"));
        return equipmentAlarmInformation;
    }

    /**
     * 查询设备报警信息列表
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 设备报警信息
     */
    @Override
    public TableDataInfo selectEquipmentAlarmInformationList(EquipmentAlarmInformation equipmentAlarmInformation) {
        //从物联网平台获取设备报警信息
        JSONObject alarmParams = linkUtil.createParamsStartPage();

        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        deviceElderBindingDto.setDeviceId(equipmentAlarmInformation.getDeviceId());
        deviceElderBindingDto.setElderId(equipmentAlarmInformation.getElderId());
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        List<Long> deviceIds = deviceElderBindingVos.stream().map(DeviceElderBindingVo::getDeviceId).collect(Collectors.toList());
        if(CollUtil.isEmpty(deviceIds)){
            //暂未绑定任何设备 不查询
            return new TableDataInfo(new ArrayList<>(1),0);
        }
        alarmParams.put("inDeviceIds", deviceIds);

        if(equipmentAlarmInformation.getStartTime() != null){
            alarmParams.put("startTime",equipmentAlarmInformation.getStartTime());
        }
        if(equipmentAlarmInformation.getEndTime() != null){
            alarmParams.put("endTime",equipmentAlarmInformation.getEndTime());
        }
        if(CollUtil.isNotEmpty(equipmentAlarmInformation.getInDeviceIds())){
            alarmParams.put("inDeviceIds", equipmentAlarmInformation.getInDeviceIds());
        }
        alarmParams.put("handleFlag",equipmentAlarmInformation.getIsHandle());

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_ALARM_LIST_URL, TableDataInfo.class,alarmParams);
        List<JSONObject> rows = tableDataInfo.getRows().stream().map(row -> BeanUtil.toBean(row, JSONObject.class)).collect(Collectors.toList());
        //处理数据
        rows.forEach(row -> {
            DeviceElderBindingVo matchDeviceElderBind = deviceElderBindingVos.stream()
                    .filter(deviceElderBindingVo -> deviceElderBindingVo.getDeviceId().equals(row.getJSONObject("deviceInfo").getLong("id")))
                    .findFirst().orElse(null);
            if(matchDeviceElderBind != null){
                row.put("elderName",matchDeviceElderBind.getElderName());
                row.put("elderPhone",matchDeviceElderBind.getElderPhone());
            }
        });

        tableDataInfo.setRows(rows);
        return tableDataInfo;
    }

    /**
     * 新增设备报警信息
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 结果
     */
    @Override
    public int insertEquipmentAlarmInformation(EquipmentAlarmInformation equipmentAlarmInformation) {
        return equipmentAlarmInformationMapper.insertEquipmentAlarmInformation(equipmentAlarmInformation);
    }

    /**
     * 修改设备报警信息
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 结果
     */
    @Override
    public int disposeEquipmentAlarmInformation(EquipmentAlarmInformation equipmentAlarmInformation) {
        if (equipmentAlarmInformation != null){
            if (StrUtil.isNotBlank(equipmentAlarmInformation.getHandleResults())){
                equipmentAlarmInformation.setHandelBy(SecurityUtils.getUsername());
                equipmentAlarmInformation.setHandelTime(DateUtils.getNowDate());
                equipmentAlarmInformation.setIsHandle(1l);
            }
        }
        return equipmentAlarmInformationMapper.disposeEquipmentAlarmInformation(equipmentAlarmInformation);
    }

    /**
     * 批量删除设备报警信息
     *
     * @param ids 需要删除的设备报警信息主键
     * @return 结果
     */
    @Override
    public int deleteEquipmentAlarmInformationByIds(Long[] ids) {
        return equipmentAlarmInformationMapper.deleteEquipmentAlarmInformationByIds(ids);
    }

    /**
     * 删除设备报警信息信息
     *
     * @param id 设备报警信息主键
     * @return 结果
     */
    @Override
    public int deleteEquipmentAlarmInformationById(Long id) {
        return equipmentAlarmInformationMapper.deleteEquipmentAlarmInformationById(id);
    }
}
