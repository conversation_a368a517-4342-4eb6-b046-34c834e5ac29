package com.wanou.project.system.service;

import java.util.List;

import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodGlucoseData;

/**
 * 血糖数据Service接口
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
public interface IBloodGlucoseDataService {
    /**
     * 查询血糖数据
     *
     * @param id 血糖数据主键
     * @return 血糖数据
     */
    public BloodGlucoseData selectBloodGlucoseDataById(Long id);

    /**
     * 查询血糖数据列表
     *
     * @param bloodGlucoseData 血糖数据
     * @return 血糖数据集合
     */
    public TableDataInfo selectBloodGlucoseDataList(BloodGlucoseData bloodGlucoseData);

    /**
     * 新增血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    public int insertBloodGlucoseData(BloodGlucoseData bloodGlucoseData);

    /**
     * 修改血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    public int updateBloodGlucoseData(BloodGlucoseData bloodGlucoseData);

    /**
     * 批量删除血糖数据
     *
     * @param ids 需要删除的血糖数据主键集合
     * @return 结果
     */
    public int deleteBloodGlucoseDataByIds(Long[] ids);

    /**
     * 删除血糖数据信息
     *
     * @param id 血糖数据主键
     * @return 结果
     */
    public int deleteBloodGlucoseDataById(Long id);
}
