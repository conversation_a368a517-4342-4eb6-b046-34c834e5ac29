package com.wanou.project.system.service;

import java.util.List;
import com.wanou.project.system.domain.ElderlyHealthTestingInformation;

/**
 * 长者健康信息检测Service接口
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
public interface IElderlyHealthTestingInformationService {
    /**
     * 查询长者健康信息检测
     *
     * @param elderId 长者健康信息检测主键
     * @return 长者健康信息检测
     */
    public ElderlyHealthTestingInformation selectElderlyHealthTestingInformationByElderId(Long elderId);

    /**
     * 查询长者健康信息检测列表
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 长者健康信息检测集合
     */
    public List<ElderlyHealthTestingInformation> selectElderlyHealthTestingInformationList(ElderlyHealthTestingInformation elderlyHealthTestingInformation);

    /**
     * 新增长者健康信息检测
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 结果
     */
    public int insertElderlyHealthTestingInformation(ElderlyHealthTestingInformation elderlyHealthTestingInformation);

    /**
     * 修改长者健康信息检测
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 结果
     */
    public int updateElderlyHealthTestingInformation(ElderlyHealthTestingInformation elderlyHealthTestingInformation);

    /**
     * 批量删除长者健康信息检测
     *
     * @param elderIds 需要删除的长者健康信息检测主键集合
     * @return 结果
     */
    public int deleteElderlyHealthTestingInformationByElderIds(Long[] elderIds);

    /**
     * 删除长者健康信息检测信息
     *
     * @param elderId 长者健康信息检测主键
     * @return 结果
     */
    public int deleteElderlyHealthTestingInformationByElderId(Long elderId);
}
