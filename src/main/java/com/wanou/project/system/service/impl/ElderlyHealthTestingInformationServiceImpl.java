package com.wanou.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.domain.ElderlyHealthTestingInformation;
import com.wanou.project.system.service.IElderlyHealthTestingInformationService;
import javax.annotation.Resource;

/**
 * 长者健康信息检测Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
@Service
public class ElderlyHealthTestingInformationServiceImpl implements IElderlyHealthTestingInformationService {

    /**
     * 查询长者健康信息检测
     *
     * @param elderId 长者健康信息检测主键
     * @return 长者健康信息检测
     */
    @Override
    public ElderlyHealthTestingInformation selectElderlyHealthTestingInformationByElderId(Long elderId) {
        return null;
    }

    /**
     * 查询长者健康信息检测列表
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 长者健康信息检测
     */
    @Override
    public List<ElderlyHealthTestingInformation> selectElderlyHealthTestingInformationList(ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        return null;
    }

    /**
     * 新增长者健康信息检测
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 结果
     */
    @Override
    public int insertElderlyHealthTestingInformation(ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        return 0;
    }

    /**
     * 修改长者健康信息检测
     *
     * @param elderlyHealthTestingInformation 长者健康信息检测
     * @return 结果
     */
    @Override
    public int updateElderlyHealthTestingInformation(ElderlyHealthTestingInformation elderlyHealthTestingInformation) {
        return 0;
    }

    /**
     * 批量删除长者健康信息检测
     *
     * @param elderIds 需要删除的长者健康信息检测主键
     * @return 结果
     */
    @Override
    public int deleteElderlyHealthTestingInformationByElderIds(Long[] elderIds) {
        return 0;
    }

    /**
     * 删除长者健康信息检测信息
     *
     * @param elderId 长者健康信息检测主键
     * @return 结果
     */
    @Override
    public int deleteElderlyHealthTestingInformationByElderId(Long elderId) {
        return 0;
    }
}
