package com.wanou.project.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;

import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.exception.CustomException;
import com.wanou.common.exception.user.UserPasswordNotMatchException;
import com.wanou.common.utils.AesUtil;
import com.wanou.common.utils.StringUtils;
import com.wanou.framework.redis.RedisCache;
import com.wanou.framework.security.LoginUser;
import com.wanou.framework.security.service.TokenService;
import com.wanou.framework.security.service.UserDetailsServiceImpl;
import com.wanou.project.system.domain.mini.MiniUserLogin;
import com.wanou.project.system.domain.mini.MiniUserLoginResponse;
import com.wanou.project.system.domain.MiniUserInfo;
import com.wanou.project.system.domain.SysUser;
import com.wanou.project.system.mapper.MiniUserInfoMapper;
import com.wanou.project.system.mapper.SysUserMapper;
import com.wanou.project.system.service.ISysUserService;
import com.wanou.project.system.service.MiniUserInfoService;
import com.wanou.project.tool.gen.util.UserContext;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9 11:47
 * 小程序用户信息服务类
 */
@Service
@Slf4j
public class MiniUserInfoServiceImpl implements MiniUserInfoService {
    @Autowired
    private MiniUserInfoMapper miniUserInfoMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private UserDetailsServiceImpl userDetailsServiceImpl;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Value("${weChat.appId}")
    private String appId;

    @Value("${weChat.appSecret}")
    private String appSecret;

    /**
     * 小程序用户登录
     *
     * @param miniUserLogin
     * @return
     */
    String url = "https://api.weixin.qq.com/sns/jscode2session";


    /**
     * 小程序一键登录
     * @param miniUserLogin
     * @return
     */
    @Override
    public MiniUserLoginResponse login(MiniUserLogin miniUserLogin) {
        if (StrUtil.isBlank(miniUserLogin.getCode())) {
            throw new CustomException("code不能为空");
        }

        String result = HttpRequest.get(url + StrUtil.format("?appid={}&secret={}&js_code={}&grant_type=authorization_code", appId,  appSecret, miniUserLogin.getCode())).execute().body();

        if(StrUtil.isBlank(result)){
            throw new CustomException("服务器异常,请稍后再试");
        }
        JSONObject jsonResult = JSONObject.parseObject(result);
        Integer errcode = jsonResult.getInteger("errcode");
        if(errcode != null){
            //出现异常
            log.error("获取openid异常:code:{},错误码:{}",miniUserLogin.getCode(),errcode);
            throw new CustomException("服务器繁忙,请稍后再试");
        }


        log.info("请求成功");
        String openid = jsonResult.get("openid").toString();
        String sessionKey = jsonResult.get("session_key").toString();
        MiniUserInfo miniUserInfo = miniUserInfoMapper.selectMiniUserInfoByOpenId(openid);
        if (miniUserInfo == null) {
            //为空 用户首次登陆 自动注册
            MiniUserInfo insertMiniUserInfo = new MiniUserInfo();
            insertMiniUserInfo.setOpenId(openid);
            insertMiniUserInfo.setRegTime(DateUtil.date());
            insertMiniUserInfo.setStatus(1);
            //使用hutool工具包生成自增的用户id
            String s = IdUtil.getSnowflake(1, 1).nextIdStr();
            //使用uuid生成一个的用户id
            insertMiniUserInfo.setUserId(Long.valueOf(s));
            //设置用户昵称
            insertMiniUserInfo.setNickName("万欧老人_"+s);
            insertMiniUserInfo.setAvatarUrl("https://wo-oss-test.oss-cn-shanghai.aliyuncs.com/retirement_manage/mini/NIXzXo0FZN.jpg");
            miniUserInfoMapper.insert(insertMiniUserInfo);
            MiniUserLoginResponse response1 = new MiniUserLoginResponse();
            response1.setMiniUserId(insertMiniUserInfo.getId());
            //并且在管理端创建用户
            SysUser sysUser = new SysUser();
            sysUser.setUserId(String.valueOf(insertMiniUserInfo.getUserId()));
            sysUser.setDeptId(111L);
            sysUser.setUserName(insertMiniUserInfo.getNickName());
            sysUser.setNickName(insertMiniUserInfo.getNickName());
            sysUser.setAvatar(insertMiniUserInfo.getAvatarUrl());
            sysUser.setCreateTime(DateUtil.date());
            sysUserMapper.insertUser(sysUser);
            LoginUser loginUser = (LoginUser) userDetailsServiceImpl.createLoginUser(sysUser);
                // 生成token
                String token = tokenService.createToken(loginUser);
                MiniUserLoginResponse response = new MiniUserLoginResponse();
                response.setToken(token);
                return response;
        }else {
                SysUser sysUser = sysUserService.selectUserById(miniUserInfo.getUserId());
                if(sysUser == null){
                    throw new CustomException("绑定的用户不存在");
                }
                else {
                    LoginUser loginUser = (LoginUser) userDetailsServiceImpl.createLoginUser(sysUser);
                    // 生成token
                    String token = tokenService.createToken(loginUser);
                    MiniUserLoginResponse response = new MiniUserLoginResponse();
                    response.setMiniUserId(miniUserInfo.getId());
                    response.setToken(token);
                    return response;
                }
            }
        }

    /**
     * 小程序绑定用户
     * @param miniUserLogin
     * @return
     */
    @Override
    public String bindUser(MiniUserLogin miniUserLogin) {
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(miniUserLogin.getUsername(), miniUserLogin.getPassword()));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            }
            else {
                throw new CustomException(e.getMessage());
            }
        }
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginUser.setMiniUserId(miniUserLogin.getMiniUserId());
        //修改小程序用户信息 与user绑定
        MiniUserInfo updateMiniUserInfo = new MiniUserInfo();
        updateMiniUserInfo.setId(miniUserLogin.getMiniUserId());
        updateMiniUserInfo.setUserId(loginUser.getUserId());
        miniUserInfoMapper.update(updateMiniUserInfo);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 根据部门id查询绑定的小程序用户
     * @param deptIdArr
     * @return
     */
    @Override
    public List<MiniUserInfo> selectByDeptIds(List<Long> deptIdArr) {
        return miniUserInfoMapper.selectByDeptIds(deptIdArr);
    }

    @Override
    public MiniUserInfo  getUserInfo(String token) {
        //直接从ThreadLocal中拿用户id
        Long user = UserContext.getUser();
        //通过userId查询用户数据
        MiniUserInfo miniUserInfo=    miniUserInfoMapper.selectByUserId(String.valueOf(user));
        return miniUserInfo;
    }

    @Transactional
    @Override
    public void updateUserInfo(MiniUserInfo miniUserInfo) {
        Long user = UserContext.getUser();
        //判断传过来的gender是0还是1，如果是0为男2，如果是1为女
        if (miniUserInfo.getGender() == 0){
            miniUserInfo.setSex("男");
        }else {
            miniUserInfo.setSex("女");
        }
        miniUserInfo.setUserId(user);
        miniUserInfoMapper.updateById(miniUserInfo);
        sysUserMapper.updateById(miniUserInfo);
    }

    @Override
    public List<String> getMiniUserInfo() {
       List<String>  miniUserInfos = miniUserInfoMapper.selectList();

        return miniUserInfos;
    }


}
