package com.wanou.project.system.service;

import com.wanou.project.system.domain.ProductAttribute;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 商品属性管理Service
 * Created by djf on 2025/3/7.
 */
public interface IProductAttributeService {
    /**
     * 根据分类ID和类型分页获取商品属性
     * @param cid 分类id
     * @param type 0->规格；1->参数
     */
    List<ProductAttribute> getList(Long cid, Integer type, Integer pageSize, Integer pageNum);

    /**
     * 添加商品属性
     */
    @Transactional
    int create(ProductAttribute ProductAttribute);

    /**
     * 修改商品属性
     */
    int update(Long id, ProductAttribute productAttribute);

    /**
     * 获取单个商品属性信息
     */
    ProductAttribute getItem(Long id);

    /**
     * 批量删除商品属性
     */
    @Transactional
    int delete(List<Long> ids);

    /**
     * 获取商品分类对应属性列表
     */
    List<ProductAttribute> getProductAttrInfo(Long productCategoryId);
}
