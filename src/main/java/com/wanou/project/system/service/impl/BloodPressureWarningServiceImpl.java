package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.LinkUtil;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.BloodPressureWarningDto;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.IDeviceElderBindingService;
import com.wanou.project.system.service.IDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.BloodPressureWarningMapper;
import com.wanou.project.system.domain.BloodPressureWarning;
import com.wanou.project.system.service.IBloodPressureWarningService;
import javax.annotation.Resource;

/**
 * 血压预警Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Service
public class BloodPressureWarningServiceImpl implements IBloodPressureWarningService {
    @Resource
    private BloodPressureWarningMapper bloodPressureWarningMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;

    /**
     * 查询血压预警
     *
     * @param id 血压预警主键
     * @return 血压预警
     */
    @Override
    public BloodPressureWarning selectBloodPressureWarningById(Long id) {
        return bloodPressureWarningMapper.selectBloodPressureWarningById(id);
    }

    /**
     * 查询血压预警列表
     *
     * @param bloodPressureWarning 血压预警
     * @return 血压预警
     */
    @Override
    public TableDataInfo selectBloodPressureWarningList(BloodPressureWarningDto bloodPressureWarning) {
        //return bloodPressureWarningMapper.selectBloodPressureWarningList(bloodPressureWarning);
        JSONObject params = linkUtil.createParams();
        params.put("isAlarm",true);
        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        if(bloodPressureWarning.getElderId() != null){
            //长者筛选
            deviceElderBindingDto.setElderId(bloodPressureWarning.getElderId());
        }
        if(bloodPressureWarning.getDeviceId() != null){
            //设备筛选
            //params.put("deviceId",bloodPressureWarning.getDeviceId());
            deviceElderBindingDto.setDeviceId(bloodPressureWarning.getDeviceId());
        }
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        if(CollUtil.isEmpty(deviceElderBindingVos)){
            return new TableDataInfo(new ArrayList<>(1),0);
        }
        List<Long> deviceIds = deviceElderBindingVos.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        params.put("inDeviceIds",deviceIds);

        if(bloodPressureWarning.getWarningResults() != null){
            //预警等级筛选
            params.put("alarmLevel",bloodPressureWarning.getWarningResults());
        }
        if(CollUtil.isNotEmpty(bloodPressureWarning.getInDeviceIds())){
            params.put("inDeviceIds",bloodPressureWarning.getInDeviceIds());
        }

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_BLOOD_PRESSURE_ALARM_LIST_URL, TableDataInfo.class, params);
        List<?> rows = tableDataInfo.getRows();
        if(CollUtil.isNotEmpty(rows)){
            //转换
            List<JSONObject> rowArr = rows.stream().map(rowItem -> BeanUtil.toBean(rowItem, JSONObject.class)).collect(Collectors.toList());
            List<Long> deviceIdArr = rowArr.stream().map(rowItem -> rowItem.getLong("deviceId")).collect(Collectors.toList());
            List<DeviceElderBindingVo> deviceElderBindingVoList = deviceElderBindingService.selectDeviceElderBindingListByIds(deviceIdArr);
            List<BloodPressureWarning> collection = rowArr.stream().map(rowItem -> {
                BloodPressureWarning warning = new BloodPressureWarning();
                warning.setId(rowItem.getLong("id"));
                deviceElderBindingVoList.stream().filter(deviceElderBindingVo -> deviceElderBindingVo.getDeviceId().equals(rowItem.getLong("deviceId")))
                        .findFirst().ifPresent(match -> warning.setElderId(match.getElderId()));
                warning.setDeviceId(rowItem.getLong("deviceId"));
                warning.setHighPressure(rowItem.getLong("high"));
                warning.setLowPressure(rowItem.getLong("low"));
                warning.setPulse(rowItem.getLong("heartRate"));
                warning.setDetectionTime(rowItem.getDate("createTime"));
                warning.setWarningResults(rowItem.getLong("alarmLevel"));
                return warning;
            }).collect(Collectors.toList());
            tableDataInfo.setRows(collection);
        }
        return tableDataInfo;
    }

    /**
     * 新增血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    @Override
    public int insertBloodPressureWarning(BloodPressureWarning bloodPressureWarning) {
        return bloodPressureWarningMapper.insertBloodPressureWarning(bloodPressureWarning);
    }

    /**
     * 修改血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    @Override
    public int updateBloodPressureWarning(BloodPressureWarning bloodPressureWarning) {
        return bloodPressureWarningMapper.updateBloodPressureWarning(bloodPressureWarning);
    }

    /**
     * 批量删除血压预警
     *
     * @param ids 需要删除的血压预警主键
     * @return 结果
     */
    @Override
    public int deleteBloodPressureWarningByIds(Long[] ids) {
        return bloodPressureWarningMapper.deleteBloodPressureWarningByIds(ids);
    }

    /**
     * 删除血压预警信息
     *
     * @param id 血压预警主键
     * @return 结果
     */
    @Override
    public int deleteBloodPressureWarningById(Long id) {
        return bloodPressureWarningMapper.deleteBloodPressureWarningById(id);
    }
}
