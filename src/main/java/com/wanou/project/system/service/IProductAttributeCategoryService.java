package com.wanou.project.system.service;

import com.wanou.project.system.domain.ProductAttributeCategory;

import java.util.List;

/**
 * 商品属性分类管理Service
 * Created by DJF on 2025/3/5
 */
public interface IProductAttributeCategoryService {
    /**
     * 创建属性分类
     */
    int create(String name);

    /**
     * 修改属性分类
     */
    int update(Long id, String name);

    /**
     * 删除属性分类
     */
    int delete(Long id);

    /**
     * 获取属性分类详情
     */
    ProductAttributeCategory getItem(Long id);

    /**
     * 分页查询属性分类
     */
    List<ProductAttributeCategory> getList(Integer pageSize, Integer pageNum);

}
