package com.wanou.project.system.service;



import com.wanou.project.system.domain.mini.MiniUserLogin;
import com.wanou.project.system.domain.mini.MiniUserLoginResponse;
import com.wanou.project.system.domain.MiniUserInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9 11:47
 */
public interface MiniUserInfoService {
    MiniUserLoginResponse login(MiniUserLogin miniUserLogin);

    String bindUser(MiniUserLogin miniUserLogin);

    /**
     * 根据部门id查询小程序用户
     * @param deptIdArr
     * @return
     */
    List<MiniUserInfo> selectByDeptIds(List<Long> deptIdArr);

    MiniUserInfo getUserInfo(String token);


    void updateUserInfo(MiniUserInfo miniUserInfo);

    List<String> getMiniUserInfo();
}
