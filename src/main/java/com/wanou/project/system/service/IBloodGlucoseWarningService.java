package com.wanou.project.system.service;

import java.util.List;

import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodGlucoseWarning;

/**
 * 血糖预警Service接口
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
public interface IBloodGlucoseWarningService {
    /**
     * 查询血糖预警
     *
     * @param id 血糖预警主键
     * @return 血糖预警
     */
    public BloodGlucoseWarning selectBloodGlucoseWarningById(Long id);

    /**
     * 查询血糖预警列表
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 血糖预警集合
     */
    public TableDataInfo selectBloodGlucoseWarningList(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 新增血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    public int insertBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 修改血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    public int updateBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 批量删除血糖预警
     *
     * @param ids 需要删除的血糖预警主键集合
     * @return 结果
     */
    public int deleteBloodGlucoseWarningByIds(Long[] ids);

    /**
     * 删除血糖预警信息
     *
     * @param id 血糖预警主键
     * @return 结果
     */
    public int deleteBloodGlucoseWarningById(Long id);
}
