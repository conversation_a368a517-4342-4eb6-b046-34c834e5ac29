package com.wanou.project.system.service;

import com.wanou.project.system.domain.ProductCategory;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商品分类管理
 * Created by djf on 2025/2/21.
 */
public interface IProductCategoryService {

    List<ProductCategory> getProductCategoryListApp(ProductCategory productCategory);

    /**
     * 分页获取商品分类
     */
    List<ProductCategory> getList(Long parentId, Integer pageSize, Integer pageNum);

    /**
     * 根据ID获取商品分类
     */
    ProductCategory getItem(Long id);

    /**
     * 新增商品分类
     */
    @Transactional
    int insertProductCategory(ProductCategory productCategory);

    /**
     * 修改商品分类
     */
    @Transactional
    int updateProductCategory(Long id, ProductCategory productCategory);

    /**
     * 删除商品分类
     */
    int deleteProductCategory(Long id);

    int updateIsAvailable(List<Long> ids, Integer isAvailable);
}
