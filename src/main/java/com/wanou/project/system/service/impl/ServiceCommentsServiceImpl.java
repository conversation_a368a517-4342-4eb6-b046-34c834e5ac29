package com.wanou.project.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanou.project.system.domain.ServiceComments;
import com.wanou.project.system.domain.vo.ServiceCommentsVo;
import com.wanou.project.system.mapper.ServiceCommentsMapper;
import com.wanou.project.system.service.IServiceCommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Service
public class ServiceCommentsServiceImpl implements IServiceCommentsService {
    @Autowired
    private ServiceCommentsMapper serviceCommentsMapper;

    @Override
    public List<ServiceCommentsVo> list(String serviceId) {
        List<ServiceComments> serviceCommentsList = serviceCommentsMapper.selectList(serviceId);
        List<ServiceCommentsVo> serviceCommentsVos = BeanUtil.copyToList(serviceCommentsList, ServiceCommentsVo.class);
        if (ObjectUtil.isNotEmpty(serviceCommentsList)) {
            for (int i = 0; i < serviceCommentsList.size(); i++) {
                String picture = serviceCommentsList.get(i).getPicture();
                if (ObjectUtil.isNotEmpty(picture)) {
                    serviceCommentsVos.get(i).setPictures(picture.split(","));
                }
            }
        }
        return serviceCommentsVos;
    }

    @Override
    public Integer count(String serviceId) {

      return   serviceCommentsMapper.count(serviceId);
    }
}
