package com.wanou.project.system.service;

import com.wanou.project.system.domain.HomeCareServices;
import java.util.List;

/**
 * <p>
 * 家庭护理服务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface IHomeCareServicesService {
    /**
     * 通过项目id查询服务列表*
     * @param id
     * @return
     */
    List<HomeCareServices> getPage(String id);

    /**
     * * 通过id查询服务*
     * @param id
     * @return
     */
    HomeCareServices getById(String id);

    /**
     * 查询家庭护理服务
     *
     * @param id 家庭护理服务主键
     * @return 家庭护理服务
     */
    public HomeCareServices selectHomeCareServicesById(Long id);

    /**
     * 查询家庭护理服务列表
     *
     * @param homeCareServices 家庭护理服务
     * @return 家庭护理服务集合
     */
    public List<HomeCareServices> selectHomeCareServicesList(HomeCareServices homeCareServices);

    /**
     * 新增家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    public int insertHomeCareServices(HomeCareServices homeCareServices);

    /**
     * 修改家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    public int updateHomeCareServices(HomeCareServices homeCareServices);

    /**
     * 批量删除家庭护理服务
     *
     * @param ids 需要删除的家庭护理服务主键集合
     * @return 结果
     */
    public int deleteHomeCareServicesByIds(Long[] ids);

    /**
     * 删除家庭护理服务信息
     *
     * @param id 家庭护理服务主键
     * @return 结果
     */
    public int deleteHomeCareServicesById(Long id);
}
