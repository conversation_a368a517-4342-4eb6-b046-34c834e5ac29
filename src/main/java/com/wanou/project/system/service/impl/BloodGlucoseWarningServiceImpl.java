package com.wanou.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.common.utils.LinkUtil;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodGlucoseData;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.IDeviceElderBindingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wanou.project.system.mapper.BloodGlucoseWarningMapper;
import com.wanou.project.system.domain.BloodGlucoseWarning;
import com.wanou.project.system.service.IBloodGlucoseWarningService;
import javax.annotation.Resource;

/**
 * 血糖预警Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Service
public class BloodGlucoseWarningServiceImpl implements IBloodGlucoseWarningService {
    @Resource
    private BloodGlucoseWarningMapper bloodGlucoseWarningMapper;
    @Resource
    private LinkUtil linkUtil;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;

    /**
     * 查询血糖预警
     *
     * @param id 血糖预警主键
     * @return 血糖预警
     */
    @Override
    public BloodGlucoseWarning selectBloodGlucoseWarningById(Long id) {
        return bloodGlucoseWarningMapper.selectBloodGlucoseWarningById(id);
    }

    /**
     * 查询血糖预警列表
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 血糖预警
     */
    @Override
    public TableDataInfo selectBloodGlucoseWarningList(BloodGlucoseWarning bloodGlucoseWarning) {
        JSONObject params = linkUtil.createParamsStartPage();
        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        deviceElderBindingDto.setElderId(bloodGlucoseWarning.getElderId());
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        List<Long> deviceIds = deviceElderBindingVos.stream().map(DeviceElderBinding::getDeviceId).collect(Collectors.toList());
        if(CollUtil.isEmpty(deviceIds)){
            return new TableDataInfo(new ArrayList<>(1),0);
        }
        params.put("inDeviceIds",deviceIds);

        if(bloodGlucoseWarning.getDeviceId() != null){
            //设备筛选
            params.put("deviceId",bloodGlucoseWarning.getDeviceId());
        }
        params.put("alarmFlag",true);
        List<Long> queryDeviceTypes = new ArrayList<>();
        queryDeviceTypes.add(8L);
        params.put("deviceTypes",queryDeviceTypes);

        TableDataInfo tableDataInfo = linkUtil.getLinkDataDoGet(Constants.LINK_GET_SENSOR_HISTORY_LIST_URL, TableDataInfo.class, params);
        List<BloodGlucoseWarning> list = tableDataInfo.getRows().stream().map(item -> {
            JSONObject bean = BeanUtil.toBean(item, JSONObject.class);
            BloodGlucoseWarning data = new BloodGlucoseWarning();
            data.setId(bean.getLong("id"));
            deviceElderBindingVos.stream().filter(deviceElderBindingVo -> deviceElderBindingVo.getDeviceId()
                            .equals(bean.getLong("deviceId")))
                    .findFirst().ifPresent(match -> {
                        data.setElderId(match.getElderId());
                        data.setElderPhone(match.getElderPhone());
                    });
            data.setGlucose(NumberUtil.toBigDecimal(bean.getString("value")));
            data.setDetectionTime(bean.getDate("reportTime"));
            data.setWarningResults(2L);
            data.setDeviceId(bean.getLong("deviceId"));

            return data;
        }).collect(Collectors.toList());
        tableDataInfo.setRows(list);
        tableDataInfo.setTotal(list.size());
        return tableDataInfo;
    }

    /**
     * 新增血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    @Override
    public int insertBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning) {
        return bloodGlucoseWarningMapper.insertBloodGlucoseWarning(bloodGlucoseWarning);
    }

    /**
     * 修改血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    @Override
    public int updateBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning) {
        return bloodGlucoseWarningMapper.updateBloodGlucoseWarning(bloodGlucoseWarning);
    }

    /**
     * 批量删除血糖预警
     *
     * @param ids 需要删除的血糖预警主键
     * @return 结果
     */
    @Override
    public int deleteBloodGlucoseWarningByIds(Long[] ids) {
        return bloodGlucoseWarningMapper.deleteBloodGlucoseWarningByIds(ids);
    }

    /**
     * 删除血糖预警信息
     *
     * @param id 血糖预警主键
     * @return 结果
     */
    @Override
    public int deleteBloodGlucoseWarningById(Long id) {
        return bloodGlucoseWarningMapper.deleteBloodGlucoseWarningById(id);
    }
}
