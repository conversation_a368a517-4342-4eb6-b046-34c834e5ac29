package com.wanou.project.system.service;

import com.wanou.project.system.domain.DeviceStore;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface IDeviceStoreService {

    List<DeviceStore> queryDevice();

    DeviceStore queryDeviceById(Long id);

    /**
     * 查询设备商城列表
     *
     * @param deviceStore 设备商城
     * @return 设备商城集合
     */
    public List<DeviceStore> selectDeviceStoreList(DeviceStore deviceStore);

    /**
     * 新增设备商城
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    public int insertDeviceStore(DeviceStore deviceStore);

    /**
     * 修改设备商城
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    public int updateDeviceStore(DeviceStore deviceStore);

    /**
     * 批量删除设备商城
     *
     * @param ids 需要删除的设备商城主键集合
     * @return 结果
     */
    public int deleteDeviceStoreByIds(Long[] ids);

    /**
     * 删除设备商城信息
     *
     * @param id 设备商城主键
     * @return 结果
     */
    public int deleteDeviceStoreById(Long id);

    /**
     *
     * @param category
     * @return
     */
    List<DeviceStore> queryDeviceByCategory(String category);
}
