package com.wanou.project.system.service.impl;

import com.wanou.project.system.domain.Address;
import com.wanou.project.system.domain.Comments;
import com.wanou.project.system.mapper.AddressMapper;
import com.wanou.project.system.service.IAddressService;
import com.wanou.project.tool.gen.util.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Service
public class AddressServiceImpl  implements IAddressService {
    @Autowired
    private  AddressMapper addressMapper;
    @Override
    public void addAddress(Address address) {
        //直接从ThreadLocal中拿用户id
        Long user = UserContext.getUser();
        address.setUserId(user);
        addressMapper.insertAddress(address);
    }

    @Override
    public List<Address> getAddress() {
        Long user = UserContext.getUser();
        return addressMapper.selectAddressByUserId(user);
    }

    @Override
    public List<Address> selectAddressListApp(Address address) {
        return addressMapper.selectAddressListApp(address);
    }
}
