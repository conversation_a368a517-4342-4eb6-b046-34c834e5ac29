package com.wanou.project.system.service;

import java.util.List;

import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.BloodPressureWarning;
import com.wanou.project.system.domain.dto.BloodPressureWarningDto;

/**
 * 血压预警Service接口
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
public interface IBloodPressureWarningService {
    /**
     * 查询血压预警
     *
     * @param id 血压预警主键
     * @return 血压预警
     */
    public BloodPressureWarning selectBloodPressureWarningById(Long id);

    /**
     * 查询血压预警列表
     *
     * @param bloodPressureWarning 血压预警
     * @return 血压预警集合
     */
    public TableDataInfo selectBloodPressureWarningList(BloodPressureWarningDto bloodPressureWarning);

    /**
     * 新增血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    public int insertBloodPressureWarning(BloodPressureWarning bloodPressureWarning);

    /**
     * 修改血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    public int updateBloodPressureWarning(BloodPressureWarning bloodPressureWarning);

    /**
     * 批量删除血压预警
     *
     * @param ids 需要删除的血压预警主键集合
     * @return 结果
     */
    public int deleteBloodPressureWarningByIds(Long[] ids);

    /**
     * 删除血压预警信息
     *
     * @param id 血压预警主键
     * @return 结果
     */
    public int deleteBloodPressureWarningById(Long id);
}
