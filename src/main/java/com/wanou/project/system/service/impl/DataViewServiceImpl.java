package com.wanou.project.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.utils.ServletUtils;
import com.wanou.framework.web.page.TableDataInfo;
import com.wanou.project.system.domain.Device;
import com.wanou.project.system.domain.EquipmentAlarmInformation;
import com.wanou.project.system.domain.dto.BloodPressureWarningDto;
import com.wanou.project.system.domain.dto.DataViewDto;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import com.wanou.project.system.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName DataViewServiceImpl
 * @Description 数据大屏Service实现类
 * <AUTHOR>
 * @Date 2024-02-28 10:04
 */
@Service
public class DataViewServiceImpl implements IDataViewService {
    /** 安防设备类型列表 */
    private static final List<Long> safeDeviceTypes = CollUtil.toList(1L);
    /** 健康设备类型列表 */
    private static final List<Long> healthDeviceTypes = CollUtil.toList(3L,5L,7L,8L);
    /** 看护设备类型列表 */
    private static final List<Long> tendDeviceTypes = CollUtil.toList(6L);
    @Resource
    private IElderBaseInfoService elderBaseInfoService;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private IEquipmentAlarmInformationService alarmInfoService;
    @Resource
    private IDeviceElderBindingService deviceElderBindingService;
    @Resource
    private IBloodPressureWarningService bloodPressureWarningService;


    /**
     * @Description 获取数据大屏数据
     * @Param [dataViewDto]
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Override
    public JSONObject getData(DataViewDto dataViewDto) {
        JSONObject result = new JSONObject();
        DateTime nowDate = DateUtil.date();
        //基本统计
        JSONObject baseStatistics = new JSONObject();
        //用户统计
        JSONObject elderBaseData = elderBaseInfoService.dataViewStatistics(dataViewDto.getDeptId());
        baseStatistics.put("elderBaseData",elderBaseData);
        //设备统计
        TableDataInfo deviceTableDataInfo = deviceService.selectDeviceList(new Device());
        //获取设备绑定信息
        DeviceElderBindingDto deviceElderBindingDto = new DeviceElderBindingDto();
        deviceElderBindingDto.setDeptId(dataViewDto.getDeptId());
        List<DeviceElderBindingVo> deviceElderBindingVos = deviceElderBindingService.list(deviceElderBindingDto);
        List<?> deviceRows = deviceTableDataInfo.getRows();
        /*long deviceOnlineCount = deviceRows.stream().filter(deviceItem -> {
            JSONObject fromJson = JSONObject.from(deviceItem);
            return fromJson.getBoolean("isOnLine");
        }).count();*/
        long deviceOnlineCount = deviceElderBindingVos.stream().filter(deviceElderBindingItem -> {
            Object matchDevice = deviceRows.stream().filter(deviceItem -> {
                JSONObject fromJson = JSONObject.from(deviceItem);
                return Objects.equals(fromJson.getLong("id"), deviceElderBindingItem.getDeviceId()) && fromJson.getBoolean("isOnLine");
            }).findFirst().orElse(null);
            return matchDevice != null;
        }).count();
        JSONObject deviceStatisticsData = new JSONObject();
        //deviceStatisticsData.put("deviceTotalCount", deviceRows.size());
        deviceStatisticsData.put("deviceTotalCount", deviceElderBindingVos.size());
        deviceStatisticsData.put("onlineCount",deviceOnlineCount);
        baseStatistics.put("deviceStatistics",deviceStatisticsData);
        result.put("baseStatistics",baseStatistics);

        List<Long> bindDeviceIds = deviceElderBindingVos.stream().map(DeviceElderBindingVo::getDeviceId).collect(Collectors.toList());
        //报警统计
        JSONObject alarmStatistics = new JSONObject();
        List<?> alarmRows = new ArrayList<>();
        List<?> bloodPressureAlarmRows = new ArrayList<>();
        if(CollUtil.isNotEmpty(bindDeviceIds)){
            //有绑定设备 查询设备报警信息
            EquipmentAlarmInformation queryAlarmParams = new EquipmentAlarmInformation();
            queryAlarmParams.setInDeviceIds(bindDeviceIds);
            setOrderByParams("alarm_time", "desc");
            TableDataInfo alarmTableDataInfo = alarmInfoService.selectEquipmentAlarmInformationList(queryAlarmParams);
            alarmRows = alarmTableDataInfo.getRows();

            //血压报警列表
            BloodPressureWarningDto pressureWarningQuery = new BloodPressureWarningDto();
            pressureWarningQuery.setInDeviceIds(bindDeviceIds);
            setOrderByParams("create_time", "desc");
            TableDataInfo bloodPressureWarningList = bloodPressureWarningService.selectBloodPressureWarningList(pressureWarningQuery);
            bloodPressureAlarmRows = bloodPressureWarningList.getRows();
        }
        //安防报警统计
        JSONObject safeAlarm = handleAlarmStatisticsItem(alarmRows, safeDeviceTypes);
        alarmStatistics.put("safeAlarm",safeAlarm);
        //健康报警统计
        JSONObject healthAlarm = handleAlarmStatisticsItem(alarmRows, healthDeviceTypes);
        //血压报警
        handleBloodPressureAlarmStatistics(healthAlarm,bloodPressureAlarmRows);
        //健康预警类型分组
        List<JSONObject> healthTypeAlarmList = getHealthTypeAlarm();
        /*JSONObject matchBloodPressure = healthTypeAlarmList.stream().filter(alarmTypeAlarm -> "血压".equals(alarmTypeAlarm.getString("label")))
                .findFirst().orElse(null);
        if(matchBloodPressure != null){
            matchBloodPressure.put("value",bloodPressureAlarmRows.size());
        }*/
        AtomicInteger bloodSugarCount = new AtomicInteger();
        AtomicInteger uricAcidCount = new AtomicInteger();
        alarmRows.forEach(alarmItem -> {
            JSONObject alarmBean = BeanUtil.toBean(alarmItem, JSONObject.class);
            Long deviceType = alarmBean.getJSONObject("deviceInfo").getLong("deviceType");
            if(deviceType == 8){
                //血糖尿酸
                if(alarmBean.getString("sensorName").contains("血糖")){
                    //血糖
                    bloodSugarCount.getAndIncrement();
                }else if(alarmBean.getString("sensorName").contains("尿酸")){
                    //尿酸
                    uricAcidCount.getAndIncrement();
                }
            }
        });
        List<?> finalBloodPressureAlarmRows = bloodPressureAlarmRows;
        healthTypeAlarmList.forEach(healthTypeAlarmItem -> {
            String label = healthTypeAlarmItem.getString("label");
            if("血糖".equals(label)){
                healthTypeAlarmItem.put("value",bloodSugarCount.get());
            }
            if("尿酸".equals(label)){
                healthTypeAlarmItem.put("value",uricAcidCount.get());
            }
            if("血压".equals(label)){
                healthTypeAlarmItem.put("value", finalBloodPressureAlarmRows.size());
            }
        });
        alarmStatistics.put("healthTypeAlarmList",healthTypeAlarmList);

        alarmStatistics.put("healthAlarm",healthAlarm);
        //安防实时预警
        List<JSONObject> nowSafeAlarms = handleNowAlarm(alarmRows, safeDeviceTypes);
        alarmStatistics.put("nowSafeAlarms",nowSafeAlarms);
        //看护实时预警
        List<JSONObject> nowTendAlarms = handleNowAlarm(alarmRows, tendDeviceTypes);
        alarmStatistics.put("nowTendAlarms",nowTendAlarms);

        result.put("alarmStatistics",alarmStatistics);

        return result;
    }

    private JSONObject handleAlarmStatisticsItem(List<?> alarmRows,List<Long> deviceTypes){
        DateTime nowDate = DateUtil.date();
        JSONObject alarm = new JSONObject(); //报警统计
        List<JSONObject> matchAlarmList = alarmRows.stream().filter(alarmItem -> {
            JSONObject alarmBean = BeanUtil.toBean(alarmItem, JSONObject.class);
            return deviceTypes.contains(alarmBean.getJSONObject("deviceInfo").getLong("deviceType"));
        }).map(alarmItem -> BeanUtil.toBean(alarmItem, JSONObject.class)).collect(Collectors.toList());
        alarm.put("totalAlarmCount",matchAlarmList.size()); //总报警数
        long todayAlarmCount = matchAlarmList.stream().filter(alarmItem -> DateUtil.isSameDay(nowDate, alarmItem.getDate("alarmTime")))
                .count();
        alarm.put("todayAlarmCount",todayAlarmCount); //今日新增报警数
        long alarmType1Count = matchAlarmList.stream().filter(alarmItem -> alarmItem.getIntValue("alarmLevel") == 1)
                .count();
        alarm.put("alarmType1Count",alarmType1Count); //严重报警数
        long alarmType2Count = matchAlarmList.stream().filter(alarmItem -> alarmItem.getIntValue("alarmLevel") == 2)
                .count();
        alarm.put("alarmType2Count",alarmType2Count); //一般报警数

        return alarm;
    }

    private void handleBloodPressureAlarmStatistics(JSONObject alarmStatistics,List<?> rows){
        alarmStatistics.put("totalAlarmCount",alarmStatistics.getIntValue("totalAlarmCount")+rows.size());
        alarmStatistics.put("alarmType1Count",alarmStatistics.getIntValue("alarmType1Count")+rows.size());
        DateTime nowDate = DateUtil.date();
        long todayCount = rows.stream().filter(alarmItem -> {
            JSONObject alarmBean = BeanUtil.toBean(alarmItem, JSONObject.class);
            return DateUtil.isSameDay(nowDate, alarmBean.getDate("detectionTime"));
        }).count();
        alarmStatistics.put("todayAlarmCount",alarmStatistics.getIntValue("todayAlarmCount")+todayCount);
    }

    private List<JSONObject> handleNowAlarm(List<?> alarmRows,List<Long> deviceTypes){
        return alarmRows.stream().filter(alarmItem -> {
            JSONObject alarmBean = BeanUtil.toBean(alarmItem, JSONObject.class);
            return deviceTypes.contains(alarmBean.getJSONObject("deviceInfo").getLong("deviceType"));
        }).map(alarmItem -> {
            JSONObject alarmBean = BeanUtil.toBean(alarmItem, JSONObject.class);
            JSONObject resultJSONObject = new JSONObject();
            resultJSONObject.put("alarmLevel",alarmBean.getIntValue("alarmLevel")==1?"严重":"一般");
            resultJSONObject.put("deviceUuid",alarmBean.getString("deviceUuid"));
            resultJSONObject.put("deviceTypeName",alarmBean.getJSONObject("deviceInfo").getString("deviceTypeName"));
            resultJSONObject.put("id",alarmBean.getLong("id"));
            resultJSONObject.put("triggerName",alarmBean.getString("triggerName"));
            resultJSONObject.put("alarmTime", DateUtil.format(alarmBean.getDate("alarmTime"),"yyyy-MM-dd HH:mm:ss"));

            return resultJSONObject;
        }).collect(Collectors.toList());
    }

    /**
     * 健康预警按类型分组获取
     * @return
     */
    private List<JSONObject> getHealthTypeAlarm(){
        List<JSONObject> list = new ArrayList<>(4);
        JSONObject obj1 = createLabelValueObj("血压", 0);
        list.add(obj1);
        JSONObject obj2 = createLabelValueObj("血糖", 0);
        list.add(obj2);
        /*JSONObject obj3 = createLabelValueObj("心率", 0);
        list.add(obj3);*/
        JSONObject obj4 = createLabelValueObj("尿酸", 0);
        list.add(obj4);
        return list;
    }

    private JSONObject createLabelValueObj(String label,Object value){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("label",label);
        jsonObject.put("value",value);
        return jsonObject;
    }

    private void setOrderByParams(String orderByColumn, String isAsc){
        HttpServletRequest request = ServletUtils.getRequest();
        request.setAttribute("orderByColumn",orderByColumn);
        request.setAttribute("isAsc",isAsc);
    }
}
