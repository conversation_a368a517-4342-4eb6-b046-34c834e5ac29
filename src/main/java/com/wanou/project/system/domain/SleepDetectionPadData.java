package com.wanou.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 睡眠检测垫数据对象 sleep_detection_pad_data
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SleepDetectionPadData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 长者电话 */
    @Excel(name = "长者电话")
    private String elderPhone;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 心率 */
    @Excel(name = "心率")
    private Long heartRate;

    /** 呼吸 */
    @Excel(name = "呼吸")
    private Long breathe;

    /** 状态1=在床2=离床3=打鼾4=体动 */
    @Excel(name = "状态1=在床2=离床3=打鼾4=体动")
    private Long status;

    /** 电池电量 */
    @Excel(name = "电池电量")
    private Long batteryLevel;

    /** 数据上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataUploadTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("elderPhone", getElderPhone())
            .append("deviceId", getDeviceId())
            .append("heartRate", getHeartRate())
            .append("breathe", getBreathe())
            .append("status", getStatus())
            .append("batteryLevel", getBatteryLevel())
            .append("dataUploadTime", getDataUploadTime())
            .toString();
    }
}
