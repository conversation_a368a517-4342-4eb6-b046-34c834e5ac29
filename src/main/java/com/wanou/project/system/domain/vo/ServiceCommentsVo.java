package com.wanou.project.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)

public class ServiceCommentsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer commentId;

    private Long userId;

    private Integer parentId;

    private String content;
   //时间格式转换
   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createdAt;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime updatedAt;

    /**
     * 评论图片
     */
    private String[] pictures;
    //评论用户的头像
    private String avatarUrl;
    //评论用户的昵称
    private String nickName;
    //评论的星级
    private Integer star;
    //评论的回复数量
    private Integer replyCount;


}
