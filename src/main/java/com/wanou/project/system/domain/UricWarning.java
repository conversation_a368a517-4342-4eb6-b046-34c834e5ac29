package com.wanou.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 尿酸预警对象 uric_warning
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UricWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 尿酸值 */
    @Excel(name = "尿酸值")
    private Long uricValues;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date detectionTime;

    /** 预警结果 */
    @Excel(name = "预警结果")
    private Long uricWarningResults;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("deviceId", getDeviceId())
            .append("uricValues", getUricValues())
            .append("detectionTime", getDetectionTime())
            .append("uricWarningResults", getUricWarningResults())
            .toString();
    }
}
