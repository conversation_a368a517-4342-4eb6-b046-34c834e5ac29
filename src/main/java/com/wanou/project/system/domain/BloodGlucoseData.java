package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 血糖数据对象 blood_glucose_data
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BloodGlucoseData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 长者电话 */
    @Excel(name = "长者电话")
    private String elderPhone;

    /** 长者电话 */
    @Excel(name = "长者姓名")
    private String elderName;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 血糖值 */
    @Excel(name = "血糖值")
    private BigDecimal glucose;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date detectionTime;

    /** 检测结果1=正常血糖2=血糖偏高 */
    @Excel(name = "检测结果1=正常血糖2=血糖偏高")
    private Long detectionResults;

    private String deviceName;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("elderPhone", getElderPhone())
            .append("deviceId", getDeviceId())
            .append("glucose", getGlucose())
            .append("detectionTime", getDetectionTime())
            .append("detectionResults", getDetectionResults())
            .toString();
    }
}
