package com.wanou.project.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 设备图片对象 device_img
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceImg extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备图片 */
    @Excel(name = "设备图片")
    private String deviceImg;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createPy;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceName", getDeviceName())
            .append("deviceImg", getDeviceImg())
            .append("createTime", getCreateTime())
            .append("createPy", getCreatePy())
            .toString();
    }
}
