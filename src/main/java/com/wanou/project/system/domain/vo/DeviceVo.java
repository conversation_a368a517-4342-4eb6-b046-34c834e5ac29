package com.wanou.project.system.domain.vo;

import lombok.Data;

import java.sql.Date;
import java.util.List;
import java.util.Map;
@Data
public class DeviceVo {
    private String address;
    private Integer bindingStatus;
    private String createBy;
    private Date createTime;
    private Integer dataJoinPeriod;
    private Integer dataJoinType;
    private Integer delFlag;
    private Long deptId;
    private String deptName;
    private Long deviceImageId;
    private String deviceImageUrl;
    private String deviceName;
    private Integer deviceType;
    private String deviceTypeName;  
    private String deviceUuid;
    private Integer heartbeatPeriod;
    private Long id;
    private Integer isAlarm;
    private Boolean isOnLine;
    private Double latitude;
    private String location;
    private Double longitude;
    private String ownerPhone;
    private List<String> ownerPhoneArr;
    private Map<String, Object> params;
    private String protocol;
    private String remark;
    private String searchValue;
    private String updateBy;
    private Date updateTime;
    private String belong;

}
