package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 尿酸数据对象 uric_data
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UricData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 长者电话 */
    @Excel(name = "长者电话")
    private String elderPhone;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date detectionTime;

    /** 尿酸值 */
    @Excel(name = "尿酸值")
    private BigDecimal uricValues;

    /** 尿酸检测结果 */
    @Excel(name = "尿酸检测结果")
    private Long uricTestResults;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("elderPhone", getElderPhone())
            .append("deviceId", getDeviceId())
            .append("detectionTime", getDetectionTime())
            .append("uricValues", getUricValues())
            .append("uricTestResults", getUricTestResults())
            .toString();
    }
}
