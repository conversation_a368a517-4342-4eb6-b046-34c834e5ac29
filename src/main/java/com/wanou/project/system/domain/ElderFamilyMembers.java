package com.wanou.project.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 长者家属信息对象 elder_family_members
 *
 * <AUTHOR>
 * @date 2024-01-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ElderFamilyMembers extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 家属id */
    private Long id;

    /** 关联长者 */
    @Excel(name = "关联长者")
    private Long elderId;

    /** 与长者关系 */
    @Excel(name = "与长者关系")
    private String relationship;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别 1=男 2=女 */
    @Excel(name = "性别 1=男 2=女")
    private Long sex;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 居中地址 */
    @Excel(name = "居中地址")
    private String address;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("relationship", getRelationship())
            .append("name", getName())
            .append("sex", getSex())
            .append("phone", getPhone())
            .append("address", getAddress())
            .toString();
    }
}
