package com.wanou.project.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 商品评价对象 comments
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("comments")
public class Comments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评价id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 父级评论 */
    @Excel(name = "父级评论")
    private String adminComment;

    /** 商品id */
    @Excel(name = "商品id")
    private Long goodsId;

    /** 商品类型 */
    @Excel(name = "商品类型")
    private Integer goodsType;

    /** 评价等级 */
    @Excel(name = "评价等级")
    private Integer rating;

    /** 评价内容 */
    @Excel(name = "评价内容")
    private String comment;

    /**
     * 是否含有图片（0无图，1有图）
     */
    private Boolean hasPicture;

    /**
     * 图片地址列表，采用JSON数组格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String[] picUrls;

    /** 评论用户的头像 */
    private String avatarUrl;
    /** 评论用户的昵称 */
    private String nickName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateAt;

    /** 逻辑删除，0未删除，1删除 */
    @Excel(name = "逻辑删除，0未删除，1删除")
    private Boolean isDel;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("parentId", getAdminComment())
            .append("goodsId", getGoodsId())
            .append("goodsType", getGoodsType())
            .append("rating", getRating())
            .append("comment", getComment())
            .append("createAt", getCreateAt())
            .append("updateAt", getUpdateAt())
            .append("isDel", getIsDel())
            .toString();
    }
}
