package com.wanou.project.system.domain.dto;

import com.wanou.project.system.domain.DeviceElderBinding;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @ClassName DeviceElderBindingDto
 * @Description 设备-长者绑定DTO
 * <AUTHOR>
 * @Date 2024-02-28 9:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceElderBindingDto extends DeviceElderBinding {
    /** 查询开始时间 */
    private Date queryStartTime;

    /** 查询结束时间 */
    private Date queryEndTime;
}
