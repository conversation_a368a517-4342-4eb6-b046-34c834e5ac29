package com.wanou.project.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 设备基本信息对象 device
 *
 * <AUTHOR>
 * @date 2024-02-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Device extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 绑定长者 */
    @Excel(name = "绑定长者")
    private Long elderId;

    /** 设备sn */
    @Excel(name = "设备sn")
    private String deviceSn;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备类型1=血糖测量仪2=血压测量仪3=sos报警设备4=睡眠检测垫5手环 */
    @Excel(name = "设备类型1=血糖测量仪2=血压测量仪3=sos报警设备4=睡眠检测垫5手环")
    private Long deviceType;

    /** 报警推送方式1=微信推送2=短信推送3=电话推送 */
    @Excel(name = "报警推送方式1=微信推送2=短信推送3=电话推送")
    private String alarmPushType;

    private String alarmPushTypeLabel;

    /** 设备图片 */
    @Excel(name = "设备图片")
    private Long deviceImg;

    /** 绑定状态1=已绑定2=未绑定 */
    @Excel(name = "绑定状态1=已绑定2=未绑定")
    private Long bindingStatus;

    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date bindingTime;

    /** 在线状态1=在线2=离线 */
    @Excel(name = "在线状态")
    private Boolean isOnline;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createPerson;

    /** 删除状态 */
    @Excel(name = "删除状态")
    private Long deleteStatus;

    /** 删除人 */
    @Excel(name = "删除人")
    private String deletePerson;

    private List<Long> alarmPushTypeValue;
}
