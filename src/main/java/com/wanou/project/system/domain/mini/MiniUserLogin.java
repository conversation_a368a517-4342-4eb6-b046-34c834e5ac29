package com.wanou.project.system.domain.mini;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/9 11:42
 * 小程序用户登录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiniUserLogin implements Serializable {
    private static final long serialVersionUID = 7216027472176563913L;

    private String code;
    private String username;
    private String password;
    private Long miniUserId;
}
