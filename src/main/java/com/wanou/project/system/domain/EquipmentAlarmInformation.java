package com.wanou.project.system.domain;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 设备报警信息对象 equipment_alarm_information
 *
 * <AUTHOR>
 * @date 2024-02-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EquipmentAlarmInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 报警设备id */
    @Excel(name = "报警设备id")
    private Long deviceId;

    /** 长者id */
    @Excel(name = "长者id")
    private Long elderId;

    /** 长者姓名 */
    @Excel(name = "长者姓名")
    private String elderName;

    /** 长者电话 */
    @Excel(name = "长者电话")
    private String elderPhone;

    /** 报警原因 */
    @Excel(name = "报警原因")
    private String reasonForAlarm;

    /** 报警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @Excel(name = "报警时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date alarmTime;

    /** 信息是否推送 */
    @Excel(name = "信息是否推送")
    private Long isInformationPush;

    /** 是否处理 */
    @Excel(name = "是否处理")
    private Long isHandle;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String handleResults;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handelTime;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handelBy;

    /** 查询开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 查询结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private List<Long> inDeviceIds;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("elderId", getElderId())
            .append("elderName", getElderName())
            .append("elderPhone", getElderPhone())
            .append("reasonForAlarm", getReasonForAlarm())
            .append("alarmTime", getAlarmTime())
            .append("isInformationPush", getIsInformationPush())
            .append("isHandle", getIsHandle())
            .append("handleResults", getHandleResults())
            .append("handelTime", getHandelTime())
            .append("handelBy", getHandelBy())
            .toString();
    }
}
