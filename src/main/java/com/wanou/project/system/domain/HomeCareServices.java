package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 家庭护理服务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HomeCareServices implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增且唯一
     */
    private Integer id;

    /**
     * 项目ID，非空
     */
    private Integer productCategoryId;

    /**
     * 服务名称，不能为空且唯一
     */
    private String name;

    /**
     * 状态（0禁用、1启用）
     */
    private Integer status;

    /**
     * 服务价格
     */
    private BigDecimal price;

    /**
     * 有效期单位月
     */
    private Integer validDuration;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    private  String picture;


}
