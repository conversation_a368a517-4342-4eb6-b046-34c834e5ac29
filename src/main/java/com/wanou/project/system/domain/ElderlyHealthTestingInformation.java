package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 长者健康信息检测对象 elderly_health_testing_information
 *
 * <AUTHOR>
 * @date 2024-02-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ElderlyHealthTestingInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 长者id */
    @Excel(name = "长者id")
    private Long elderId;

    /** 长者姓名 */
    @Excel(name = "长者姓名")
    private String elderName;

    /** 血压检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "血压检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bloodPressureTime;

    /** 收缩压 */
    @Excel(name = "收缩压")
    private BigDecimal systolicPressure;

    /** 舒张压 */
    @Excel(name = "舒张压")
    private BigDecimal diastolicPressure;

    /** 心率 */
    @Excel(name = "心率")
    private Long heartRate;

    /** 血氧饱和度 */
    @Excel(name = "血氧饱和度")
    private String bloodOxygenSaturation;

    /** 血压测量结果 */
    @Excel(name = "血压测量结果")
    private String bloodPressureResults;

    /** 血糖检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "血糖检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bloodSugarTime;

    /** 血糖检测值 */
    @Excel(name = "血糖检测值")
    private BigDecimal bloodGlucoseTestingValue;

    /** 血糖检测结果 */
    @Excel(name = "血糖检测结果")
    private String bloodGlucoseTestingResults;

    /** 尿酸检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "尿酸检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uricAcidTestingTime;

    /** 尿酸检测值 */
    @Excel(name = "尿酸检测值")
    private BigDecimal uricAcidTestingValue;

    /** 尿酸检测结果 */
    @Excel(name = "尿酸检测结果")
    private String uricAcidTestingResults;

    /** 体温检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "体温检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bodyTemperatureTime;

    /** 体温检测值 */
    @Excel(name = "体温检测值")
    private BigDecimal bodyTemperatureValue;

    /** 睡眠分层 */
    @Excel(name = "睡眠分层")
    private String sleepStratification;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("elderId", getElderId())
            .append("elderName", getElderName())
            .append("bloodPressureTime", getBloodPressureTime())
            .append("systolicPressure", getSystolicPressure())
            .append("diastolicPressure", getDiastolicPressure())
            .append("heartRate", getHeartRate())
            .append("bloodOxygenSaturation", getBloodOxygenSaturation())
            .append("bloodPressureResults", getBloodPressureResults())
            .append("bloodSugarTime", getBloodSugarTime())
            .append("bloodGlucoseTestingValue", getBloodGlucoseTestingValue())
            .append("bloodGlucoseTestingResults", getBloodGlucoseTestingResults())
            .append("uricAcidTestingTime", getUricAcidTestingTime())
            .append("uricAcidTestingValue", getUricAcidTestingValue())
            .append("uricAcidTestingResults", getUricAcidTestingResults())
            .append("bodyTemperatureTime", getBodyTemperatureTime())
            .append("bodyTemperatureValue", getBodyTemperatureValue())
            .append("sleepStratification", getSleepStratification())
            .toString();
    }
}
