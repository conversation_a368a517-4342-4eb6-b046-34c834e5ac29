package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DeviceStore implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer productCategoryId;

    private String deviceName;

    private BigDecimal price;

    private String unit;

    private String description;

    private  String deviceImageUrl;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String createdBy;

    private String updatedBy;

    private Integer isAvailable;


}
