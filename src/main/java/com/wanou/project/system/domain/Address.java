package com.wanou.project.system.domain;

import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Address implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String recipient;

    private String phoneNumber;

    private String address;

    private String houseNumber;

    private Integer addressType;


}
