package com.wanou.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 血压预警对象 blood_pressure_warning
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BloodPressureWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 高压 */
    @Excel(name = "高压")
    private Long highPressure;

    /** 低压 */
    @Excel(name = "低压")
    private Long lowPressure;

    /** 脉搏 */
    @Excel(name = "脉搏")
    private Long pulse;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date detectionTime;

    /** 预警结果 1=轻度 2=中高 3=重度 */
    @Excel(name = "预警结果 1=轻度 2=中高 3=重度")
    private Long warningResults;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("deviceId", getDeviceId())
            .append("highPressure", getHighPressure())
            .append("lowPressure", getLowPressure())
            .append("pulse", getPulse())
            .append("detectionTime", getDetectionTime())
            .append("warningResults", getWarningResults())
            .toString();
    }
}
