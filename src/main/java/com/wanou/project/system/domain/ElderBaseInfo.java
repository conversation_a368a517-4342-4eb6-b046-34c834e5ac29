package com.wanou.project.system.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 长者基本信息对象 elder_base_info
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ElderBaseInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别 */
    @Excel(name = "性别")
    private Long sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private Long age;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private Long idCardType;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 区/县 */
    @Excel(name = "区/县")
    private String county;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 居住情况 */
    @Excel(name = "居住情况")
    private Long liveState;

    /** 是否删除 */
    private Integer isDel;

    /** 删除人 */
    private String delBy;

    /**部门id**/
    private Long deptId;
}
