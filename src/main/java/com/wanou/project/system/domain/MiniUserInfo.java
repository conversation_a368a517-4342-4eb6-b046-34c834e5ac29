package com.wanou.project.system.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MiniUserInfo implements Serializable {
    private static final long serialVersionUID = -2703800483252098097L;

    private Long id;
    private String openId;
    private String unionId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    private Date regTime;
    private Integer status;
    private String nickName;
    private String avatarUrl;
    private Integer age;
    private String sex;
    private String birthday;
    private Integer elderId;
    private String elderName;
    //前端传来的性别提示0或者1
    private Integer gender;
    //老人手机号码
    private String elderPhone;
    //老人省份证
    private String elderIdCard;


}
