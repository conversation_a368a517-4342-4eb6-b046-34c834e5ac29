package com.wanou.project.system.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ProductCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long parentId;

    private String categoryName;

    private Integer categoryLevel;

    private Integer isAvailable;

    private Integer sort;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;

    private String picture;

    private String keywords;

    private String remark;

    private Boolean isDel;

    @Override
    public String toString() {
        return "ProductCategory{" +
                "id=" + id +
                ", parentId=" + parentId +
                ", categoryName='" + categoryName + '\'' +
                ", categoryLevel=" + categoryLevel +
                ", isAvailable=" + isAvailable +
                ", sort=" + sort +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", picture='" + picture + '\'' +
                ", keywords='" + keywords + '\'' +
                ", remark='" + remark + '\'' +
                ", isDel=" + isDel +
                '}';
    }
}