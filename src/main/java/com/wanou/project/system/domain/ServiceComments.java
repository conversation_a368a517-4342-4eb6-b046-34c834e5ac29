package com.wanou.project.system.domain;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ServiceComments implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer commentId;

    private Long userId;

    private Integer parentId;

    private String content;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * 评论图片
     */
    private String picture;
    //评论用户的头像
    private String avatarUrl;
    //评论用户的昵称
    private String nickName;
    //评论的星级
    private Integer star;
    //评论的回复数量
    private Integer replyCount;


}
