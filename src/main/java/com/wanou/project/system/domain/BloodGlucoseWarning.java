package com.wanou.project.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wanou.framework.aspectj.lang.annotation.Excel;
import com.wanou.framework.web.domain.BaseEntity;

/**
 * 血糖预警对象 blood_glucose_warning
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BloodGlucoseWarning extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属长者 */
    @Excel(name = "所属长者")
    private Long elderId;

    /** 长者电话 */
    @Excel(name = "长者电话")
    private String elderPhone;

    /** 所属设备 */
    @Excel(name = "所属设备")
    private Long deviceId;

    /** 血糖值 */
    @Excel(name = "血糖值")
    private BigDecimal glucose;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date detectionTime;

    /** 预警结果 1=血糖偏高 2=高血糖 */
    @Excel(name = "预警结果 1=血糖偏高 2=高血糖")
    private Long warningResults;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("elderId", getElderId())
            .append("deviceId", getDeviceId())
            .append("glucose", getGlucose())
            .append("detectionTime", getDetectionTime())
            .append("warningResults", getWarningResults())
            .toString();
    }
}
