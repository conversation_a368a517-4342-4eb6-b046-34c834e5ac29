package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.DeviceImg;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备图片Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Mapper
public interface DeviceImgMapper {
    /**
     * 查询设备图片
     *
     * @param id 设备图片主键
     * @return 设备图片
     */
    public DeviceImg selectDeviceImgById(Long id);

    /**
     * 查询设备图片列表
     *
     * @param deviceImg 设备图片
     * @return 设备图片集合
     */
    public List<DeviceImg> selectDeviceImgList(DeviceImg deviceImg);

    /**
     * 新增设备图片
     *
     * @param deviceImg 设备图片
     * @return 结果
     */
    public int insertDeviceImg(DeviceImg deviceImg);

    /**
     * 修改设备图片
     *
     * @param deviceImg 设备图片
     * @return 结果
     */
    public int updateDeviceImg(DeviceImg deviceImg);

    /**
     * 删除设备图片
     *
     * @param id 设备图片主键
     * @return 结果
     */
    public int deleteDeviceImgById(Long id);

    /**
     * 批量删除设备图片
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceImgByIds(Long[] ids);
}
