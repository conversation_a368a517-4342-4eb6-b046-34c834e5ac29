package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.ProductAttributeCategory;

import java.util.List;

public interface ProductAttributeCategoryMapper {

    List<ProductAttributeCategory> selectProductAttributeCategory(ProductAttributeCategory productAttributeCategory);

    ProductAttributeCategory selectById(Long id);

    int insertProductAttributeCategory(ProductAttributeCategory row);

    int updateById(ProductAttributeCategory row);

    int deleteById(Long id);

}