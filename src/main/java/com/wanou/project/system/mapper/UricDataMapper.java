package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.UricData;
import org.apache.ibatis.annotations.Mapper;

/**
 * 尿酸数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
@Mapper
public interface UricDataMapper {
    /**
     * 查询尿酸数据
     *
     * @param id 尿酸数据主键
     * @return 尿酸数据
     */
    public UricData selectUricDataById(Long id);

    /**
     * 查询尿酸数据列表
     *
     * @param uricData 尿酸数据
     * @return 尿酸数据集合
     */
    public List<UricData> selectUricDataList(UricData uricData);

    /**
     * 新增尿酸数据
     *
     * @param uricData 尿酸数据
     * @return 结果
     */
    public int insertUricData(UricData uricData);

    /**
     * 修改尿酸数据
     *
     * @param uricData 尿酸数据
     * @return 结果
     */
    public int updateUricData(UricData uricData);

    /**
     * 删除尿酸数据
     *
     * @param id 尿酸数据主键
     * @return 结果
     */
    public int deleteUricDataById(Long id);

    /**
     * 批量删除尿酸数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUricDataByIds(Long[] ids);
}
