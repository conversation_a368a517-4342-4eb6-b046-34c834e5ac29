package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.BloodGlucoseWarning;
import org.apache.ibatis.annotations.Mapper;

/**
 * 血糖预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-18
 */
@Mapper
public interface BloodGlucoseWarningMapper {
    /**
     * 查询血糖预警
     *
     * @param id 血糖预警主键
     * @return 血糖预警
     */
    public BloodGlucoseWarning selectBloodGlucoseWarningById(Long id);

    /**
     * 查询血糖预警列表
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 血糖预警集合
     */
    public List<BloodGlucoseWarning> selectBloodGlucoseWarningList(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 新增血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    public int insertBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 修改血糖预警
     *
     * @param bloodGlucoseWarning 血糖预警
     * @return 结果
     */
    public int updateBloodGlucoseWarning(BloodGlucoseWarning bloodGlucoseWarning);

    /**
     * 删除血糖预警
     *
     * @param id 血糖预警主键
     * @return 结果
     */
    public int deleteBloodGlucoseWarningById(Long id);

    /**
     * 批量删除血糖预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBloodGlucoseWarningByIds(Long[] ids);
}
