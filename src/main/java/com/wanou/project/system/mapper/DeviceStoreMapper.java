package com.wanou.project.system.mapper;

import com.wanou.framework.web.page.ShopPage;
import com.wanou.project.system.domain.DeviceStore;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Mapper
public interface DeviceStoreMapper {

    List<DeviceStore> listDevice( );

    DeviceStore getDeviceById(Long id);

    /**
     * 查询设备商城列表
     *
     * @param deviceStore 设备商城
     * @return 设备商城集合
     */
    public List<DeviceStore> selectDeviceStoreList(DeviceStore deviceStore);

    /**
     * 设备商城新增
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    public int insertDeviceStore(DeviceStore deviceStore);

    /**
     * 设备商城修改
     *
     * @param deviceStore 设备商城
     * @return 结果
     */
    public int updateDeviceStore(DeviceStore deviceStore);

    /**
     * 设备商城删除
     *
     * @param id 设备商城主键
     * @return 结果
     */
    public int deleteDeviceStoreById(Long id);

    /**
     * 设备商城批量删除
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceStoreByIds(Long[] ids);

    List<DeviceStore> listCategroy(String id);
}
