package com.wanou.project.system.mapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.date.DateTime;

import com.alibaba.fastjson2.JSONObject;
import com.wanou.project.system.domain.ElderBaseInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 长者基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface ElderBaseInfoMapper
{
    /**
     * 查询长者基本信息
     *
     * @param id 长者基本信息主键
     * @return 长者基本信息
     */
    public ElderBaseInfo selectElderBaseInfoById(Long id);

    /**
     * 查询长者基本信息列表
     *
     * @param elderBaseInfo 长者基本信息
     * @return 长者基本信息集合
     */
    public List<ElderBaseInfo> selectElderBaseInfoList(ElderBaseInfo elderBaseInfo);

    /**
     * 新增长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    public int insertElderBaseInfo(ElderBaseInfo elderBaseInfo);

    /**
     * 修改长者基本信息
     *
     * @param elderBaseInfo 长者基本信息
     * @return 结果
     */
    public int updateElderBaseInfo(ElderBaseInfo elderBaseInfo);

    /**
     * 删除长者基本信息
     *
     * @param id 长者基本信息主键
     * @return 结果
     */
    public int deleteElderBaseInfoById(Long id);

    /**
     * 批量删除长者基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteElderBaseInfoByIds(Long[] ids);

    JSONObject totalElderData(@Param("startOfWeek") DateTime startOfWeek, @Param("endOfWeek") DateTime endOfWeek,
                              @Param("startOfMonth") DateTime startOfMonth, @Param("endOfMonth") DateTime endOfMonth);

    /**
     * 查询长者性别分布
     * @return
     */
    public List<JSONObject> totalElderBySex(Long deptId);

    public List<ElderBaseInfo> selectColumns(List<String> columns, Long deptId);

    JSONObject dataViewStatistics(Long deptId);
}
