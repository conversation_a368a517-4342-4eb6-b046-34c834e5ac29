package com.wanou.project.system.mapper;


import com.wanou.project.system.domain.MiniUserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MiniUserInfoMapper {
   public MiniUserInfo selectMiniUserInfoByOpenId(String openid);

   int insert(MiniUserInfo miniUserInfo);

    void update(MiniUserInfo miniUserInfo);

    List<MiniUserInfo> selectByDeptIds(@Param("deptIdArr") List<Long> deptIdArr);

    MiniUserInfo selectByUserId(String userId);

    void updateById(MiniUserInfo miniUserInfo);

    void updateMiniUserInfo(MiniUserInfo miniUserInfo);

    List<String> selectList();
}
