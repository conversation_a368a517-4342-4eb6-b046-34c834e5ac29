package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.HomeCareServices;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 家庭护理服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Mapper
public interface HomeCareServicesMapper  {

    List<HomeCareServices> getServiceByProjectId(String id);

    HomeCareServices getServiceById(String id);

    /**
     * 查询家庭护理服务
     *
     * @param id 家庭护理服务主键
     * @return 家庭护理服务
     */
    public HomeCareServices selectHomeCareServicesById(Long id);

    /**
     * 查询家庭护理服务列表
     *
     * @param homeCareServices 家庭护理服务
     * @return 家庭护理服务集合
     */
    public List<HomeCareServices> selectHomeCareServicesList(HomeCareServices homeCareServices);

    /**
     * 新增家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    public int insertHomeCareServices(HomeCareServices homeCareServices);

    /**
     * 修改家庭护理服务
     *
     * @param homeCareServices 家庭护理服务
     * @return 结果
     */
    public int updateHomeCareServices(HomeCareServices homeCareServices);

    /**
     * 删除家庭护理服务
     *
     * @param id 家庭护理服务主键
     * @return 结果
     */
    public int deleteHomeCareServicesById(Long id);

    /**
     * 批量删除家庭护理服务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeCareServicesByIds(Long[] ids);
}
