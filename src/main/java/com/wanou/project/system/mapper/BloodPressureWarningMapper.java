package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.BloodPressureWarning;
import org.apache.ibatis.annotations.Mapper;

/**
 * 血压预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
@Mapper
public interface BloodPressureWarningMapper {
    /**
     * 查询血压预警
     *
     * @param id 血压预警主键
     * @return 血压预警
     */
    public BloodPressureWarning selectBloodPressureWarningById(Long id);

    /**
     * 查询血压预警列表
     *
     * @param bloodPressureWarning 血压预警
     * @return 血压预警集合
     */
    public List<BloodPressureWarning> selectBloodPressureWarningList(BloodPressureWarning bloodPressureWarning);

    /**
     * 新增血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    public int insertBloodPressureWarning(BloodPressureWarning bloodPressureWarning);

    /**
     * 修改血压预警
     *
     * @param bloodPressureWarning 血压预警
     * @return 结果
     */
    public int updateBloodPressureWarning(BloodPressureWarning bloodPressureWarning);

    /**
     * 删除血压预警
     *
     * @param id 血压预警主键
     * @return 结果
     */
    public int deleteBloodPressureWarningById(Long id);

    /**
     * 批量删除血压预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBloodPressureWarningByIds(Long[] ids);
}
