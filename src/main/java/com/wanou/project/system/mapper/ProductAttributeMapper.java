package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.ProductAttribute;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductAttributeMapper {

    int deleteById(ProductAttribute productAttribute);

    int insertProductAttribute(ProductAttribute productAttribute);

    List<ProductAttribute> selectProductAttribute(ProductAttribute productAttribute);

    ProductAttribute selectById(Long id);

    int updateById(ProductAttribute productAttribute);

    List<ProductAttribute> getProductAttrInfo(@Param("id") Long productCategoryId);
}