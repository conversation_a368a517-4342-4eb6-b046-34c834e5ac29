package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.Orders;
import com.wanou.project.system.domain.dto.OrderDeliveryParam;
import com.wanou.project.system.domain.dto.OrderDetail;
import com.wanou.project.system.domain.dto.OrderQueryParam;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 订单查询自定义
 * Created by djf on 2025/2/10.
 */

public interface OrderDao {
    /**
     * 条件查询订单
     */
    List<Orders> getList(@Param("queryParam") OrderQueryParam queryParam);

    /**
     * 批量发货
     */
    int delivery(@Param("list") List<OrderDeliveryParam> deliveryParamList);

    /**
     * 获取订单详情
     */
    OrderDetail getDetail(@Param("id") Long id);
}
