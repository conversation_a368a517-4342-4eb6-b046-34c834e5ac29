package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.ProductAttributeValue;
import java.util.List;

public interface ProductAttributeValueMapper {

    int deleteById(Long id);

    int insertProductAttributeValue(ProductAttributeValue productAttributeValue);

    List<ProductAttributeValue> selectProductAttributeValue(ProductAttributeValue productAttributeValue);

    ProductAttributeValue selectById(Long id);

    int updateById(ProductAttributeValue productAttributeValue);
}