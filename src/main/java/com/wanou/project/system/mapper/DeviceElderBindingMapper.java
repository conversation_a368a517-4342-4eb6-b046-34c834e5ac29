package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.DeviceElderBinding;
import com.wanou.project.system.domain.dto.DeviceElderBindingDto;
import com.wanou.project.system.domain.dto.UnBingDto;
import com.wanou.project.system.domain.vo.DeviceElderBindingVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户绑定Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Mapper
public interface DeviceElderBindingMapper {
    /**
     * 查询用户绑定
     *
     * @param id 用户绑定主键
     * @return 用户绑定
     */
    public DeviceElderBinding selectDeviceElderBindingById(Long id);

    /**
     * 查询用户绑定列表
     *
     * @param deviceElderBinding 用户绑定
     * @return 用户绑定集合
     */
    public List<DeviceElderBindingVo> selectDeviceElderBindingList(DeviceElderBindingDto deviceElderBinding);

    /**
     * 新增用户绑定
     *
     * @param deviceElderBinding 用户绑定
     * @return 结果
     */
    public int insertDeviceElderBinding(DeviceElderBinding deviceElderBinding);

    /**
     * 修改用户绑定
     *
     * @param deviceElderBinding 用户绑定
     * @return 结果
     */
    public int updateDeviceElderBinding(DeviceElderBinding deviceElderBinding);

    /**
     * 删除用户绑定
     *
     * @param id 用户绑定主键
     * @return 结果
     */
    public int deleteDeviceElderBindingById(Long id);

    /**
     * 批量删除用户绑定
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceElderBindingByIds(Long[] ids);

    List<DeviceElderBindingVo> selectDeviceElderBindingListByIds(List<Long> deviceIds);

    DeviceElderBindingVo selectDeviceElderBindingByDeviceId(Long id);

    public int  deleteDeviceElderBindingByDeviceId(Long id);

    List<DeviceElderBinding> selectDeviceElderBindingByElderId(Integer elderId);

    void unbind(UnBingDto unBingDto);
}
