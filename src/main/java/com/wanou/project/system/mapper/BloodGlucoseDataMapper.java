package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.BloodGlucoseData;
import org.apache.ibatis.annotations.Mapper;

/**
 * 血糖数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Mapper
public interface BloodGlucoseDataMapper {
    /**
     * 查询血糖数据
     *
     * @param id 血糖数据主键
     * @return 血糖数据
     */
    public BloodGlucoseData selectBloodGlucoseDataById(Long id);

    /**
     * 查询血糖数据列表
     *
     * @param bloodGlucoseData 血糖数据
     * @return 血糖数据集合
     */
    public List<BloodGlucoseData> selectBloodGlucoseDataList(BloodGlucoseData bloodGlucoseData);

    /**
     * 新增血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    public int insertBloodGlucoseData(BloodGlucoseData bloodGlucoseData);

    /**
     * 修改血糖数据
     *
     * @param bloodGlucoseData 血糖数据
     * @return 结果
     */
    public int updateBloodGlucoseData(BloodGlucoseData bloodGlucoseData);

    /**
     * 删除血糖数据
     *
     * @param id 血糖数据主键
     * @return 结果
     */
    public int deleteBloodGlucoseDataById(Long id);

    /**
     * 批量删除血糖数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBloodGlucoseDataByIds(Long[] ids);
}
