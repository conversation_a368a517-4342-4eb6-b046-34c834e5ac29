package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.Address;
import com.wanou.project.system.domain.Comments;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Mapper
public interface AddressMapper  {

    void insertAddress(Address address);

    List<Address> selectAddressByUserId(Long user);

    List<Address> selectAddressListApp(Address address);
}
