package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.EquipmentAlarmInformation;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备报警信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-17
 */
@Mapper
public interface EquipmentAlarmInformationMapper {
    /**
     * 查询设备报警信息
     *
     * @param id 设备报警信息主键
     * @return 设备报警信息
     */
    public EquipmentAlarmInformation selectEquipmentAlarmInformationById(Long id);

    /**
     * 查询设备报警信息列表
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 设备报警信息集合
     */
    public List<EquipmentAlarmInformation> selectEquipmentAlarmInformationList(EquipmentAlarmInformation equipmentAlarmInformation);

    /**
     * 新增设备报警信息
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 结果
     */
    public int insertEquipmentAlarmInformation(EquipmentAlarmInformation equipmentAlarmInformation);

    /**
     * 修改设备报警信息
     *
     * @param equipmentAlarmInformation 设备报警信息
     * @return 结果
     */
    public int disposeEquipmentAlarmInformation(EquipmentAlarmInformation equipmentAlarmInformation);

    /**
     * 删除设备报警信息
     *
     * @param id 设备报警信息主键
     * @return 结果
     */
    public int deleteEquipmentAlarmInformationById(Long id);

    /**
     * 批量删除设备报警信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEquipmentAlarmInformationByIds(Long[] ids);
}
