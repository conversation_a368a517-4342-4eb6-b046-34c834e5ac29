package com.wanou.project.system.mapper;

import java.util.List;
import com.wanou.project.system.domain.UricWarning;
import org.apache.ibatis.annotations.Mapper;

/**
 * 尿酸预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@Mapper
public interface UricWarningMapper {
    /**
     * 查询尿酸预警
     *
     * @param id 尿酸预警主键
     * @return 尿酸预警
     */
    public UricWarning selectUricWarningById(Long id);

    /**
     * 查询尿酸预警列表
     *
     * @param uricWarning 尿酸预警
     * @return 尿酸预警集合
     */
    public List<UricWarning> selectUricWarningList(UricWarning uricWarning);

    /**
     * 新增尿酸预警
     *
     * @param uricWarning 尿酸预警
     * @return 结果
     */
    public int insertUricWarning(UricWarning uricWarning);

    /**
     * 修改尿酸预警
     *
     * @param uricWarning 尿酸预警
     * @return 结果
     */
    public int updateUricWarning(UricWarning uricWarning);

    /**
     * 删除尿酸预警
     *
     * @param id 尿酸预警主键
     * @return 结果
     */
    public int deleteUricWarningById(Long id);

    /**
     * 批量删除尿酸预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUricWarningByIds(Long[] ids);
}
