package com.wanou.project.system.mapper;

import com.wanou.project.system.domain.ProductCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCategoryMapper {

    List<ProductCategory> selectProductCategoryListApp(ProductCategory productCategory);

    List<ProductCategory> getList(ProductCategory productCategory);

    ProductCategory selectById(Long id);

    int insertProductCategory(ProductCategory productCategory);

    int updateProductCategory(ProductCategory productCategory);

    int updateIsAvailable(@Param("ids") List<Long> ids, @Param("isAvailable") Integer isAvailable);

    int deleteProductCategory(Long id);
}