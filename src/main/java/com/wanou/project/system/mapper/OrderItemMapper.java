package com.wanou.project.system.mapper;


import com.wanou.project.system.domain.OrderItem;
import com.wanou.project.system.domain.mbg.OrderItemExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderItemMapper {
    long countByExample(OrderItemExample example);

    int deleteByExample(OrderItemExample example);

    int deleteByPrimaryKey(Long id);

    int insert(OrderItem record);

    int insertList(List<OrderItem> records);
    int insertSelective(OrderItem record);

    List<OrderItem> selectByOrderIds(List<Long> orderIds);

    List<OrderItem> selectByOrderId(@Param("orderId") Long orderId);

    OrderItem selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") OrderItem record, @Param("example") OrderItemExample example);

    int updateByExample(@Param("record") OrderItem record, @Param("example") OrderItemExample example);

    int updateByPrimaryKeySelective(OrderItem record);

    int updateByPrimaryKey(OrderItem record);

    int releaseStockBySkuId(@Param("productSkuId")Long productSkuId,@Param("quantity") Integer quantity);
}
