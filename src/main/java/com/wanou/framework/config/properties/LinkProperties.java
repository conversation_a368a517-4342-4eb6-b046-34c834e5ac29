package com.wanou.framework.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName LinkProperties
 * @Description 物联网平台配置类
 * <AUTHOR>
 * @Date 2024-02-23 14:11
 */
@Configuration
@ConfigurationProperties(prefix = "link")
@Data
public class LinkProperties {
    private String url;
    private String username;
    private String password;
    private String uuid;
    private String deptId;
}
