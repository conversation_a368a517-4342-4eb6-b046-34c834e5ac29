package com.wanou.framework.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.wanou.framework.config.properties.SwaggerConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


@Configuration
@EnableSwagger2
@EnableConfigurationProperties(SwaggerConfigProperties.class)
@EnableKnife4j
@Import(BeanValidatorPluginsConfiguration.class)
public class SwaggerConfiguration {
    @Autowired
    SwaggerConfigProperties swaggerConfigProperties;


    private static final String USER_API_DOC = ".*(\\/api/user\\/).*";

    private static final String BACK_API_DOC = ".*(\\/api/admin\\/).*";

    /**
     * 接口地址中包含/api/user
     */
    @Bean
    @ConditionalOnClass(SwaggerConfigProperties.class)
    public Docket createUserApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                // 配置 api扫描路径
                .apis(RequestHandlerSelectors.basePackage(swaggerConfigProperties.getSwaggerPath()))
                // 指定路径的设置  any代表所有路径
                .paths(PathSelectors.any())
                .build()
                .groupName("接口文档")
                .pathMapping("/")
                .apiInfo(new ApiInfoBuilder()
                        //页面标题
                        .title("接口文档")
                        //创建人
                        .contact(new Contact("周杰", "www.zj.com", "<EMAIL>"))
                        //版本号
                        .version("2.0")
                        //描述
                        .description("万欧科技智慧居家养老平台接口文档")
                        .build()
                );
    }


}
