package com.wanou.framework.aspectj;

import com.wanou.common.utils.AesUtil;
import com.wanou.framework.aspectj.lang.annotation.Encrypt;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * @ClassName EncryptAspect
 * @Description TODO
 * <AUTHOR>
 * @Date 2024-01-27 15:42
 */
@Aspect
@Component
public class EncryptAspect {

    //配置切入点
    @Pointcut("@annotation(com.wanou.framework.aspectj.lang.annotation.Encrypt)")
    public void encryptPointCut() {
    }

    @Before("encryptPointCut() && @annotation(encrypt)")
    public void doBefore(JoinPoint point, Encrypt encrypt) throws Throwable {
        Object[] args = point.getArgs();
        String[] fields = encrypt.field();
        if(args.length == 1){
            Class<?> aClass = args[0].getClass();
            for (int i = 0; i < fields.length; i++) {
                Field declaredField = aClass.getDeclaredField(fields[i]);
                if (declaredField != null) {
                    declaredField.setAccessible(true);
                    declaredField.set(args[0], AesUtil.encryptToHex((String) declaredField.get(args[0])));
                }
            }
        }
    }
}
