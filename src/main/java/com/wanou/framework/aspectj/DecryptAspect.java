package com.wanou.framework.aspectj;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.utils.AesUtil;
import com.wanou.common.utils.DesensitizedUtils;
import com.wanou.common.utils.reflect.ReflectUtils;
import com.wanou.framework.aspectj.lang.annotation.Decrypt;
import com.wanou.framework.web.domain.AjaxResult;
import com.wanou.framework.web.page.TableDataInfo;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName DecryptAspect
 * @Description TODO
 * <AUTHOR>
 * @Date 2024-01-27 15:42
 */
@Aspect
@Component
public class DecryptAspect {

    //配置切入点
    @Pointcut("@annotation(com.wanou.framework.aspectj.lang.annotation.Decrypt)")
    public void decryptPointCut() {
    }

    @AfterReturning(value = "decryptPointCut() && @annotation(decrypt)",returning = "result")
    public void doAfterReturning(JoinPoint point, Decrypt decrypt,Object result) throws Throwable {
        String[] fields = decrypt.field();
        boolean desensitization = decrypt.isDesensitization();
        if(fields.length > 0){
            if(result instanceof TableDataInfo){
                //分页列表数据
                TableDataInfo tableDataInfo = (TableDataInfo) result;
                handleTableDataInfo(tableDataInfo,fields,desensitization);
            }else if(result instanceof AjaxResult){
                //单个对象数据
                handleAjaxResult((AjaxResult)result,fields,desensitization);
            }
        }
    }

    private void handleTableDataInfo(TableDataInfo tableDataInfo,String[] fields,boolean desensitization){
        List<?> rows = tableDataInfo.getRows();
        if(CollUtil.isNotEmpty(rows)){
            rows.forEach(row -> {
                Class<?> aClass = row.getClass();
                //判断类型
                if(row instanceof JSONObject){
                    //JSONObject
                    JSONObject jsonObject = (JSONObject) row;
                    for (int i = 0; i < fields.length; i++) {
                        String value = jsonObject.getString(fields[i]);
                        if(value != null){
                            String decryptFromHex = AesUtil.decryptFromHex(value);
                            if(desensitization){
                                //脱敏
                                decryptFromHex = DesensitizedUtils.desensitized(decryptFromHex,fields[i]);
                            }
                            jsonObject.put(fields[i],decryptFromHex);
                        }
                    }
                }else {
                    Field[] allFields = ReflectUtils.getAllFields(aClass);
                    for (int i = 0; i < fields.length; i++) {
                        try {
                            int finalI = i;
                            Field classField = Arrays.stream(allFields).filter(field -> field.getName().equals(fields[finalI])).findFirst().orElse(null);
                            if(classField == null){
                                //没有这个字段
                                continue;
                            }
                            classField.setAccessible(true);
                            if(classField.getType().equals(String.class)){
                                String decryptFromHex = AesUtil.decryptFromHex((String) ReflectUtils.getFieldValue(row, classField.getName()));
                                if(desensitization){
                                    //脱敏
                                    decryptFromHex = DesensitizedUtils.desensitized(decryptFromHex,classField.getName());
                                }
                                classField.set(row, decryptFromHex);
                            }
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
        }
    }

    private void handleAjaxResult(AjaxResult ajaxResult,String[] fields,boolean desensitization){
        Object data = ajaxResult.get(AjaxResult.DATA_TAG);
        if(data == null){
            return;
        }
        Class<?> aClass = data.getClass();
        Field[] allFields = ReflectUtils.getAllFields(aClass);
        for (int i = 0; i < fields.length; i++) {
            int finalI = i;
            Field matchField = Arrays.stream(allFields).filter(field -> field.getName().equals(fields[finalI])).findFirst()
                    .orElse(null);
            if(matchField == null){
                //没有这个字段
                continue;
            }
            matchField.setAccessible(true);
            String decryptFromHex = AesUtil.decryptFromHex((String) ReflectUtils.getFieldValue(data, matchField.getName()));
            if(desensitization){
                //脱敏
                decryptFromHex = DesensitizedUtils.desensitized(decryptFromHex,matchField.getName());
            }
            try {
                matchField.set(data, decryptFromHex);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
