package com.wanou.framework.task;

import cn.hutool.core.collection.CollUtil;
import com.wanou.common.constant.Constants;
import com.wanou.framework.redis.RedisCache;
import com.wanou.project.system.domain.SysDept;
import com.wanou.project.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @ClassName RedisCacheInit
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/17 18:45
 */
@Component
public class RedisCacheInit {
    @Autowired
    ISysDeptService sysDeptService;
    @Autowired
    private RedisCache redisCache;

    @PostConstruct
    public void init(){
        deptInit();
    }

    /**
     * 部门数据初始化
     */
    public void deptInit(){
        redisCache.deleteObject(Constants.SYS_DEPT_CACHE);
        List<SysDept> sysDeptList = sysDeptService.selectAllDeptList();
        if(CollUtil.isNotEmpty(sysDeptList)){
            sysDeptList.forEach(dept -> redisCache.setCacheMapBound(Constants.SYS_DEPT_CACHE,dept.getDeptId().toString(),dept));
        }
    }
}
