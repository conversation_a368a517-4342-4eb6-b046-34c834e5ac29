package com.wanou.framework.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wanou.common.constant.Constants;
import com.wanou.framework.redis.RedisCache;
import com.wanou.project.common.domain.District;
import com.wanou.project.common.service.IDistrictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DistrictTask
 * @Description 行政区划任务
 * <AUTHOR>
 * @Date 2024-01-26 10:19
 */
@Component
@Slf4j
public class DistrictTask {
    private static final String url = "https://restapi.amap.com/v3/config/district?key=33f10ccdd03ca3f08bba0ca0b1c1c705&subdistrict=3";

    @Resource
    private RedisCache redisCache;
    @Resource
    private IDistrictService districtService;

    @PostConstruct
    public void init(){
        execute();
    }

    /**
     * 每天凌晨4点执行
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void execute(){
        log.info("更新行政区划任务开始执行");

        String body = HttpRequest.get(url).execute().body();
        JSONObject res = JSON.parseObject(body);
        if(!"1".equals(res.getString("status")) || !"10000".equals(res.getString("infocode"))){
            log.error("获取行政区划数据失败：{}",res);
            return;
        }
        JSONArray districts = res.getJSONArray("districts");
        if(CollUtil.isEmpty(districts)){
            log.error("无行政区划数据");
            return;
        }
        JSONObject countryData = districts.getJSONObject(0); //国家数据
        if(countryData == null){
            log.error("无国家数据");
            return;
        }
        String countryAdcode = countryData.getString("adcode");
        JSONArray provinces = countryData.getJSONArray("districts"); //省份数据
        if(CollUtil.isEmpty(provinces)){
            log.error("无省份数据");
            return;
        }
        List<JSONObject> allCityList = new ArrayList<>();
        List<JSONObject> allDistrictList = new ArrayList<>();
        List<JSONObject> provinceList = new ArrayList<>(provinces.size());
        provinces.forEach((province) -> {
            JSONObject bean = BeanUtil.toBean(province, JSONObject.class);
            //封装省份数据
            JSONObject provinceObj = getDistrictData(bean);
            provinceList.add(provinceObj);
            //封装市区数据
            String provinceAdcode = bean.getString("adcode");
            JSONArray cityArray = bean.getJSONArray("districts");
            if(CollUtil.isNotEmpty(cityArray)){
                List<JSONObject> cityList = new ArrayList<>(cityArray.size());
                cityArray.forEach(city -> {
                    JSONObject cityBean = BeanUtil.toBean(city, JSONObject.class);
                    JSONObject cityObj = getDistrictData(cityBean);
                    cityObj.put("province",provinceAdcode);
                    cityList.add(cityObj);

                    //封装区县数据
                    String cityAdcode = cityBean.getString("adcode");
                    JSONArray districtArray = cityBean.getJSONArray("districts");
                    if(CollUtil.isNotEmpty(districtArray)){
                        List<JSONObject> districtList = new ArrayList<>(districtArray.size());
                        districtArray.forEach(district -> {
                            JSONObject districtBean = BeanUtil.toBean(district, JSONObject.class);
                            JSONObject districtObj = getDistrictData(districtBean);
                            districtObj.put("province",provinceAdcode);
                            districtObj.put("city",cityAdcode);
                            districtList.add(districtObj);
                        });
                        //redisCache.setCacheList(Constants.DISTRICTS_DISTRICT+cityAdcode, districtList);
                        sort(districtList);
                        redisCache.setCacheMapBound(Constants.DISTRICTS_DISTRICT,cityAdcode,districtList);
                        allDistrictList.addAll(districtList);
                    }
                });
                //redisCache.setCacheList(Constants.DISTRICTS_CITY+provinceAdcode, cityList);
                sort(cityList);
                redisCache.setCacheMapBound(Constants.DISTRICTS_CITY,provinceAdcode,cityList);
                allCityList.addAll(cityList);
            }
        });

        //redisCache.setCacheList(Constants.DISTRICTS_PROVINCE+countryAdcode, provinceList);
        sort(provinceList);
        redisCache.setCacheMapBound(Constants.DISTRICTS_PROVINCE,countryAdcode,provinceList);

        //存库
        districtService.batchInsertOrUpdate(convertSaveDistrictList(provinceList));
        if(CollUtil.isNotEmpty(allCityList)){
            List<List<JSONObject>> citySplit = CollUtil.split(allCityList, 100);
            citySplit.forEach(cityArr -> districtService.batchInsertOrUpdate(convertSaveDistrictList(cityArr)));
        }
        if(CollUtil.isNotEmpty(allDistrictList)){
            List<List<JSONObject>> districtSplit = CollUtil.split(allDistrictList, 100);
            districtSplit.forEach(districtArr -> districtService.batchInsertOrUpdate(convertSaveDistrictList(districtArr)));
        }

        log.info("更新行政区划任务执行结束");
    }

    private JSONObject getDistrictData(JSONObject srcBean){
        JSONObject obj = new JSONObject();
        obj.put("name",srcBean.getString("name"));
        obj.put("adcode",srcBean.getString("adcode"));
        obj.put("level",srcBean.getString("level"));
        obj.put("lastLevel","district".equals(srcBean.getString("level")));
        return obj;
    }

    private void sort(List<JSONObject> srcList){
        srcList = CollUtil.sort(srcList, Comparator.comparing(o -> o.getString("adcode")));
    }

    private List<District> convertSaveDistrictList(List<JSONObject> srcList){

        return srcList.stream().map(item -> {
            District district = new District();
            district.setName(item.getString("name"));
            district.setAdcode(item.getString("adcode"));
            district.setLevel(item.getString("level"));
            district.setProvince(item.getString("province"));
            district.setCity(item.getString("city"));
            return district;
        }).collect(Collectors.toList());
    }
}
