# 项目相关配置
ruoyi:
  # 名称
  name: retirement_service
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2023

  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:\projectFile\retirement\ruoyi\uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.wanou: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 10
    # 密码
#    password: Wanou@redis!
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: aC1ykpPpskCWj8yAGLvFHpmRus
  # 令牌有效期（默认30分钟）
  expireTime: 120

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.wanou.project.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml,classpath*:mybatis/**/*Dao.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

    map-underscore-to-camel-case: true

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql



# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: ruoyi
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.wanou.project.system
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_

### OSS配置
oss:
  accessKeyId: LTAI5tRWj6rvQX7heTpjJB4e
  accessSecret: ******************************
  roleArn: acs:ram::1676374418623267:role/sts
  bucket-name: wo-oss-test
  region: oss-cn-shanghai
  domain: https://wo-oss-test.oss-cn-shanghai.aliyuncs.com
  end-point: oss-cn-shanghai.aliyuncs.com
  portrait-oss-prefix: protrait/
  static-resource-prefix: https://wo-oss-test.oss-cn-shanghai.aliyuncs.com/

#积木报表
minidao:
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: mysql


#微信开发配置
weChat:
  ## 微信开发者appId
#  appId: wx8b0f97936605c1e9 # 正式
#  appId: wx0b7ff3d6447a3fda # 正式
  appId:   wx8b0f97936605c1e9     #公司公众号小程序
#  appId:   wxd6abd800e3c94e7a     #自己
  #  appId: wxee0845c1d1bfa852 # 测试
  ## 微信开发者 appSecret
  appSecret: d389b1a4e5b0cd0eafbace91daf8c0a8 # 公司公众号小程序
#  appSecret: 50f1b1ea82041713d20f1560c55d5dd8 # 自己
#  appSecret: a1ab35545041614d3efb944ecf4c9d3e # 正式
#  appSecret: 3683b7b9e5de41e8ef6b849d4f4e228e # 测试

# 小程序配置
mini:
  oss-head-image-prefix: mini_head_img/
unzipPath: D:\unzip
zipSavePath: D:\zipSave
