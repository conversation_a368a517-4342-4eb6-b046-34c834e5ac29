<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.HomeCareProgramMapper">
    <resultMap type="HomeCareProgram" id="HomeCareProgramResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="picture"    column="picture"    />
    </resultMap>

    <select id="listAll" resultType="com.wanou.project.system.domain.HomeCareProgram" resultMap="HomeCareProgramResult">
        select * from home_care_program
    </select>

    <sql id="selectHomeCareProgramVo">
        select id, name, status, create_time, update_time, create_by, update_by, remark, picture from home_care_program
    </sql>

    <select id="selectHomeCareProgramList" parameterType="HomeCareProgram" resultMap="HomeCareProgramResult">
        <include refid="selectHomeCareProgramVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="picture != null  and picture != ''"> and picture = #{picture}</if>
        </where>
    </select>

    <select id="selectHomeCareProgramById" parameterType="Long" resultMap="HomeCareProgramResult">
        <include refid="selectHomeCareProgramVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeCareProgram" parameterType="HomeCareProgram" useGeneratedKeys="true" keyProperty="id">
        insert into home_care_program
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="picture != null">picture,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="picture != null">#{picture},</if>
        </trim>
    </insert>

    <update id="updateHomeCareProgram" parameterType="HomeCareProgram">
        update home_care_program
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = now(),</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="picture != null">picture = #{picture},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCareProgramById" parameterType="Long">
        delete from home_care_program where id = #{id}
    </delete>

    <delete id="deleteHomeCareProgramByIds" parameterType="String">
        delete from home_care_program where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
