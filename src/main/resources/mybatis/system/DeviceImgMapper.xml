<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.DeviceImgMapper">
    
    <resultMap type="DeviceImg" id="DeviceImgResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceImg"    column="device_img"    />
        <result property="createTime"    column="create_time"    />
        <result property="createPy"    column="create_py"    />
    </resultMap>

    <sql id="selectDeviceImgVo">
        select id, device_name, device_img, create_time, create_py from device_img
    </sql>

    <select id="selectDeviceImgList" parameterType="DeviceImg" resultMap="DeviceImgResult">
        <include refid="selectDeviceImgVo"/>
        <where>  
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceImg != null  and deviceImg != ''"> and device_img = #{deviceImg}</if>
            <if test="createPy != null  and createPy != ''"> and create_py = #{createPy}</if>
        </where>
    </select>
    
    <select id="selectDeviceImgById" parameterType="Long" resultMap="DeviceImgResult">
        <include refid="selectDeviceImgVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDeviceImg" parameterType="DeviceImg" useGeneratedKeys="true" keyProperty="id">
        insert into device_img
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="deviceImg != null and deviceImg != ''">device_img,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createPy != null">create_py,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="deviceImg != null and deviceImg != ''">#{deviceImg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createPy != null">#{createPy},</if>
         </trim>
    </insert>

    <update id="updateDeviceImg" parameterType="DeviceImg">
        update device_img
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="deviceImg != null and deviceImg != ''">device_img = #{deviceImg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createPy != null">create_py = #{createPy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceImgById" parameterType="Long">
        delete from device_img where id = #{id}
    </delete>

    <delete id="deleteDeviceImgByIds" parameterType="String">
        delete from device_img where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>