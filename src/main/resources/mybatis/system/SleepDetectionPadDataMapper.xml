<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.SleepDetectionPadDataMapper">
    
    <resultMap type="SleepDetectionPadData" id="SleepDetectionPadDataResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="elderPhone"    column="elder_phone"    />
        <result property="deviceId"    column="device_id"    />
        <result property="heartRate"    column="heart_rate"    />
        <result property="breathe"    column="breathe"    />
        <result property="status"    column="status"    />
        <result property="batteryLevel"    column="battery_level"    />
        <result property="dataUploadTime"    column="data_upload_time"    />
    </resultMap>

    <sql id="selectSleepDetectionPadDataVo">
        select id, elder_id, elder_phone, device_id, heart_rate, breathe, status, battery_level, data_upload_time from sleep_detection_pad_data
    </sql>

    <select id="selectSleepDetectionPadDataList" parameterType="SleepDetectionPadData" resultMap="SleepDetectionPadDataResult">
        <include refid="selectSleepDetectionPadDataVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="elderPhone != null  and elderPhone != ''"> and elder_phone = #{elderPhone}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="heartRate != null "> and heart_rate = #{heartRate}</if>
            <if test="breathe != null "> and breathe = #{breathe}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="batteryLevel != null "> and battery_level = #{batteryLevel}</if>
            <if test="dataUploadTime != null "> and data_upload_time = #{dataUploadTime}</if>
        </where>
    </select>
    
    <select id="selectSleepDetectionPadDataById" parameterType="Long" resultMap="SleepDetectionPadDataResult">
        <include refid="selectSleepDetectionPadDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSleepDetectionPadData" parameterType="SleepDetectionPadData">
        insert into sleep_detection_pad_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="elderId != null">elder_id,</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="heartRate != null">heart_rate,</if>
            <if test="breathe != null">breathe,</if>
            <if test="status != null">status,</if>
            <if test="batteryLevel != null">battery_level,</if>
            <if test="dataUploadTime != null">data_upload_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="elderId != null">#{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">#{elderPhone},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="heartRate != null">#{heartRate},</if>
            <if test="breathe != null">#{breathe},</if>
            <if test="status != null">#{status},</if>
            <if test="batteryLevel != null">#{batteryLevel},</if>
            <if test="dataUploadTime != null">#{dataUploadTime},</if>
         </trim>
    </insert>

    <update id="updateSleepDetectionPadData" parameterType="SleepDetectionPadData">
        update sleep_detection_pad_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone = #{elderPhone},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="heartRate != null">heart_rate = #{heartRate},</if>
            <if test="breathe != null">breathe = #{breathe},</if>
            <if test="status != null">status = #{status},</if>
            <if test="batteryLevel != null">battery_level = #{batteryLevel},</if>
            <if test="dataUploadTime != null">data_upload_time = #{dataUploadTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSleepDetectionPadDataById" parameterType="Long">
        delete from sleep_detection_pad_data where id = #{id}
    </delete>

    <delete id="deleteSleepDetectionPadDataByIds" parameterType="String">
        delete from sleep_detection_pad_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>