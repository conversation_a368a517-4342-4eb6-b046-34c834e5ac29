<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.DeviceStoreMapper">
    <resultMap id="DeviceResult" type="com.wanou.project.system.domain.DeviceStore">
        <result property="id"    column="id"    />
        <result property="productCategoryId"    column="product_category_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="price"    column="price"    />
        <result property="unit"    column="unit"    />
        <result property="deviceImageUrl"    column="device_image_url"    />
        <result property="createdAt"    column="created_at"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="isAvailable"    column="is_available"    />
    </resultMap>

    <select id="listDevice" resultType="com.wanou.project.system.domain.DeviceStore" resultMap="DeviceResult">
        select * from device_store
        <where>
        is_available = 1
        <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
        <if test="productCategoryId != null "> and product_category_id = #{productCategoryId}</if>
        </where>
    </select>

    <select id="getDeviceById" resultType="com.wanou.project.system.domain.DeviceStore"
           resultMap="DeviceResult">
        select * from device_store where id = #{id}
    </select>

    <select id="selectDeviceStoreList" parameterType="Devicestore" resultMap="DeviceResult">
        select id, device_name, price, unit, description, created_at, updated_at, created_by, updated_by, device_image_url, is_available,product_category_id from device_store
        <where>
            <if test="productCategoryId != null "> and product_category_id = #{productCategoryId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="deviceImageUrl != null  and deviceImageUrl != ''"> and device_image_url = #{deviceImageUrl}</if>
            <if test="isAvailable != null "> and is_available = #{isAvailable}</if>
        </where>
    </select>
    <select id="listCategroy" resultType="com.wanou.project.system.domain.DeviceStore"
            resultMap="DeviceResult">
        select * from device_store where product_category_id = #{id} and is_available = 1
    </select>

    <insert id="insertDeviceStore" parameterType="Devicestore" useGeneratedKeys="true" keyProperty="id">
        insert into device_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCategoryId != null">product_category_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="price != null">price,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="description != null">description,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by,</if>
            <if test="deviceImageUrl != null and deviceImageUrl != ''">device_image_url,</if>
            <if test="isAvailable != null">is_available,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productCategoryId != null">#{productCategoryId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="price != null">#{price},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="description != null">#{description},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="updatedBy != null and updatedBy != ''">#{updatedBy},</if>
            <if test="deviceImageUrl != null and deviceImageUrl != ''">#{deviceImageUrl},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
        </trim>
    </insert>

    <update id="updateDeviceStore" parameterType="Devicestore">
        update device_store
        <trim prefix="SET" suffixOverrides=",">
            <if test="productCategoryId != null">product_category_id = #{productCategoryId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="price != null">price = #{price},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = now(),</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="deviceImageUrl != null and deviceImageUrl != ''">device_image_url = #{deviceImageUrl},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceStoreById" parameterType="Long">
        delete from device_store where id = #{id}
    </delete>

    <delete id="deleteDeviceStoreByIds" parameterType="String">
        delete from device_store where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
