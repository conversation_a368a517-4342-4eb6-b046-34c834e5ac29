<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ElderBaseInfoMapper">

    <resultMap type="ElderBaseInfo" id="ElderBaseInfoResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="idCardType"    column="id_card_type"    />
        <result property="idCard"    column="id_card"    />
        <result property="phone"    column="phone"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="county"    column="county"    />
        <result property="address"    column="address"    />
        <result property="liveState"    column="live_state"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
        <result property="delBy"    column="del_by"    />
    </resultMap>

    <resultMap id="ElderBaseInfoVoResult" type="ElderBaseInfoVo" extends="ElderBaseInfoResult">
        <result property="provinceName" column="province_name" />
        <result property="cityName" column="city_name" />
        <result property="countyName" column="county_name" />
    </resultMap>

    <sql id="selectElderBaseInfoVo">
        SELECT
            t1.id,
            t1.dept_id,
            t1.`name`,
            t1.sex,
            t1.age,
            t1.id_card_type,
            t1.id_card,
            t1.phone,
            t1.province,
            t1.city,
            t1.county,
            t1.address,
            t1.live_state,
            t1.create_time,
            t1.create_by,
            t1.update_time,
            t1.update_by,
            t1.is_del,
            t1.del_by,
            dt1.`name` province_name,
            dt2.`name` city_name,
            dt3.`name` county_name
        FROM
            elder_base_info t1
            LEFT JOIN district dt1 ON t1.province=dt1.adcode
            LEFT JOIN district dt2 ON t1.city=dt2.adcode
            LEFT JOIN district dt3 ON t1.county=dt3.adcode
    </sql>

    <select id="selectElderBaseInfoList" parameterType="ElderBaseInfo" resultMap="ElderBaseInfoVoResult">
        SELECT
        t1.id,
        t1.dept_id,
        t1.`name`,
        t1.sex,
        t1.age,
        t1.id_card_type,
        t1.id_card,
        t1.phone,
        t1.province,
        t1.city,
        t1.county,
        t1.address,
        t1.live_state,
        t1.create_time,
        t1.create_by,
        t1.update_time,
        t1.update_by,
        t1.is_del,
        t1.del_by,
        dt1.`name` province_name,
        dt2.`name` city_name,
        dt3.`name` county_name
        FROM
        elder_base_info t1
        LEFT JOIN district dt1 ON t1.province=dt1.adcode
        LEFT JOIN district dt2 ON t1.city=dt2.adcode
        LEFT JOIN district dt3 ON t1.county=dt3.adcode
        left join sys_dept sd on t1.dept_id = sd.dept_id
        <where>
            <if test="name != null  and name != ''"> and t1.`name` like concat('%', #{name}, '%')</if>
            <if test="sex != null "> and t1.sex = #{sex}</if>
            <if test="deptId !=null">and (sd.dept_id=#{deptId} or FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="age != null "> and t1.age = #{age}</if>
            <if test="idCardType != null "> and t1.id_card_type = #{idCardType}</if>
            <if test="idCard != null  and idCard != ''"> and t1.id_card like concat('%', #{idCard}, '%')</if>
            <if test="phone != null  and phone != ''"> and t1.phone like concat('%', #{phone}, '%')</if>
            <if test="province != null  and province != ''"> and t1.province = #{province}</if>
            <if test="city != null  and city != ''"> and t1.city = #{city}</if>
            <if test="county != null  and county != ''"> and t1.county = #{county}</if>
            <if test="address != null  and address != ''"> and t1.address like concat('%', #{address}, '%')</if>
            <if test="liveState != null "> and t1.live_state = #{liveState}</if>
        </where>
    </select>

    <select id="selectElderBaseInfoById" parameterType="Long" resultMap="ElderBaseInfoVoResult">
        <include refid="selectElderBaseInfoVo"/>
        where t1.id = #{id}
    </select>

    <select id="totalElderData" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        COUNT(t1.id) elderTotal,
        SUM(IF(DATE(NOW())=DATE(t1.create_time),1,0)) todayElder,
        (select
        COUNT(t1.id)
        from
        elder_base_info t1 LEFT JOIN sys_dept t2 on t1.dept_id = t2.dept_id
        where
        t1.create_time >= #{startOfWeek} and t1.create_time &lt;= #{endOfWeek}) weekElder,
        (select
        COUNT(t1.id)
        from
        elder_base_info t1 LEFT JOIN sys_dept t2 on t1.dept_id = t2.dept_id
        where
        t1.create_time >= #{startOfMonth} and t1.create_time &lt;= #{endOfMonth})
        monthElder
        FROM
        elder_base_info t1 LEFT JOIN sys_dept t2 on t1.dept_id = t2.dept_id
    </select>

    <select id="totalElderBySex" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(t1.id) `value`,t2.dict_label `name`
        FROM
            `elder_base_info` t1
            LEFT JOIN sys_dict_data t2 ON t2.dict_type='sys_user_sex' AND t2.dict_value=t1.sex
            INNER JOIN sys_dept t3 ON t3.dept_id = t1.dept_id
        WHERE
            (t3.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},t3.ancestors))
        GROUP BY
            t1.sex
    </select>

    <select id="selectColumns" resultType="com.wanou.project.system.domain.ElderBaseInfo" resultMap="ElderBaseInfoResult">
        select
        <foreach collection="columns" item="columnItem" separator=",">
            t1.`${columnItem}`
        </foreach>
        from elder_base_info t1
        left join sys_dept t2 on t2.dept_id = t1.dept_id
        where t2.dept_id = #{deptId} or find_in_set(#{deptId},t2.ancestors)
    </select>

    <select id="dataViewStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(t1.id) totalElderCount,SUM(IF(DATE_FORMAT(t1.create_time,'%Y%m%d')=DATE_FORMAT(NOW(),'%Y%m%d'),1,0)) todayAddElderCount
        FROM
            elder_base_info t1
            LEFT JOIN sys_dept t2 ON t2.dept_id = t1.dept_id
        WHERE
            t2.dept_id = #{deptId} OR FIND_IN_SET(#{deptId},t2.ancestors)
    </select>

    <insert id="insertElderBaseInfo" parameterType="ElderBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into elder_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null and deptId != ''">dept_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="idCardType != null">id_card_type,</if>
            <if test="idCard != null">id_card,</if>
            <if test="phone != null">phone,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="county != null">county,</if>
            <if test="address != null">address,</if>
            <if test="liveState != null">live_state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="delBy != null">del_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null and deptId != ''">#{deptId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="idCardType != null">#{idCardType},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="phone != null">#{phone},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="county != null">#{county},</if>
            <if test="address != null">#{address},</if>
            <if test="liveState != null">#{liveState},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="delBy != null">#{delBy},</if>
         </trim>
    </insert>

    <update id="updateElderBaseInfo" parameterType="ElderBaseInfo">
        update elder_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="idCardType != null">id_card_type = #{idCardType},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="county != null">county = #{county},</if>
            <if test="address != null">address = #{address},</if>
            <if test="liveState != null">live_state = #{liveState},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="delBy != null">del_by = #{delBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderBaseInfoById" parameterType="Long">
        delete from elder_base_info where id = #{id}
    </delete>

    <delete id="deleteElderBaseInfoByIds" parameterType="String">
        delete from elder_base_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
