<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.UricWarningMapper">
    
    <resultMap type="UricWarning" id="UricWarningResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="uricValues"    column="uric_values"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="uricWarningResults"    column="uric_warning_results"    />
    </resultMap>

    <sql id="selectUricWarningVo">
        select id, elder_id, device_id, uric_values, detection_time, uric_warning_results from uric_warning
    </sql>

    <select id="selectUricWarningList" parameterType="UricWarning" resultMap="UricWarningResult">
        <include refid="selectUricWarningVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="uricValues != null "> and uric_values = #{uricValues}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="uricWarningResults != null "> and uric_warning_results = #{uricWarningResults}</if>
        </where>
    </select>
    
    <select id="selectUricWarningById" parameterType="Long" resultMap="UricWarningResult">
        <include refid="selectUricWarningVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUricWarning" parameterType="UricWarning" useGeneratedKeys="true" keyProperty="id">
        insert into uric_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="uricValues != null">uric_values,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="uricWarningResults != null">uric_warning_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="uricValues != null">#{uricValues},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="uricWarningResults != null">#{uricWarningResults},</if>
         </trim>
    </insert>

    <update id="updateUricWarning" parameterType="UricWarning">
        update uric_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="uricValues != null">uric_values = #{uricValues},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="uricWarningResults != null">uric_warning_results = #{uricWarningResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUricWarningById" parameterType="Long">
        delete from uric_warning where id = #{id}
    </delete>

    <delete id="deleteUricWarningByIds" parameterType="String">
        delete from uric_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>