<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.BloodGlucoseDataMapper">
    
    <resultMap type="BloodGlucoseData" id="BloodGlucoseDataResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="elderPhone"    column="elder_phone"    />
        <result property="deviceId"    column="device_id"    />
        <result property="glucose"    column="glucose"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="detectionResults"    column="detection_results"    />
    </resultMap>

    <sql id="selectBloodGlucoseDataVo">
        select id, elder_id, elder_phone, device_id, glucose, detection_time, detection_results from blood_glucose_data
    </sql>

    <select id="selectBloodGlucoseDataList" parameterType="BloodGlucoseData" resultMap="BloodGlucoseDataResult">
        <include refid="selectBloodGlucoseDataVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="elderPhone != null  and elderPhone != ''"> and elder_phone = #{elderPhone}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="glucose != null "> and glucose = #{glucose}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="detectionResults != null "> and detection_results = #{detectionResults}</if>
        </where>
    </select>
    
    <select id="selectBloodGlucoseDataById" parameterType="Long" resultMap="BloodGlucoseDataResult">
        <include refid="selectBloodGlucoseDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBloodGlucoseData" parameterType="BloodGlucoseData" useGeneratedKeys="true" keyProperty="id">
        insert into blood_glucose_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="glucose != null">glucose,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="detectionResults != null">detection_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">#{elderPhone},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="glucose != null">#{glucose},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="detectionResults != null">#{detectionResults},</if>
         </trim>
    </insert>

    <update id="updateBloodGlucoseData" parameterType="BloodGlucoseData">
        update blood_glucose_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone = #{elderPhone},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="glucose != null">glucose = #{glucose},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="detectionResults != null">detection_results = #{detectionResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBloodGlucoseDataById" parameterType="Long">
        delete from blood_glucose_data where id = #{id}
    </delete>

    <delete id="deleteBloodGlucoseDataByIds" parameterType="String">
        delete from blood_glucose_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>Q
    </delete>
</mapper>