<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.BloodGlucoseWarningMapper">
    
    <resultMap type="BloodGlucoseWarning" id="BloodGlucoseWarningResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="glucose"    column="glucose"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="warningResults"    column="warning_results"    />
    </resultMap>

    <sql id="selectBloodGlucoseWarningVo">
        select id, elder_id, device_id, glucose, detection_time, warning_results from blood_glucose_warning
    </sql>

    <select id="selectBloodGlucoseWarningList" parameterType="BloodGlucoseWarning" resultMap="BloodGlucoseWarningResult">
        <include refid="selectBloodGlucoseWarningVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="glucose != null "> and glucose = #{glucose}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="warningResults != null "> and warning_results = #{warningResults}</if>
        </where>
    </select>
    
    <select id="selectBloodGlucoseWarningById" parameterType="Long" resultMap="BloodGlucoseWarningResult">
        <include refid="selectBloodGlucoseWarningVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBloodGlucoseWarning" parameterType="BloodGlucoseWarning" useGeneratedKeys="true" keyProperty="id">
        insert into blood_glucose_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="glucose != null">glucose,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="warningResults != null">warning_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="glucose != null">#{glucose},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="warningResults != null">#{warningResults},</if>
         </trim>
    </insert>

    <update id="updateBloodGlucoseWarning" parameterType="BloodGlucoseWarning">
        update blood_glucose_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="glucose != null">glucose = #{glucose},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="warningResults != null">warning_results = #{warningResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBloodGlucoseWarningById" parameterType="Long">
        delete from blood_glucose_warning where id = #{id}
    </delete>

    <delete id="deleteBloodGlucoseWarningByIds" parameterType="String">
        delete from blood_glucose_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>