<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ServiceCommentsMapper">
    <resultMap id="CommentResult" type="com.wanou.project.system.domain.ServiceComments">

        <result column="comment_id" property="commentId" />
        <result column="nick_name" property="nickName" />
        <result column="user_id" property="userId" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="parent_id" property="parentId" />
        <result column="content" property="content" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    <select id="selectList" resultType="com.wanou.project.system.domain.ServiceComments"  resultMap="CommentResult">
--     通过user_id联表查询mini_user_info表中的nick_name和avatar_url并且根据service_id查询对应的服务评论
    select sc.*, mu.nick_name, mu.avatar_url from service_comments sc left join mini_user_info mu on sc.user_id = mu.user_id
    where sc.service_id = #{serviceId} order by sc.created_at desc
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(*) from service_comments where service_id = #{serviceId}
    </select>
</mapper>
