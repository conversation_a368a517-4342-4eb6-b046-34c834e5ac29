<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.HomeCareServicesMapper">
    <resultMap type="HomeCareServices" id="HomeCareServicesResult">
        <result property="id"    column="id"    />
        <result property="productCategoryId"    column="product_category_id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="price"    column="price"    />
        <result property="validDuration"    column="valid_duration"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="picture"    column="picture"    />
    </resultMap>

    <sql id="selectHomeCareServicesVo">
        select id, product_category_id, name, status, price, valid_duration, create_time, update_time, create_by, update_by, remark, picture from home_care_services
    </sql>

    <select id="getServiceByProjectId" resultType="com.wanou.project.system.domain.HomeCareServices">
        SELECT * FROM home_care_services WHERE product_category_id = #{id}
    </select>
    <select id="getServiceById" resultType="com.wanou.project.system.domain.HomeCareServices" resultMap="HomeCareServicesResult">

        SELECT * FROM home_care_services WHERE id = #{id}
    </select>

    <select id="selectHomeCareServicesList" parameterType="HomeCareServices" resultMap="HomeCareServicesResult">
        <include refid="selectHomeCareServicesVo"/>
        <where>
            <if test="productCategoryId != null "> and product_category_id = #{productCategoryId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="validDuration != null "> and valid_duration = #{validDuration}</if>
            <if test="picture != null  and picture != ''"> and picture = #{picture}</if>
        </where>
    </select>

    <select id="selectHomeCareServicesById" parameterType="Long" resultMap="HomeCareServicesResult">
        <include refid="selectHomeCareServicesVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeCareServices" parameterType="HomeCareServices" useGeneratedKeys="true" keyProperty="id">
        insert into home_care_services
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productCategoryId != null">product_category_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="status != null">status,</if>
            <if test="price != null">price,</if>
            <if test="validDuration != null">valid_duration,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="picture != null">picture,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productCategoryId != null">#{productCategoryId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="price != null">#{price},</if>
            <if test="validDuration != null">#{validDuration},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="picture != null">#{picture},</if>
        </trim>
    </insert>

    <update id="updateHomeCareServices" parameterType="HomeCareServices">
        update home_care_services
        <trim prefix="SET" suffixOverrides=",">
            <if test="productCategoryId != null">product_category_id = #{productCategoryId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="price != null">price = #{price},</if>
            <if test="validDuration != null">valid_duration = #{validDuration},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = now(),</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="picture != null">picture = #{picture},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeCareServicesById" parameterType="Long">
        delete from home_care_services where id = #{id}
    </delete>

    <delete id="deleteHomeCareServicesByIds" parameterType="String">
        delete from home_care_services where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
