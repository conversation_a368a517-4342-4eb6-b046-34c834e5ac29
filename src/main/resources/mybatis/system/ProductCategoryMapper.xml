<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ProductCategoryMapper">
  <resultMap id="BaseResultMap" type="com.wanou.project.system.domain.ProductCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_level" jdbcType="INTEGER" property="categoryLevel" />
    <result property="updateBy"    column="update_by" jdbcType="VARCHAR"/>
    <result property="createBy"    column="create_by" jdbcType="VARCHAR"/>
    <result property="updateTime"    column="update_time" />
    <result property="createTime"    column="create_time"    />
    <result column="is_available" jdbcType="INTEGER" property="isAvailable" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="picture" jdbcType="VARCHAR" property="picture" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result property="isDel"    column="is_del"    />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, parent_id, category_name, category_level, is_available, create_time, update_time , create_by, update_by ,
    sort, picture, keywords , remark , is_del
  </sql>

  <select id="selectProductCategoryListApp" parameterType="com.wanou.project.system.domain.ProductCategory" resultMap="BaseResultMap">
    select distinct
    <include refid="Base_Column_List" />
    from product_category
    <where>
        is_del = 0
        <if test="parentId != null">and parent_id = #{parentId}</if>
        <if test="categoryName != null and categoryName != ''">
            and category_name like concat('%',#{categoryName},'%')
        </if>
    </where>
  </select>

  <select id="getList" parameterType="com.wanou.project.system.domain.ProductCategory" resultMap="BaseResultMap">
    select distinct
    <include refid="Base_Column_List" />
    from product_category
    where is_del = 0 and parent_id = #{parentId}
  </select>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from product_category
      where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertProductCategory" parameterType="com.wanou.project.system.domain.ProductCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="categoryName != null">category_name,</if>
      <if test="categoryLevel != null">category_level,</if>
      <if test="parentId != null">parent_id,</if>
      <if test="sort != null">sort,</if>
      <if test="isAvailable != null">is_available,</if>
      <if test="keywords != null">keywords,</if>
      <if test="createTime != null">create_time,</if>
      <if test="updateTime != null">update_time,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="picture != null">picture,</if>
      <if test="remark != null">remark,</if>
      <if test="isDel != null">is_del,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id},</if>
      <if test="categoryName != null">#{categoryName},</if>
      <if test="categoryLevel != null">#{categoryLevel},</if>
      <if test="parentId != null">#{parentId},</if>
      <if test="sort != null">#{sort},</if>
      <if test="isAvailable != null">#{isAvailable},</if>
      <if test="keywords != null">#{keywords},</if>
      <if test="createTime != null">#{createTime},</if>
      <if test="updateTime != null">#{updateTime},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="picture != null">#{picture},</if>
      <if test="remark != null">#{remark},</if>
      <if test="isDel != null">#{isDel},</if>
    </trim>
  </insert>

  <update id="updateProductCategory" parameterType="com.wanou.project.system.domain.ProductCategory">
    update product_category
     <trim prefix="SET" suffixOverrides=",">
       <if test="categoryName != null">category_name = #{categoryName},</if>
       <if test="categoryLevel != null">category_level = #{categoryLevel},</if>
       <if test="parentId != null">parent_id = #{parentId},</if>
       <if test="sort != null">sort = #{sort},</if>
       <if test="isAvailable != null">is_available = #{isAvailable},</if>
       <if test="keywords != null">keywords = #{keywords},</if>
       <if test="createTime != null">create_time = #{createTime},</if>
       <if test="updateTime != null">update_time = now(),</if>
       <if test="createBy != null">create_by = #{createBy},</if>
       <if test="updateBy != null">update_by = #{updateBy},</if>
       <if test="picture != null">picture = #{picture},</if>
       <if test="remark != null">remark = #{remark},</if>
       <if test="isDel != null">is_del = #{isDel},</if>
     </trim>
      where id = #{id}
  </update>

  <update id="updateIsAvailable">
    UPDATE product_category
    SET is_available = #{isAvailable},
    update_time = NOW()
    WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <delete id="deleteProductCategory" parameterType="java.lang.Long">
    update product_category set
    <if test="isDel != null">is_del = 1</if>
    where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>