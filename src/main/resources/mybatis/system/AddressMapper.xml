<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.AddressMapper">
    <resultMap id="AddressResult" type="Address">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="address"    column="address"    />
        <result property="recipient"    column="recipient"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="addressType"    column="address_type"    />

    </resultMap>

    <sql id="selectAddressVo">
        select id, user_id, address, recipient, phone_number, house_number, address_type from address
    </sql>

    <insert id="insertAddress">
        insert into address (id, user_id, address, recipient, phone_number, house_number,address_type) values (#{id}, #{userId}, #{address}, #{recipient}, #{phoneNumber}, #{houseNumber},#{addressType})

    </insert>
    <select id="selectAddressByUserId" resultType="com.wanou.project.system.domain.Address"  resultMap="AddressResult">
        select * from address where user_id = #{userId}
    </select>

    <select id="selectAddressListApp" parameterType="Address" resultMap="AddressResult">
        <include refid="selectAddressVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="address != null "> and address = #{address}</if>
            <if test="recipient != null "> and recipient = #{recipient}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phoneNumber = #{phone_number}</if>
            <if test="houseNumber != null  and houseNumber != ''"> and houseNumber = #{house_number}</if>
            <if test="addressType != null  and addressType != ''"> and addressType = #{address_type}</if>
        </where>
    </select>
</mapper>
