<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.DeviceMapper">

    <resultMap type="Device" id="DeviceResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="deviceSn"    column="device_sn"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="alarmPushType"    column="alarm_push_type"    />
        <result property="deviceImg"    column="device_img"    />
        <result property="bindingStatus"    column="binding_status"    />
        <result property="bindingTime"    column="binding_time"    />
        <result property="presence"    column="presence"    />
        <result property="createTime"    column="create_time"    />
        <result property="createPerson"    column="create_person"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="deletePerson"    column="delete_person"    />
    </resultMap>

    <sql id="selectDeviceVo">
        select id, elder_id, device_sn, device_name, device_type, alarm_push_type, device_img, binding_status,binding_time, presence, create_time, create_person, delete_status, delete_person from device
    </sql>

    <select id="selectDeviceList" parameterType="Device" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="deviceSn != null  and deviceSn != ''"> and device_sn = #{deviceSn}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
            <if test="alarmPushType != null  and alarmPushType != ''"> and alarm_push_type = #{alarmPushType}</if>
            <if test="deviceImg != null "> and device_img = #{deviceImg}</if>
            <if test="bindingStatus != null "> and binding_status = #{bindingStatus}</if>
            <if test="presence != null "> and presence = #{presence}</if>
            <if test="createPerson != null  and createPerson != ''"> and create_person = #{createPerson}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="deletePerson != null  and deletePerson != ''"> and delete_person = #{deletePerson}</if>
        </where>
    </select>

    <select id="selectDeviceById" parameterType="Long" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        where id = #{id}
    </select>
    <select id="selectDeviceByIds" resultType="com.wanou.project.system.domain.Device" resultMap="DeviceResult">

    select * from device where id in( <foreach collection="deviceIds" item="id" separator=",">
            #{id}
        </foreach> ) and delete_status = 0

    </select>

    <insert id="insertDevice" parameterType="Device" useGeneratedKeys="true" keyProperty="id">
        insert into device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="deviceSn != null and deviceSn != ''">device_sn,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="alarmPushType != null and alarmPushType != ''">alarm_push_type,</if>
            <if test="deviceImg != null">device_img,</if>
            <if test="bindingStatus != null">binding_status,</if>
            <if test="bindingTime != null">binding_time,</if>
            <if test="presence != null">presence,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createPerson != null">create_person,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="deletePerson != null">delete_person,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="deviceSn != null and deviceSn != ''">#{deviceSn},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="alarmPushType != null and alarmPushType != ''">#{alarmPushType},</if>
            <if test="deviceImg != null">#{deviceImg},</if>
            <if test="bindingStatus != null">#{bindingStatus},</if>
            <if test="bindingTime != null">#{bindingTime},</if>
            <if test="presence != null">#{presence},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createPerson != null">#{createPerson},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="deletePerson != null">#{deletePerson},</if>
         </trim>
    </insert>

    <update id="updateDevice" parameterType="Device">
        update device
        <trim prefix="SET" suffixOverrides=",">
            elder_id = #{elderId},
            <if test="deviceSn != null and deviceSn != ''">device_sn = #{deviceSn},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="alarmPushType != null and alarmPushType != ''">alarm_push_type = #{alarmPushType},</if>
            <if test="deviceImg != null">device_img = #{deviceImg},</if>
            <if test="bindingStatus != null">binding_status = #{bindingStatus},</if>
            binding_time = #{bindingTime},
            <if test="presence != null">presence = #{presence},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createPerson != null">create_person = #{createPerson},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="deletePerson != null">delete_person = #{deletePerson},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceById" parameterType="Long">
        delete from device where id = #{id}
    </delete>

    <delete id="deleteDeviceByIds" parameterType="String">
        delete from device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
