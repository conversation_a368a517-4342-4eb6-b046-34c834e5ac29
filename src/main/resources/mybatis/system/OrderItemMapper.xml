<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.OrderItemMapper">
  <resultMap id="BaseResultMap" type="com.wanou.project.system.domain.OrderItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_pic" jdbcType="VARCHAR" property="productPic" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_brand" jdbcType="VARCHAR" property="productBrand" />
    <result column="product_sn" jdbcType="VARCHAR" property="productSn" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="product_quantity" jdbcType="INTEGER" property="productQuantity" />
    <result column="product_sku_id" jdbcType="BIGINT" property="productSkuId" />
    <result column="product_sku_code" jdbcType="VARCHAR" property="productSkuCode" />
    <result column="product_category_id" jdbcType="BIGINT" property="productCategoryId" />
    <result column="promotion_name" jdbcType="VARCHAR" property="promotionName" />
    <result column="promotion_amount" jdbcType="DECIMAL" property="promotionAmount" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="integration_amount" jdbcType="DECIMAL" property="integrationAmount" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="gift_integration" jdbcType="INTEGER" property="giftIntegration" />
    <result column="gift_growth" jdbcType="INTEGER" property="giftGrowth" />
    <result column="product_attr" jdbcType="VARCHAR" property="productAttr" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_sn, product_id, product_pic, product_name, product_brand, product_sn,
    product_price, product_quantity, product_sku_id, product_sku_code, product_category_id,
    promotion_name, promotion_amount, coupon_amount, integration_amount, real_amount,
    gift_integration, gift_growth, product_attr
  </sql>
  <select id="selectByOrderIds" resultMap="BaseResultMap">
    SELECT *
    FROM order_item
    WHERE order_id IN
    <foreach collection="orderIds" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="selectByOrderId" resultMap="BaseResultMap">
    SELECT *
    FROM order_item
    WHERE order_id = #{orderId}
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.wanou.project.system.domain.mbg.OrderItemExample">
    delete from order_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wanou.project.system.domain.OrderItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_item (order_id, order_sn, product_id,
      product_pic, product_name, product_brand,
      product_sn, product_price, product_quantity,
      product_sku_id, product_sku_code, product_category_id,
      promotion_name, promotion_amount, coupon_amount,
      integration_amount, real_amount, gift_integration,
      gift_growth, product_attr)
    values (#{orderId,jdbcType=BIGINT}, #{orderSn,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT},
      #{productPic,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{productBrand,jdbcType=VARCHAR},
      #{productSn,jdbcType=VARCHAR}, #{productPrice,jdbcType=DECIMAL}, #{productQuantity,jdbcType=INTEGER},
      #{productSkuId,jdbcType=BIGINT}, #{productSkuCode,jdbcType=VARCHAR}, #{productCategoryId,jdbcType=BIGINT},
      #{promotionName,jdbcType=VARCHAR}, #{promotionAmount,jdbcType=DECIMAL}, #{couponAmount,jdbcType=DECIMAL},
      #{integrationAmount,jdbcType=DECIMAL}, #{realAmount,jdbcType=DECIMAL}, #{giftIntegration,jdbcType=INTEGER},
      #{giftGrowth,jdbcType=INTEGER}, #{productAttr,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wanou.project.system.domain.OrderItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderSn != null">
        order_sn,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productPic != null">
        product_pic,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productBrand != null">
        product_brand,
      </if>
      <if test="productSn != null">
        product_sn,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="productQuantity != null">
        product_quantity,
      </if>
      <if test="productSkuId != null">
        product_sku_id,
      </if>
      <if test="productSkuCode != null">
        product_sku_code,
      </if>
      <if test="productCategoryId != null">
        product_category_id,
      </if>
      <if test="promotionName != null">
        promotion_name,
      </if>
      <if test="promotionAmount != null">
        promotion_amount,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="integrationAmount != null">
        integration_amount,
      </if>
      <if test="realAmount != null">
        real_amount,
      </if>
      <if test="giftIntegration != null">
        gift_integration,
      </if>
      <if test="giftGrowth != null">
        gift_growth,
      </if>
      <if test="productAttr != null">
        product_attr,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productPic != null">
        #{productPic,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productBrand != null">
        #{productBrand,jdbcType=VARCHAR},
      </if>
      <if test="productSn != null">
        #{productSn,jdbcType=VARCHAR},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="productQuantity != null">
        #{productQuantity,jdbcType=INTEGER},
      </if>
      <if test="productSkuId != null">
        #{productSkuId,jdbcType=BIGINT},
      </if>
      <if test="productSkuCode != null">
        #{productSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryId != null">
        #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="promotionName != null">
        #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionAmount != null">
        #{promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="integrationAmount != null">
        #{integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="giftIntegration != null">
        #{giftIntegration,jdbcType=INTEGER},
      </if>
      <if test="giftGrowth != null">
        #{giftGrowth,jdbcType=INTEGER},
      </if>
      <if test="productAttr != null">
        #{productAttr,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <insert id="insertList" >

      insert into order_item (order_id, order_sn, product_id, product_pic,
        product_name, product_brand, product_sn, product_price,
        product_quantity, product_sku_id, product_sku_code,
        product_category_id, promotion_name, promotion_amount,
        coupon_amount, integration_amount, real_amount,
        gift_integration, gift_growth, product_attr)
      values
      <foreach collection="list" item="item" index="index" separator=",">
        (#{item.orderId,jdbcType=BIGINT}, #{item.orderSn,jdbcType=VARCHAR}, #{item.productId,jdbcType=BIGINT}, #{item.productPic,jdbcType=VARCHAR},
          #{item.productName,jdbcType=VARCHAR}, #{item.productBrand,jdbcType=VARCHAR}, #{item.productSn,jdbcType=VARCHAR}, #{item.productPrice,jdbcType=DECIMAL},
          #{item.productQuantity,jdbcType=INTEGER}, #{item.productSkuId,jdbcType=BIGINT}, #{item.productSkuCode,jdbcType=VARCHAR},
          #{item.productCategoryId,jdbcType=BIGINT}, #{item.promotionName,jdbcType=VARCHAR}, #{item.promotionAmount,jdbcType=DECIMAL},
          #{item.couponAmount,jdbcType=DECIMAL}, #{item.integrationAmount,jdbcType=DECIMAL}, #{item.realAmount,jdbcType=DECIMAL},
          #{item.giftIntegration,jdbcType=INTEGER}, #{item.giftGrowth,jdbcType=INTEGER}, #{item.productAttr,jdbcType=VARCHAR})
      </foreach>

    </insert>
    <select id="countByExample" parameterType="com.wanou.project.system.domain.mbg.OrderItemExample" resultType="java.lang.Long">
    select count(*) from order_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSn != null">
        order_sn = #{record.orderSn,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productPic != null">
        product_pic = #{record.productPic,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productBrand != null">
        product_brand = #{record.productBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.productSn != null">
        product_sn = #{record.productSn,jdbcType=VARCHAR},
      </if>
      <if test="record.productPrice != null">
        product_price = #{record.productPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.productQuantity != null">
        product_quantity = #{record.productQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.productSkuId != null">
        product_sku_id = #{record.productSkuId,jdbcType=BIGINT},
      </if>
      <if test="record.productSkuCode != null">
        product_sku_code = #{record.productSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productCategoryId != null">
        product_category_id = #{record.productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.promotionName != null">
        promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionAmount != null">
        promotion_amount = #{record.promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.integrationAmount != null">
        integration_amount = #{record.integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.realAmount != null">
        real_amount = #{record.realAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.giftIntegration != null">
        gift_integration = #{record.giftIntegration,jdbcType=INTEGER},
      </if>
      <if test="record.giftGrowth != null">
        gift_growth = #{record.giftGrowth,jdbcType=INTEGER},
      </if>
      <if test="record.productAttr != null">
        product_attr = #{record.productAttr,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_item
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_sn = #{record.orderSn,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_pic = #{record.productPic,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_brand = #{record.productBrand,jdbcType=VARCHAR},
      product_sn = #{record.productSn,jdbcType=VARCHAR},
      product_price = #{record.productPrice,jdbcType=DECIMAL},
      product_quantity = #{record.productQuantity,jdbcType=INTEGER},
      product_sku_id = #{record.productSkuId,jdbcType=BIGINT},
      product_sku_code = #{record.productSkuCode,jdbcType=VARCHAR},
      product_category_id = #{record.productCategoryId,jdbcType=BIGINT},
      promotion_name = #{record.promotionName,jdbcType=VARCHAR},
      promotion_amount = #{record.promotionAmount,jdbcType=DECIMAL},
      coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      integration_amount = #{record.integrationAmount,jdbcType=DECIMAL},
      real_amount = #{record.realAmount,jdbcType=DECIMAL},
      gift_integration = #{record.giftIntegration,jdbcType=INTEGER},
      gift_growth = #{record.giftGrowth,jdbcType=INTEGER},
      product_attr = #{record.productAttr,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wanou.project.system.domain.OrderItem">
    update order_item
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderSn != null">
        order_sn = #{orderSn,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productPic != null">
        product_pic = #{productPic,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productBrand != null">
        product_brand = #{productBrand,jdbcType=VARCHAR},
      </if>
      <if test="productSn != null">
        product_sn = #{productSn,jdbcType=VARCHAR},
      </if>
      <if test="productPrice != null">
        product_price = #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="productQuantity != null">
        product_quantity = #{productQuantity,jdbcType=INTEGER},
      </if>
      <if test="productSkuId != null">
        product_sku_id = #{productSkuId,jdbcType=BIGINT},
      </if>
      <if test="productSkuCode != null">
        product_sku_code = #{productSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryId != null">
        product_category_id = #{productCategoryId,jdbcType=BIGINT},
      </if>
      <if test="promotionName != null">
        promotion_name = #{promotionName,jdbcType=VARCHAR},
      </if>
      <if test="promotionAmount != null">
        promotion_amount = #{promotionAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="integrationAmount != null">
        integration_amount = #{integrationAmount,jdbcType=DECIMAL},
      </if>
      <if test="realAmount != null">
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="giftIntegration != null">
        gift_integration = #{giftIntegration,jdbcType=INTEGER},
      </if>
      <if test="giftGrowth != null">
        gift_growth = #{giftGrowth,jdbcType=INTEGER},
      </if>
      <if test="productAttr != null">
        product_attr = #{productAttr,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wanou.project.system.domain.OrderItem">
    update order_item
    set order_id = #{orderId,jdbcType=BIGINT},
      order_sn = #{orderSn,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=BIGINT},
      product_pic = #{productPic,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_brand = #{productBrand,jdbcType=VARCHAR},
      product_sn = #{productSn,jdbcType=VARCHAR},
      product_price = #{productPrice,jdbcType=DECIMAL},
      product_quantity = #{productQuantity,jdbcType=INTEGER},
      product_sku_id = #{productSkuId,jdbcType=BIGINT},
      product_sku_code = #{productSkuCode,jdbcType=VARCHAR},
      product_category_id = #{productCategoryId,jdbcType=BIGINT},
      promotion_name = #{promotionName,jdbcType=VARCHAR},
      promotion_amount = #{promotionAmount,jdbcType=DECIMAL},
      coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      integration_amount = #{integrationAmount,jdbcType=DECIMAL},
      real_amount = #{realAmount,jdbcType=DECIMAL},
      gift_integration = #{giftIntegration,jdbcType=INTEGER},
      gift_growth = #{giftGrowth,jdbcType=INTEGER},
      product_attr = #{productAttr,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="releaseStockBySkuId">
    UPDATE sku_stock
    SET lock_stock = lock_stock - #{quantity}
    WHERE
      id = #{productSkuId}
      AND lock_stock - #{quantity} &gt;= 0
  </update>
</mapper>
