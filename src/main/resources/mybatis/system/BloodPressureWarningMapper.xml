<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.BloodPressureWarningMapper">
    
    <resultMap type="BloodPressureWarning" id="BloodPressureWarningResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="highPressure"    column="high_pressure"    />
        <result property="lowPressure"    column="low_pressure"    />
        <result property="pulse"    column="pulse"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="warningResults"    column="warning_results"    />
    </resultMap>

    <sql id="selectBloodPressureWarningVo">
        select id, elder_id, device_id, high_pressure, low_pressure, pulse, detection_time, warning_results from blood_pressure_warning
    </sql>

    <select id="selectBloodPressureWarningList" parameterType="BloodPressureWarning" resultMap="BloodPressureWarningResult">
        <include refid="selectBloodPressureWarningVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="highPressure != null "> and high_pressure = #{highPressure}</if>
            <if test="lowPressure != null "> and low_pressure = #{lowPressure}</if>
            <if test="pulse != null "> and pulse = #{pulse}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="warningResults != null "> and warning_results = #{warningResults}</if>
        </where>
    </select>
    
    <select id="selectBloodPressureWarningById" parameterType="Long" resultMap="BloodPressureWarningResult">
        <include refid="selectBloodPressureWarningVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBloodPressureWarning" parameterType="BloodPressureWarning" useGeneratedKeys="true" keyProperty="id">
        insert into blood_pressure_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="highPressure != null">high_pressure,</if>
            <if test="lowPressure != null">low_pressure,</if>
            <if test="pulse != null">pulse,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="warningResults != null">warning_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="highPressure != null">#{highPressure},</if>
            <if test="lowPressure != null">#{lowPressure},</if>
            <if test="pulse != null">#{pulse},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="warningResults != null">#{warningResults},</if>
         </trim>
    </insert>

    <update id="updateBloodPressureWarning" parameterType="BloodPressureWarning">
        update blood_pressure_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="highPressure != null">high_pressure = #{highPressure},</if>
            <if test="lowPressure != null">low_pressure = #{lowPressure},</if>
            <if test="pulse != null">pulse = #{pulse},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="warningResults != null">warning_results = #{warningResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBloodPressureWarningById" parameterType="Long">
        delete from blood_pressure_warning where id = #{id}
    </delete>

    <delete id="deleteBloodPressureWarningByIds" parameterType="String">
        delete from blood_pressure_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>