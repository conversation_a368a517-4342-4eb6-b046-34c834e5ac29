<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.EquipmentAlarmInformationMapper">

    <resultMap type="EquipmentAlarmInformation" id="EquipmentAlarmInformationResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="elderName"    column="elder_name"    />
        <result property="elderPhone"    column="elder_phone"    />
        <result property="reasonForAlarm"    column="reason_for_alarm"    />
        <result property="alarmTime"    column="alarm_time"    />
        <result property="isInformationPush"    column="is_information_push"    />
        <result property="isHandle"    column="is_handle"    />
        <result property="handleResults"    column="handle_results"    />
        <result property="handelTime"    column="handel_time"    />
        <result property="handelBy"    column="handel_by"    />
    </resultMap>

    <sql id="selectEquipmentAlarmInformationVo">
        select id, device_id, elder_id, elder_name, elder_phone, reason_for_alarm, alarm_time, is_information_push, is_handle, handle_results, handel_time, handel_by from equipment_alarm_information
    </sql>

    <select id="selectEquipmentAlarmInformationList" parameterType="EquipmentAlarmInformation" resultMap="EquipmentAlarmInformationResult">
        <include refid="selectEquipmentAlarmInformationVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="elderName != null  and elderName != ''"> and elder_name like concat('%', #{elderName}, '%')</if>
            <if test="elderPhone != null  and elderPhone != ''"> and elder_phone = #{elderPhone}</if>
            <if test="reasonForAlarm != null  and reasonForAlarm != ''"> and reason_for_alarm = #{reasonForAlarm}</if>
            <if test="alarmTime != null "> and alarm_time = #{alarmTime}</if>
            <if test="isInformationPush != null "> and is_information_push = #{isInformationPush}</if>
            <if test="isHandle != null "> and is_handle = #{isHandle}</if>
            <if test="handleResults != null  and handleResults != ''"> and handle_results = #{handleResults}</if>
            <if test="handelTime != null "> and handel_time = #{handelTime}</if>
            <if test="handelBy != null  and handelBy != ''"> and handel_by = #{handelBy}</if>
        </where>
    </select>

    <select id="selectEquipmentAlarmInformationById" parameterType="Long" resultMap="EquipmentAlarmInformationResult">
        <include refid="selectEquipmentAlarmInformationVo"/>
        where id = #{id}
    </select>

    <insert id="insertEquipmentAlarmInformation" parameterType="EquipmentAlarmInformation" useGeneratedKeys="true" keyProperty="id">
        insert into equipment_alarm_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="elderId != null">elder_id,</if>
            <if test="elderName != null and elderName != ''">elder_name,</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone,</if>
            <if test="reasonForAlarm != null">reason_for_alarm,</if>
            <if test="alarmTime != null">alarm_time,</if>
            <if test="isInformationPush != null">is_information_push,</if>
            <if test="isHandle != null">is_handle,</if>
            <if test="handleResults != null">handle_results,</if>
            <if test="handelTime != null">handel_time,</if>
            <if test="handelBy != null">handel_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="elderId != null">#{elderId},</if>
            <if test="elderName != null and elderName != ''">#{elderName},</if>
            <if test="elderPhone != null and elderPhone != ''">#{elderPhone},</if>
            <if test="reasonForAlarm != null">#{reasonForAlarm},</if>
            <if test="alarmTime != null">#{alarmTime},</if>
            <if test="isInformationPush != null">#{isInformationPush},</if>
            <if test="isHandle != null">#{isHandle},</if>
            <if test="handleResults != null">#{handleResults},</if>
            <if test="handelTime != null">#{handelTime},</if>
            <if test="handelBy != null">#{handelBy},</if>
         </trim>
    </insert>

    <update id="disposeEquipmentAlarmInformation" parameterType="EquipmentAlarmInformation">
        update equipment_alarm_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="elderName != null and elderName != ''">elder_name = #{elderName},</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone = #{elderPhone},</if>
            <if test="reasonForAlarm != null">reason_for_alarm = #{reasonForAlarm},</if>
            <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
            <if test="isInformationPush != null">is_information_push = #{isInformationPush},</if>
            <if test="isHandle != null">is_handle = #{isHandle},</if>
            <if test="handleResults != null">handle_results = #{handleResults},</if>
            <if test="handelTime != null">handel_time = #{handelTime},</if>
            <if test="handelBy != null">handel_by = #{handelBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEquipmentAlarmInformationById" parameterType="Long">
        delete from equipment_alarm_information where id = #{id}
    </delete>

    <delete id="deleteEquipmentAlarmInformationByIds" parameterType="String">
        delete from equipment_alarm_information where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
