<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.CommentsMapper">

    <resultMap type="Comments" id="CommentsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsType"    column="goods_type"    />
        <result property="rating"    column="rating"    />
        <result property="adminComment"    column="admin_comment"    />
        <result property="comment"    column="comment"    />
        <result property="hasPicture"    column="has_picture"    />
        <result property="updateAt"    column="update_at"    />
        <result property="createAt"    column="create_at"    />
        <result property="isDel"    column="is_del"    />
        <result column="nick_name" property="nickName" />
        <result column="avatar_url" property="avatarUrl" />
    </resultMap>

    <sql id="selectCommentsVo">
        select id, user_id, goods_id, goods_type, rating, admin_comment, comment, has_picture, pic_urls, create_at, update_at, is_del from comments
    </sql>

    <select id="selectCommentsListApp" parameterType="Comments" resultMap="CommentsResult">
        select cs.*, mu.nick_name, mu.avatar_url from comments cs left join mini_user_info mu on cs.user_id = mu.user_id
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="adminComment != null "> and admin_comment = #{adminComment}</if>
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="goodsType != null  and goodsType != '' or goodsType==0 "> and goods_type = #{goodsType}</if>
            <if test="rating != null  and rating != '' or rating==0 "> and rating = #{rating}</if>
            <if test="comment != null  and comment != ''"> and comment = #{comment}</if>
            <if test="createAt != null ">and DATE(create_at) = DATE(#{createAt, jdbcType=TIMESTAMP})</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            and is_del = 0
        </where>
    </select>

    <select id="selectCommentsList" parameterType="Comments" resultMap="CommentsResult">
        <include refid="selectCommentsVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="adminComment != null "> and admin_comment = #{adminComment}</if>
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="goodsType != null  and goodsType != '' or goodsType==0 "> and goods_type = #{goodsType}</if>
            <if test="rating != null"> and rating = #{rating}</if>
            <if test="comment != null  and comment != ''"> and comment = #{comment}</if>
            <if test="createAt != null ">and DATE(create_at) = DATE(#{createAt, jdbcType=TIMESTAMP})</if>
            <if test="updateAt != null "> and update_at = #{updateAt}</if>
            and is_del = 0
        </where>
    </select>

    <select id="selectCommentsById" parameterType="Long" resultMap="CommentsResult">
        <include refid="selectCommentsVo"/>
        where id = #{id}
    </select>

    <insert id="insertComments" parameterType="Comments" useGeneratedKeys="true" keyProperty="id">
        insert into comments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="adminComment != null">admin_comment,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="goodsType != null">goods_type,</if>
            <if test="rating != null and rating != ''">rating,</if>
            <if test="comment != null">comment,</if>
            <if test="hasPicture != null">has_picture,</if>
            <if test="picUrls != null">pic_urls,</if>
            <if test="createAt != null">create_at,</if>
            <if test="updateAt != null">update_at,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="adminComment != null">admin_comment,</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="goodsType != null">#{goodsType},</if>
            <if test="rating != null and rating != ''">#{rating},</if>
            <if test="comment != null">#{comment},</if>
            <if test="hasPicture != null">has_picture,</if>
            <if test="picUrls != null">pic_urls,</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="updateAt != null">#{updateAt},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateComments" parameterType="Comments">
        update comments
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="adminComment != null ">admin_comment = #{adminComment}</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="goodsType != null and goodsType != ''">goods_type = #{goodsType},</if>
            <if test="rating != null and rating != ''">rating = #{rating},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="hasPicture != null "> and has_picture = #{hasPicture}</if>
            <if test="picUrls != null "> and pic_urls = #{picUrls}</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommentsById" parameterType="Long">
        update comments set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteCommentsByIds" parameterType="String">
        update comments set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
