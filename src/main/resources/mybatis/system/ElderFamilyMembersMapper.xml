<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ElderFamilyMembersMapper">

    <resultMap type="ElderFamilyMembers" id="ElderFamilyMembersResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="relationship"    column="relationship"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="phone"    column="phone"    />
        <result property="address"    column="address"    />
    </resultMap>

    <sql id="selectElderFamilyMembersVo">
        select id, elder_id, relationship, name, sex, phone, address from elder_family_members
    </sql>

    <select id="selectElderFamilyMembersList" parameterType="ElderFamilyMembers" resultMap="ElderFamilyMembersResult">
        <include refid="selectElderFamilyMembersVo"/>
        <where>
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="relationship != null  and relationship != ''"> and relationship = #{relationship}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
        </where>
    </select>

    <select id="selectElderFamilyMembersById" parameterType="Long" resultMap="ElderFamilyMembersResult">
        <include refid="selectElderFamilyMembersVo"/>
        where id = #{id}
    </select>

    <insert id="insertElderFamilyMembers" parameterType="ElderFamilyMembers">
        insert into elder_family_members
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="elderId != null">elder_id,</if>
            <if test="relationship != null">relationship,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sex != null">sex,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="address != null">address,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="elderId != null">#{elderId},</if>
            <if test="relationship != null">#{relationship},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="address != null">#{address},</if>
         </trim>
    </insert>

    <update id="updateElderFamilyMembers" parameterType="ElderFamilyMembers">
        update elder_family_members
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="relationship != null">relationship = #{relationship},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElderFamilyMembersById" parameterType="Long">
        delete from elder_family_members where id = #{id}
    </delete>

    <delete id="deleteElderFamilyMembersByIds" parameterType="String">
        delete from elder_family_members where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteElderFamilyMembersByElderIds">
        delete from elder_family_members where elder_id in
        <foreach item="elderId" collection="array" open="(" separator="," close=")">
            #{elderId}
        </foreach>
    </delete>
</mapper>
