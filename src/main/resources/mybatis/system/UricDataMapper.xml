<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.UricDataMapper">
    
    <resultMap type="UricData" id="UricDataResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="elderPhone"    column="elder_phone"    />
        <result property="deviceId"    column="device_id"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="uricValues"    column="uric_values"    />
        <result property="uricTestResults"    column="uric_test_results"    />
    </resultMap>

    <sql id="selectUricDataVo">
        select id, elder_id, elder_phone, device_id, detection_time, uric_values, uric_test_results from uric_data
    </sql>

    <select id="selectUricDataList" parameterType="UricData" resultMap="UricDataResult">
        <include refid="selectUricDataVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="elderPhone != null  and elderPhone != ''"> and elder_phone = #{elderPhone}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="uricValues != null "> and uric_values = #{uricValues}</if>
            <if test="uricTestResults != null "> and uric_test_results = #{uricTestResults}</if>
        </where>
    </select>
    
    <select id="selectUricDataById" parameterType="Long" resultMap="UricDataResult">
        <include refid="selectUricDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUricData" parameterType="UricData" useGeneratedKeys="true" keyProperty="id">
        insert into uric_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="uricValues != null">uric_values,</if>
            <if test="uricTestResults != null">uric_test_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">#{elderPhone},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="uricValues != null">#{uricValues},</if>
            <if test="uricTestResults != null">#{uricTestResults},</if>
         </trim>
    </insert>

    <update id="updateUricData" parameterType="UricData">
        update uric_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone = #{elderPhone},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="uricValues != null">uric_values = #{uricValues},</if>
            <if test="uricTestResults != null">uric_test_results = #{uricTestResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUricDataById" parameterType="Long">
        delete from uric_data where id = #{id}
    </delete>

    <delete id="deleteUricDataByIds" parameterType="String">
        delete from uric_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>