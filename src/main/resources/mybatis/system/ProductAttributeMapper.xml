<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ProductAttributeMapper">
  <resultMap id="BaseResultMap" type="com.wanou.project.system.domain.ProductAttribute">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_attribute_category_id" jdbcType="BIGINT" property="productAttributeCategoryId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="select_type" jdbcType="INTEGER" property="selectType" />
    <result column="input_type" jdbcType="INTEGER" property="inputType" />
    <result column="input_list" jdbcType="VARCHAR" property="inputList" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="filter_type" jdbcType="INTEGER" property="filterType" />
    <result column="search_type" jdbcType="INTEGER" property="searchType" />
    <result column="related_status" jdbcType="INTEGER" property="relatedStatus" />
    <result column="hand_add_status" jdbcType="INTEGER" property="handAddStatus" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, product_attribute_category_id, name, select_type, input_type, input_list, sort, 
    filter_type, search_type, related_status, hand_add_status, type
  </sql>
  <select id="selectProductAttribute" parameterType="com.wanou.project.system.domain.ProductAttribute" resultMap="BaseResultMap">
    select distinct
    <include refid="Base_Column_List" />
    from product_attribute
    <where>
      <if test="productAttributeCategoryId != null"> and product_attribute_category_id = #{productAttributeCategoryId,jdbcType=BIGINT}</if>
      <if test="name != null"> and name = #{name,jdbcType=VARCHAR}</if>
      <if test="type != null"> and type = #{type,jdbcType=INTEGER}</if>
    </where>
  </select>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_attribute
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteById" parameterType="java.lang.Long">
    delete from product_attribute
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertProductAttribute" parameterType="com.wanou.project.system.domain.ProductAttribute">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_attribute
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productAttributeCategoryId != null">
        product_attribute_category_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="selectType != null">
        select_type,
      </if>
      <if test="inputType != null">
        input_type,
      </if>
      <if test="inputList != null">
        input_list,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="filterType != null">
        filter_type,
      </if>
      <if test="searchType != null">
        search_type,
      </if>
      <if test="relatedStatus != null">
        related_status,
      </if>
      <if test="handAddStatus != null">
        hand_add_status,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productAttributeCategoryId != null">
        #{productAttributeCategoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="selectType != null">
        #{selectType,jdbcType=INTEGER},
      </if>
      <if test="inputType != null">
        #{inputType,jdbcType=INTEGER},
      </if>
      <if test="inputList != null">
        #{inputList,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="filterType != null">
        #{filterType,jdbcType=INTEGER},
      </if>
      <if test="searchType != null">
        #{searchType,jdbcType=INTEGER},
      </if>
      <if test="relatedStatus != null">
        #{relatedStatus,jdbcType=INTEGER},
      </if>
      <if test="handAddStatus != null">
        #{handAddStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateById" parameterType="com.wanou.project.system.domain.ProductAttribute">
    update product_attribute
    <set>
      <if test="productAttributeCategoryId != null">
        product_attribute_category_id = #{productAttributeCategoryId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="selectType != null">
        select_type = #{selectType,jdbcType=INTEGER},
      </if>
      <if test="inputType != null">
        input_type = #{inputType,jdbcType=INTEGER},
      </if>
      <if test="inputList != null">
        input_list = #{inputList,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="filterType != null">
        filter_type = #{filterType,jdbcType=INTEGER},
      </if>
      <if test="searchType != null">
        search_type = #{searchType,jdbcType=INTEGER},
      </if>
      <if test="relatedStatus != null">
        related_status = #{relatedStatus,jdbcType=INTEGER},
      </if>
      <if test="handAddStatus != null">
        hand_add_status = #{handAddStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>