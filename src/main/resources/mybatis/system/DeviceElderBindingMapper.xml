<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.DeviceElderBindingMapper">

    <resultMap type="DeviceElderBinding" id="DeviceElderBindingResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="bindingTime"    column="binding_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <resultMap id="DeviceElderBindingVoResult" type="DeviceElderBindingVo" extends="DeviceElderBindingResult">
        <result property="elderName" column="elder_name" />
        <result property="elderPhone" column="elder_phone" />
    </resultMap>

    <sql id="selectDeviceElderBindingVo">
        SELECT
            t1.id,
            t1.device_id,
            t1.elder_id,
            t1.binding_time,
            t1.create_by,
            t2.`name` elder_name,
            t2.phone elder_phone
        FROM
            device_elder_binding t1
            LEFT JOIN elder_base_info t2 ON t2.id = t1.elder_id
            LEFT JOIN sys_dept t3 ON t3.dept_id = t2.dept_id
    </sql>

    <select id="selectDeviceElderBindingList" parameterType="DeviceElderBindingDto" resultMap="DeviceElderBindingVoResult">
        <include refid="selectDeviceElderBindingVo"/>
        <where>
            <if test="deviceId != null "> and t1.device_id = #{deviceId}</if>
            <if test="elderId != null "> and t1.elder_id = #{elderId}</if>
            <if test="bindingTime != null "> and t1.binding_time = #{bindingTime}</if>
            <if test="queryStartTime != null"> and t1.binding_time >= #{queryStartTime} </if>
            <if test="queryEndTime != null"> and t1.binding_time &lt;= #{queryEndTime} </if>
            <if test="deptId != null">and (t3.dept_id = #{deptId} or find_in_set(#{deptId}, t3.ancestors))</if>
        </where>
    </select>

    <select id="selectDeviceElderBindingById" parameterType="Long" resultMap="DeviceElderBindingResult">
        <include refid="selectDeviceElderBindingVo"/>
        where t1.id = #{id}
    </select>

    <select id="selectDeviceElderBindingListByIds"
            resultType="com.wanou.project.system.domain.vo.DeviceElderBindingVo" resultMap="DeviceElderBindingVoResult">
        <include refid="selectDeviceElderBindingVo"/>
        where t1.device_id in
        <foreach item="deviceId" collection="deviceIds" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
    <select id="selectDeviceElderBindingByDeviceId"
            resultType="DeviceElderBindingVo" resultMap="DeviceElderBindingVoResult">
        select id, device_id, elder_id, binding_time, create_by
        from device_elder_binding
        where device_id = #{id}
    </select>
    <select id="selectDeviceElderBindingByElderId"  resultType="com.wanou.project.system.domain.DeviceElderBinding" resultMap="DeviceElderBindingResult">
    select id, device_id, elder_id, binding_time, create_by from  device_elder_binding where elder_id = #{id}

    </select>


    <insert id="insertDeviceElderBinding" parameterType="DeviceElderBinding" useGeneratedKeys="true" keyProperty="id">
        insert into device_elder_binding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="elderId != null">elder_id,</if>
            <if test="bindingTime != null">binding_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="elderId != null">#{elderId},</if>
            <if test="bindingTime != null">#{bindingTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateDeviceElderBinding" parameterType="DeviceElderBinding">
        update device_elder_binding
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="bindingTime != null">binding_time = #{bindingTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceElderBindingById" parameterType="Long">
        delete from device_elder_binding where id = #{id}
    </delete>

    <delete id="deleteDeviceElderBindingByIds" parameterType="String">
        delete from device_elder_binding where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDeviceElderBindingByDeviceId">
        delete from device_elder_binding where device_id = #{deviceId}
    </delete>
    <delete id="unbind">
        delete from device_elder_binding where device_id = #{deviceId} and elder_id = #{elderId}
    </delete>
</mapper>

