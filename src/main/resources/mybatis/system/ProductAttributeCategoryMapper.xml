<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.ProductAttributeCategoryMapper">
  <resultMap id="BaseResultMap" type="com.wanou.project.system.domain.ProductAttributeCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="attribute_count" jdbcType="INTEGER" property="attributeCount" />
    <result column="param_count" jdbcType="INTEGER" property="paramCount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, attribute_count, param_count
  </sql>
  <select id="selectProductAttributeCategory" parameterType="com.wanou.project.system.domain.ProductAttributeCategory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from product_attribute_category
    <where>
      <if test="id != null">and id = #{id,jdbcType=BIGINT}</if>
      <if test="name != null">and name = #{name,jdbcType=VARCHAR}</if>
      <if test="attributeCount != null">and attribute =_count = #{attributeCount,jdbcType=INTEGER}</if>
      <if test="paramCount != null">and param_count = #{paramCount,jdbcType=INTEGER}</if>
    </where>
  </select>
  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_attribute_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertProductAttributeCategory" parameterType="com.wanou.project.system.domain.ProductAttributeCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_attribute_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="attributeCount != null">
        attribute_count,
      </if>
      <if test="paramCount != null">
        param_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="attributeCount != null">
        #{attributeCount,jdbcType=INTEGER},
      </if>
      <if test="paramCount != null">
        #{paramCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wanou.project.system.domain.ProductAttributeCategory">
    update product_attribute_category
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="attributeCount != null">
        attribute_count = #{attributeCount,jdbcType=INTEGER},
      </if>
      <if test="paramCount != null">
        param_count = #{paramCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wanou.project.system.domain.ProductAttributeCategory">
    update product_attribute_category
    set name = #{name,jdbcType=VARCHAR},
      attribute_count = #{attributeCount,jdbcType=INTEGER},
      param_count = #{paramCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteById" parameterType="java.lang.Long">
    delete from product_attribute_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>