<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.BloodPressureDataMapper">
    
    <resultMap type="BloodPressureData" id="BloodPressureDataResult">
        <result property="id"    column="id"    />
        <result property="elderId"    column="elder_id"    />
        <result property="elderPhone"    column="elder_phone"    />
        <result property="deviceId"    column="device_id"    />
        <result property="highPressure"    column="high_pressure"    />
        <result property="lowPressure"    column="low_pressure"    />
        <result property="pulse"    column="pulse"    />
        <result property="detectionTime"    column="detection_time"    />
        <result property="bloodPressureTestResults"    column="blood_pressure_test_results"    />
    </resultMap>

    <sql id="selectBloodPressureDataVo">
        select id, elder_id, elder_phone, device_id, high_pressure, low_pressure, pulse, detection_time, blood_pressure_test_results from blood_pressure_data
    </sql>

    <select id="selectBloodPressureDataList" parameterType="BloodPressureData" resultMap="BloodPressureDataResult">
        <include refid="selectBloodPressureDataVo"/>
        <where>  
            <if test="elderId != null "> and elder_id = #{elderId}</if>
            <if test="elderPhone != null  and elderPhone != ''"> and elder_phone = #{elderPhone}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="highPressure != null "> and high_pressure = #{highPressure}</if>
            <if test="lowPressure != null "> and low_pressure = #{lowPressure}</if>
            <if test="pulse != null "> and pulse = #{pulse}</if>
            <if test="detectionTime != null "> and detection_time = #{detectionTime}</if>
            <if test="bloodPressureTestResults != null "> and blood_pressure_test_results = #{bloodPressureTestResults}</if>
        </where>
    </select>
    
    <select id="selectBloodPressureDataById" parameterType="Long" resultMap="BloodPressureDataResult">
        <include refid="selectBloodPressureDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBloodPressureData" parameterType="BloodPressureData" useGeneratedKeys="true" keyProperty="id">
        insert into blood_pressure_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elderId != null">elder_id,</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="highPressure != null">high_pressure,</if>
            <if test="lowPressure != null">low_pressure,</if>
            <if test="pulse != null">pulse,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="bloodPressureTestResults != null">blood_pressure_test_results,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elderId != null">#{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">#{elderPhone},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="highPressure != null">#{highPressure},</if>
            <if test="lowPressure != null">#{lowPressure},</if>
            <if test="pulse != null">#{pulse},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="bloodPressureTestResults != null">#{bloodPressureTestResults},</if>
         </trim>
    </insert>

    <update id="updateBloodPressureData" parameterType="BloodPressureData">
        update blood_pressure_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="elderPhone != null and elderPhone != ''">elder_phone = #{elderPhone},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="highPressure != null">high_pressure = #{highPressure},</if>
            <if test="lowPressure != null">low_pressure = #{lowPressure},</if>
            <if test="pulse != null">pulse = #{pulse},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="bloodPressureTestResults != null">blood_pressure_test_results = #{bloodPressureTestResults},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBloodPressureDataById" parameterType="Long">
        delete from blood_pressure_data where id = #{id}
    </delete>

    <delete id="deleteBloodPressureDataByIds" parameterType="String">
        delete from blood_pressure_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>