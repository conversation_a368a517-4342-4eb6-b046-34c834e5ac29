<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.system.mapper.MiniUserInfoMapper">
    <resultMap id="MiniUserInfoResult" type="com.wanou.project.system.domain.MiniUserInfo">
        <result column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="union_id" property="unionId" />
        <result column="user_id" property="userId" />
        <result column="reg_time" property="regTime" />
        <result column="status" property="status" />
        <result column="nick_name" property="nickName" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="sex" property="sex" />
        <result column="age" property="age" />
        <result column="birthday" property="birthday" />
        <result column="elder_id" property="elderId" />
        <result column="elder_name" property="elderName" />
        <result column="elder_phone" property="elderPhone" />
        <result column="elder_id_card" property="elderIdCard" />
    </resultMap>
    <resultMap id="MiniUserInfoName" type="com.wanou.project.system.domain.MiniUserInfo">
        <result column="elder_name" property="elderName" />
    </resultMap>

    <select id="selectMiniUserInfoByOpenId" parameterType="string" resultType="com.wanou.project.system.domain.MiniUserInfo" resultMap="MiniUserInfoResult">
        select id,open_id,union_id,user_id,reg_time,`status` from mini_user_info where open_id=#{openid}
    </select>

    <select id="selectByDeptIds" resultMap="MiniUserInfoResult">
        SELECT
        	t1.id,
        	t1.open_id,
        	t1.union_id,
        	t1.user_id,
        	t1.reg_time,
        	t1.`status`
        FROM
        	mini_user_info t1
        	LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
        WHERE
        	t2.dept_id IN
        	<foreach collection="deptIdArr" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
    </select>
    <select id="selectByUserId" resultType="com.wanou.project.system.domain.MiniUserInfo" resultMap="MiniUserInfoResult">
     select * from mini_user_info where user_id=#{userId}
    </select>
    <select id="selectList" resultType="com.wanou.project.system.domain.MiniUserInfo"  resultMap="MiniUserInfoName">
        select elder_name from mini_user_info
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into mini_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="unionId != null and unionId != ''">union_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="regTime != null">reg_time,</if>
            <if test="status != null">`status`,</if>
            <if test="nickName != null">`nick_name`,</if>
            <if test="avatarUrl != null">`avatar_url`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="unionId != null and unionId != ''">#{unionId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="regTime != null">#{regTime},</if>
            <if test="status != null">#{status},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
        </trim>
    </insert>

    <update id="update">
        update mini_user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="unionId != null and unionId != ''">union_id = #{unionId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="regTime != null">reg_time = #{regTime},</if>
            <if test="status != null">`status` = #{status},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateById" parameterType="com.wanou.project.system.domain.MiniUserInfo">
        update mini_user_info set nick_name=#{nickName},avatar_url=#{avatarUrl},sex=#{sex},age=#{age},birthday=#{birthday},elder_name=#{elderName} where user_id=#{userId}
    </update>
    <update id="updateMiniUserInfo" parameterType="com.wanou.project.system.domain.MiniUserInfo">
        update mini_user_info set sex=#{sex},age=#{age},birthday=#{birthday},elder_name=#{elderName}
        ,elder_id=#{elderId},elder_phone=#{elderPhone},elder_id_card=#{elderIdCard}
        where user_id=#{userId}
    </update>
</mapper>
