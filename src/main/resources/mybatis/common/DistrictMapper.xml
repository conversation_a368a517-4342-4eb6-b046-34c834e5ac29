<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanou.project.common.mapper.DistrictMapper">

    <resultMap type="District" id="DistrictResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="adcode" column="adcode"    />
        <result property="level"    column="level"    />
        <result property="country"    column="country"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
    </resultMap>


    <select id="getDistrict" resultType="com.wanou.project.common.domain.District">
        select * from district
        <where>
            <if test="level != null and level != ''">
                and level = #{level}
                <if test="level == 'city'">
                    and province = #{province}
                </if>
                <if test="level == 'district'">
                    and city = #{city}
                </if>
            </if>
        </where>
    </select>

    <insert id="batchInsertOrUpdate">
        INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
        <foreach collection="districts" item="item" separator=",">
            (#{item.name},#{item.adcode},#{item.level},#{item.country},#{item.province},#{item.city},#{item.district})
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)
    </insert>
</mapper>
