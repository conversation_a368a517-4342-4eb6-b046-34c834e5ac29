# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
#        url: **************************************************************************************************************************************************************************
        url: jdbc:mysql://**************:23306/retirement?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true
        username: root
        password: Wanou@2008
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        slf4j:
          enabled: true
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
# Swagger配置
knife4j:
  # 增强功能开启
  enable: true
swagger:
  # 是否开启swagger
  enabled: true
  swagger-path: com.wanou.project.system.controller
  title: 养老中心接口文档
  description: 智慧养老
  contact-name: 万欧科技有限公司
  contact-url: http://www.wanou.cn
  contact-email: zhoujie.cn
  version: v1.0
# 日志配置
#swagger:
#  swagger-path: com.wanou.project.system.controller
#  title: 智慧养老服务
#  description: 智慧养老
#  contact-name: 万欧科技有限公司
#logging:
#  level:
#    com.wanou: debug
#    org.springframework: warn
unzipPath: D:\unzip
zipSavePath: D:\zipSave

link:
  url: http://**************:8086
  username: internal_retirement
  password: MXDcg877yXMx6VB0
  uuid: 1ac51e14a5334e87ad45002048cf3ac0
  deptId: 207
mybatis-plus:
    global-config:
      db-config:
        update-strategy: not_null
        id-type: auto


