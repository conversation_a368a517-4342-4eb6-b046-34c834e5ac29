package com.wanou.test;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import net.coobird.thumbnailator.Thumbnails;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.test.context.junit4.SpringRunner;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2021/8/12 14:27
 */

public class DemoTest {

    @Test
    public void test1() throws IOException {
        // ImgUtil.compress(new File("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg"),new File("C:\\Users\\<USER>\\Desktop\\compress.jpg"),0.1F);
        //ImgUtil.scale(new File("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg"),new File("C:\\Users\\<USER>\\Desktop\\compress.jpg"),480,640,null);
        //updateImage("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg","C:\\Users\\<USER>\\Desktop\\compress.jpg",480);
        //zipImageFile(new File("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg"),new File("C:\\Users\\<USER>\\Desktop\\compress.jpg"),480,640,1F);
        BufferedImage read = ImgUtil.read(new File("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg"));
        int width = read.getWidth();
        double div = NumberUtil.div(width, 480, 2);
        //float scale =  width / 480F;
        //Image img = ImgUtil.scale(read, 0.25F);
        //ImgUtil.write(img,new File("C:\\Users\\<USER>\\Desktop\\compress.jpg"));

        Thumbnails.of(new File("C:\\Users\\<USER>\\Desktop\\IMG_20210812_141852.jpg")).size(640,480).toFile(new File("C:\\Users\\<USER>\\Desktop\\compress.jpg"));
    }

    @Test
    public void test2() throws InterruptedException, FileNotFoundException {
        String url = "https://restapi.amap.com/v3/config/district?key=33f10ccdd03ca3f08bba0ca0b1c1c705&subdistrict=3";
        String body = HttpRequest.get(url).execute().body();
        //todo
        JSONObject res = JSON.parseObject(body);
        if(!"1".equals(res.getString("status")) || !"10000".equals(res.getString("infocode"))){
            System.out.println("请求失败");
            return;
        }
        JSONArray districts = res.getJSONArray("districts");
        if(CollUtil.isEmpty(districts)){
            System.out.println("无数据");
            return;
        }
        JSONObject countryData = districts.getJSONObject(0); //国家数据
        if(countryData == null){
            System.out.println("国家数据请求失败");
            return;
        }
        JSONArray provinces = countryData.getJSONArray("districts"); //省份数据
        if(CollUtil.isEmpty(provinces)){
            System.out.println("无省份数据");
            return;
        }
        Map<String,JSONObject> provincesMap = new HashMap<>();
        provinces.forEach((province) -> {
            JSONObject bean = BeanUtil.toBean(province, JSONObject.class);
            provincesMap.put(bean.getString("name"),bean);
        });
        System.out.println(provincesMap);
    }

    @Test
    public void test3() throws InterruptedException {
        DateTime now = DateUtil.date();
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.offsetDay(now, -6), now, DateField.DAY_OF_MONTH);
        System.out.println(dateTimes);
    }

    @Async
    public void syncTest() throws InterruptedException {
        Thread.sleep(1000L);
        System.out.println("异步执行");
    }

    @Test
    public void test4(){
        String url = "http://www.netbian.com/mei/";


        int index = 1;
        int ii = 0;
        while (true){
            String body = HttpRequest.get(url).execute().body();
            Document parse = Jsoup.parse(body);
            Elements select = parse.select("img[src]");
            for (int i = (index*20)-19; i <= index*20; i++) {
                String src = select.get(ii++).attr("src");
                if(!src.endsWith("gif") && src.startsWith("http")){
                    //保存
                    InputStream inputStream = HttpRequest.get(src).execute().bodyStream();
                    FileUtil.writeFromStream(inputStream,new File("D:\\img\\" + i + ".jpg"));
                }
            }
            index++;
            ii = 0;
            url = "http://www.netbian.com/mei/index_" + index + ".htm";

            if((index*20) >= 1300){
                break;
            }
        }

    }

    @Test
    public void test5() throws IOException {
        //d41d8cd98f00b204e9800998ecf8427e
        FileInputStream fileInputStream = IoUtil.toStream(new File("D:\\img\\430481098912034722.jpg"));
        byte[] bytes1 = IoUtil.readBytes(fileInputStream,false);
        String md5 = SecureUtil.md5(new File("D:\\img\\430481098912034722.jpg"));
        System.out.println(bytes1.length);
        fileInputStream.close();
    }
}
