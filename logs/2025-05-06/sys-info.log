08:43:23.166 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
08:43:23.187 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 31632 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by <PERSON><PERSON> in D:\wangoproject\zhyl\retirement_service_djf2)
08:43:23.188 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
08:43:28.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
08:43:28.274 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:43:28.275 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
08:43:28.441 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:43:35.968 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
08:43:36.477 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
08:44:04.153 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
08:44:04.719 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:44:04.734 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:44:04.734 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:44:04.743 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1746492244721'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

08:44:04.743 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
08:44:04.743 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:44:04.744 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7bb888b7
08:44:17.108 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
08:44:17.139 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 54.285 seconds (JVM running for 56.688)
08:44:18.500 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1746492244721 started.
11:05:10.672 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
11:12:58.051 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
12:23:45.484 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
13:31:36.935 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
16:04:00.740 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
16:17:48.755 [Druid-ConnectionPool-Create-2089779261] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
17:08:07.165 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:08:49.905 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1746492244721 paused.
17:08:49.933 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1746492244721 shutting down.
17:08:49.934 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1746492244721 paused.
17:08:49.937 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1746492244721 shutdown complete.
17:08:49.938 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:08:49.952 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
17:08:49.963 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
