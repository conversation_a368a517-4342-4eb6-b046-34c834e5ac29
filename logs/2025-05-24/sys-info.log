14:47:01.313 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
14:47:01.357 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 40292 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by <PERSON><PERSON> in D:\wangoproject\zhyl\retirement_service_djf2)
14:47:01.360 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
14:47:06.889 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:47:06.889 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:47:06.890 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
14:47:07.301 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:47:19.186 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
14:47:20.090 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
14:47:26.863 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
14:47:27.302 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:47:27.328 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:47:27.329 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:47:27.342 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748069247306'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:47:27.343 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
14:47:27.343 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:47:27.347 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@78c91d2a
14:47:38.323 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:47:38.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-8080"]
14:47:38.352 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:47:38.362 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-8080"]
14:47:38.362 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-8080"]
14:47:38.387 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748069247306 shutting down.
14:47:38.388 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748069247306 paused.
14:47:38.388 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748069247306 shutdown complete.
14:47:38.389 [main] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:47:38.395 [main] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
14:47:38.402 [main] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
14:48:15.466 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
14:48:15.495 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 41396 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
14:48:15.496 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
14:48:18.832 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:48:18.833 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:18.833 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
14:48:19.009 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:23.894 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
14:48:24.240 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
14:48:28.862 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
14:48:29.113 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:48:29.125 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:48:29.125 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:48:29.132 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748069309115'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:48:29.132 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
14:48:29.132 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:48:29.134 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@31c800a5
14:48:36.293 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:48:36.324 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.222 seconds (JVM running for 23.213)
14:48:37.504 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748069309115 started.
14:48:53.460 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:48:54.125 [http-nio-8080-exec-1] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:25:16.261 [http-nio-8080-exec-19] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:57:59.793 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
15:57:59.812 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 12116 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
15:57:59.812 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
15:58:02.713 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
15:58:02.713 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:58:02.713 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
15:58:02.877 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:58:07.469 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
15:58:07.875 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
15:58:12.790 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
15:58:13.047 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:58:13.057 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:58:13.057 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:58:13.067 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748073493047'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:58:13.067 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
15:58:13.067 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:58:13.067 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@63e4bbf9
15:58:21.458 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
15:58:21.490 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.988 seconds (JVM running for 23.67)
15:58:22.789 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748073493047 started.
16:00:04.540 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:00:23.265 [http-nio-8080-exec-7] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
16:23:50.514 [http-nio-8080-exec-11] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
16:30:01.627 [http-nio-8080-exec-23] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
16:38:48.032 [http-nio-8080-exec-1] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
17:17:38.173 [http-nio-8080-exec-21] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
