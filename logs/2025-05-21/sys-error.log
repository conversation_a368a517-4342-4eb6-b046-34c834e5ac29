11:12:28.498 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1747796228927_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1239 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1747797147251,"LAPTOP-JHBER3JS1747796228927"]
15:36:36.449 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1747796228927_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1231 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1747812983216,"WAITING"]
15:40:01.462 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1747796228927_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1482 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1747813187979,"WAITING"]
15:42:57.005 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 6868 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1747813398139,1747813358135]
15:43:22.487 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1183 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1747813430920,1747813389303]
