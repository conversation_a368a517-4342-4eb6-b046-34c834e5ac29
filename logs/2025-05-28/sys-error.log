08:56:41.303 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1119 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["老城区","410302","district",null,"410000","410300",null,"西工区","410303","district",null,"410000","410300",null,"瀍河回族区","410304","district",null,"410000","410300",null,"涧西区","410305","district",null,"410000","410300",null,"偃师区","410307","district",null,"410000","410300",null,"孟津区","410308","district",null,"410000","410300",null,"洛龙区","410311","district",null,"410000","410300",null,"新安县","410323","district",null,"410000","410300",null,"栾川县","410324","district",null,"410000","410300",null,"嵩县","410325","district",null,"410000","410300",null,"汝阳县","410326","district",null,"410000","410300",null,"宜阳县","410327","district",null,"410000","410300",null,"洛宁县","410328","district",null,"410000","410300",null,"伊川县","410329","district",null,"410000","410300",null,"源汇区","411102","district",null,"410000","411100",null,"郾城区","411103","district",null,"410000","411100",null,"召陵区","411104","district",null,"410000","411100",null,"舞阳县","411121","district",null,"410000","411100",null,"临颍县","411122","district",null,"410000","411100",null,"宛城区","411302","district",null,"410000","411300",null,"卧龙区","411303","district",null,"410000","411300",null,"南召县","411321","district",null,"410000","411300",null,"方城县","411322","district",null,"410000","411300",null,"西峡县","411323","district",null,"410000","411300",null,"镇平县","411324","district",null,"410000","411300",null,"内乡县","411325","district",null,"410000","411300",null,"淅川县","411326","district",null,"410000","411300",null,"社旗县","411327","district",null,"410000","411300",null,"唐河县","411328","district",null,"410000","411300",null,"新野县","411329","district",null,"410000","411300",null,"桐柏县","411330","district",null,"410000","411300",null,"邓州市","411381","district",null,"410000","411300",null,"浉河区","411502","district",null,"410000","411500",null,"平桥区","411503","district",null,"410000","411500",null,"罗山县","411521","district",null,"410000","411500",null,"光山县","411522","district",null,"410000","411500",null,"新县","411523","district",null,"410000","411500",null,"商城县","411524","district",null,"410000","411500",null,"固始县","411525","district",null,"410000","411500",null,"潢川县","411526","district",null,"410000","411500",null,"淮滨县","411527","district",null,"410000","411500",null,"息县","411528","district",null,"410000","411500",null,"华龙区","410902","district",null,"410000","410900",null,"清丰县","410922","district",null,"410000","410900",null,"南乐县","410923","district",null,"410000","410900",null,"范县","410926","district",null,"410000","410900",null,"台前县","410927","district",null,"410000","410900",null,"濮阳县","410928","district",null,"410000","410900",null,"中原区","410102","district",null,"410000","410100",null,"二七区","410103","district",null,"410000","410100",null,"管城回族区","410104","district",null,"410000","410100",null,"金水区","410105","district",null,"410000","410100",null,"上街区","410106","district",null,"410000","410100",null,"惠济区","410108","district",null,"410000","410100",null,"中牟县","410122","district",null,"410000","410100",null,"巩义市","410181","district",null,"410000","410100",null,"荥阳市","410182","district",null,"410000","410100",null,"新密市","410183","district",null,"410000","410100",null,"新郑市","410184","district",null,"410000","410100",null,"登封市","410185","district",null,"410000","410100",null,"川汇区","411602","district",null,"410000","411600",null,"淮阳区","411603","district",null,"410000","411600",null,"扶沟县","411621","district",null,"410000","411600",null,"西华县","411622","district",null,"410000","411600",null,"商水县","411623","district",null,"410000","411600",null,"沈丘县","411624","district",null,"410000","411600",null,"郸城县","411625","district",null,"410000","411600",null,"太康县","411627","district",null,"410000","411600",null,"鹿邑县","411628","district",null,"410000","411600",null,"项城市","411681","district",null,"410000","411600",null,"驿城区","411702","district",null,"410000","411700",null,"西平县","411721","district",null,"410000","411700",null,"上蔡县","411722","district",null,"410000","411700",null,"平舆县","411723","district",null,"410000","411700",null,"正阳县","411724","district",null,"410000","411700",null,"确山县","411725","district",null,"410000","411700",null,"泌阳县","411726","district",null,"410000","411700",null,"汝南县","411727","district",null,"410000","411700",null,"遂平县","411728","district",null,"410000","411700",null,"新蔡县","411729","district",null,"410000","411700",null,"新华区","410402","district",null,"410000","410400",null,"卫东区","410403","district",null,"410000","410400",null,"石龙区","410404","district",null,"410000","410400",null,"湛河区","410411","district",null,"410000","410400",null,"宝丰县","410421","district",null,"410000","410400",null,"叶县","410422","district",null,"410000","410400",null,"鲁山县","410423","district",null,"410000","410400",null,"郏县","410425","district",null,"410000","410400",null,"舞钢市","410481","district",null,"410000","410400",null,"汝州市","410482","district",null,"410000","410400",null,"湖滨区","411202","district",null,"410000","411200",null,"陕州区","411203","district",null,"410000","411200",null,"渑池县","411221","district",null,"410000","411200",null,"卢氏县","411224","district",null,"410000","411200",null,"义马市","411281","district",null,"410000","411200",null,"灵宝市","411282","district",null,"410000","411200",null,"坡头镇","419001","street",null,"410000","419001",null,"梨林镇","419001","street",null,"410000","419001",null,"思礼镇","419001","street",null,"410000","419001",null,"五龙口镇","419001","street",null,"410000","419001",null]
08:56:42.519 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1205 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["王屋镇","419001","street",null,"410000","419001",null,"玉泉街道","419001","street",null,"410000","419001",null,"轵城镇","419001","street",null,"410000","419001",null,"济水街道","419001","street",null,"410000","419001",null,"沁园街道","419001","street",null,"410000","419001",null,"下冶镇","419001","street",null,"410000","419001",null,"克井镇","419001","street",null,"410000","419001",null,"天坛街道","419001","street",null,"410000","419001",null,"邵原镇","419001","street",null,"410000","419001",null,"北海街道","419001","street",null,"410000","419001",null,"承留镇","419001","street",null,"410000","419001",null,"大峪镇","419001","street",null,"410000","419001",null,"红旗区","410702","district",null,"410000","410700",null,"卫滨区","410703","district",null,"410000","410700",null,"凤泉区","410704","district",null,"410000","410700",null,"牧野区","410711","district",null,"410000","410700",null,"新乡县","410721","district",null,"410000","410700",null,"获嘉县","410724","district",null,"410000","410700",null,"原阳县","410725","district",null,"410000","410700",null,"延津县","410726","district",null,"410000","410700",null,"封丘县","410727","district",null,"410000","410700",null,"卫辉市","410781","district",null,"410000","410700",null,"辉县市","410782","district",null,"410000","410700",null,"长垣市","410783","district",null,"410000","410700",null,"解放区","410802","district",null,"410000","410800",null,"中站区","410803","district",null,"410000","410800",null,"马村区","410804","district",null,"410000","410800",null,"山阳区","410811","district",null,"410000","410800",null,"修武县","410821","district",null,"410000","410800",null,"博爱县","410822","district",null,"410000","410800",null,"武陟县","410823","district",null,"410000","410800",null,"温县","410825","district",null,"410000","410800",null,"沁阳市","410882","district",null,"410000","410800",null,"孟州市","410883","district",null,"410000","410800",null,"文峰区","410502","district",null,"410000","410500",null,"北关区","410503","district",null,"410000","410500",null,"殷都区","410505","district",null,"410000","410500",null,"龙安区","410506","district",null,"410000","410500",null,"安阳县","410522","district",null,"410000","410500",null,"汤阴县","410523","district",null,"410000","410500",null,"滑县","410526","district",null,"410000","410500",null,"内黄县","410527","district",null,"410000","410500",null,"林州市","410581","district",null,"410000","410500",null,"鹤山区","410602","district",null,"410000","410600",null,"山城区","410603","district",null,"410000","410600",null,"淇滨区","410611","district",null,"410000","410600",null,"浚县","410621","district",null,"410000","410600",null,"淇县","410622","district",null,"410000","410600",null,"魏都区","411002","district",null,"410000","411000",null,"建安区","411003","district",null,"410000","411000",null,"鄢陵县","411024","district",null,"410000","411000",null,"襄城县","411025","district",null,"410000","411000",null,"禹州市","411081","district",null,"410000","411000",null,"长葛市","411082","district",null,"410000","411000",null,"梁园区","411402","district",null,"410000","411400",null,"睢阳区","411403","district",null,"410000","411400",null,"民权县","411421","district",null,"410000","411400",null,"睢县","411422","district",null,"410000","411400",null,"宁陵县","411423","district",null,"410000","411400",null,"柘城县","411424","district",null,"410000","411400",null,"虞城县","411425","district",null,"410000","411400",null,"夏邑县","411426","district",null,"410000","411400",null,"永城市","411481","district",null,"410000","411400",null,"龙亭区","410202","district",null,"410000","410200",null,"顺河回族区","410203","district",null,"410000","410200",null,"鼓楼区","410204","district",null,"410000","410200",null,"禹王台区","410205","district",null,"410000","410200",null,"祥符区","410212","district",null,"410000","410200",null,"杞县","410221","district",null,"410000","410200",null,"通许县","410222","district",null,"410000","410200",null,"尉氏县","410223","district",null,"410000","410200",null,"兰考县","410225","district",null,"410000","410200",null,"龙湖区","440507","district",null,"440000","440500",null,"金平区","440511","district",null,"440000","440500",null,"濠江区","440512","district",null,"440000","440500",null,"潮阳区","440513","district",null,"440000","440500",null,"潮南区","440514","district",null,"440000","440500",null,"澄海区","440515","district",null,"440000","440500",null,"南澳县","440523","district",null,"440000","440500",null,"禅城区","440604","district",null,"440000","440600",null,"南海区","440605","district",null,"440000","440600",null,"顺德区","440606","district",null,"440000","440600",null,"三水区","440607","district",null,"440000","440600",null,"高明区","440608","district",null,"440000","440600",null,"端州区","441202","district",null,"440000","441200",null,"鼎湖区","441203","district",null,"440000","441200",null,"高要区","441204","district",null,"440000","441200",null,"广宁县","441223","district",null,"440000","441200",null,"怀集县","441224","district",null,"440000","441200",null,"封开县","441225","district",null,"440000","441200",null,"德庆县","441226","district",null,"440000","441200",null,"四会市","441284","district",null,"440000","441200",null,"惠城区","441302","district",null,"440000","441300",null,"惠阳区","441303","district",null,"440000","441300",null,"博罗县","441322","district",null,"440000","441300",null,"惠东县","441323","district",null,"440000","441300",null,"龙门县","441324","district",null,"440000","441300",null,"罗湖区","440303","district",null,"440000","440300",null,"福田区","440304","district",null,"440000","440300",null,"南山区","440305","district",null,"440000","440300",null]
08:56:44.524 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1215 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["高埗镇","441900","street",null,"440000","441900",null,"桥头镇","441900","street",null,"440000","441900",null,"清溪镇","441900","street",null,"440000","441900",null,"望牛墩镇","441900","street",null,"440000","441900",null,"道滘镇","441900","street",null,"440000","441900",null,"莞城街道","441900","street",null,"440000","441900",null,"企石镇","441900","street",null,"440000","441900",null,"东坑镇","441900","street",null,"440000","441900",null,"东城街道","441900","street",null,"440000","441900",null,"万江街道","441900","street",null,"440000","441900",null,"南城街道","441900","street",null,"440000","441900",null,"谢岗镇","441900","street",null,"440000","441900",null,"长安镇","441900","street",null,"440000","441900",null,"寮步镇","441900","street",null,"440000","441900",null,"石排镇","441900","street",null,"440000","441900",null,"茶山镇","441900","street",null,"440000","441900",null,"沙田镇","441900","street",null,"440000","441900",null,"常平镇","441900","street",null,"440000","441900",null,"中堂镇","441900","street",null,"440000","441900",null,"厚街镇","441900","street",null,"440000","441900",null,"凤岗镇","441900","street",null,"440000","441900",null,"横沥镇","441900","street",null,"440000","441900",null,"樟木头镇","441900","street",null,"440000","441900",null,"石龙镇","441900","street",null,"440000","441900",null,"虎门镇","441900","street",null,"440000","441900",null,"大岭山镇","441900","street",null,"440000","441900",null,"塘厦镇","441900","street",null,"440000","441900",null,"大朗镇","441900","street",null,"440000","441900",null,"石碣镇","441900","street",null,"440000","441900",null,"武江区","440203","district",null,"440000","440200",null,"浈江区","440204","district",null,"440000","440200",null,"曲江区","440205","district",null,"440000","440200",null,"始兴县","440222","district",null,"440000","440200",null,"仁化县","440224","district",null,"440000","440200",null,"翁源县","440229","district",null,"440000","440200",null,"乳源瑶族自治县","440232","district",null,"440000","440200",null,"新丰县","440233","district",null,"440000","440200",null,"乐昌市","440281","district",null,"440000","440200",null,"南雄市","440282","district",null,"440000","440200",null,"源城区","441602","district",null,"440000","441600",null,"紫金县","441621","district",null,"440000","441600",null,"龙川县","441622","district",null,"440000","441600",null,"连平县","441623","district",null,"440000","441600",null,"和平县","441624","district",null,"440000","441600",null,"东源县","441625","district",null,"440000","441600",null,"云城区","445302","district",null,"440000","445300",null,"云安区","445303","district",null,"440000","445300",null,"新兴县","445321","district",null,"440000","445300",null,"郁南县","445322","district",null,"440000","445300",null,"罗定市","445381","district",null,"440000","445300",null,"海勃湾区","150302","district",null,"150000","150300",null,"海南区","150303","district",null,"150000","150300",null,"乌达区","150304","district",null,"150000","150300",null,"临河区","150802","district",null,"150000","150800",null,"五原县","150821","district",null,"150000","150800",null,"磴口县","150822","district",null,"150000","150800",null,"乌拉特前旗","150823","district",null,"150000","150800",null,"乌拉特中旗","150824","district",null,"150000","150800",null,"乌拉特后旗","150825","district",null,"150000","150800",null,"杭锦后旗","150826","district",null,"150000","150800",null,"东河区","150202","district",null,"150000","150200",null,"昆都仑区","150203","district",null,"150000","150200",null,"青山区","150204","district",null,"150000","150200",null,"石拐区","150205","district",null,"150000","150200",null,"白云鄂博矿区","150206","district",null,"150000","150200",null,"九原区","150207","district",null,"150000","150200",null,"土默特右旗","150221","district",null,"150000","150200",null,"固阳县","150222","district",null,"150000","150200",null,"达尔罕茂明安联合旗","150223","district",null,"150000","150200",null,"海拉尔区","150702","district",null,"150000","150700",null,"扎赉诺尔区","150703","district",null,"150000","150700",null,"阿荣旗","150721","district",null,"150000","150700",null,"莫力达瓦达斡尔族自治旗","150722","district",null,"150000","150700",null,"鄂伦春自治旗","150723","district",null,"150000","150700",null,"鄂温克族自治旗","150724","district",null,"150000","150700",null,"陈巴尔虎旗","150725","district",null,"150000","150700",null,"新巴尔虎左旗","150726","district",null,"150000","150700",null,"新巴尔虎右旗","150727","district",null,"150000","150700",null,"满洲里市","150781","district",null,"150000","150700",null,"牙克石市","150782","district",null,"150000","150700",null,"扎兰屯市","150783","district",null,"150000","150700",null,"额尔古纳市","150784","district",null,"150000","150700",null,"根河市","150785","district",null,"150000","150700",null,"乌兰浩特市","152201","district",null,"150000","152200",null,"阿尔山市","152202","district",null,"150000","152200",null,"科尔沁右翼前旗","152221","district",null,"150000","152200",null,"科尔沁右翼中旗","152222","district",null,"150000","152200",null,"扎赉特旗","152223","district",null,"150000","152200",null,"突泉县","152224","district",null,"150000","152200",null,"科尔沁区","150502","district",null,"150000","150500",null,"科尔沁左翼中旗","150521","district",null,"150000","150500",null,"科尔沁左翼后旗","150522","district",null,"150000","150500",null,"开鲁县","150523","district",null,"150000","150500",null,"库伦旗","150524","district",null,"150000","150500",null,"奈曼旗","150525","district",null,"150000","150500",null,"扎鲁特旗","150526","district",null,"150000","150500",null,"霍林郭勒市","150581","district",null,"150000","150500",null,"红山区","150402","district",null,"150000","150400",null,"元宝山区","150403","district",null,"150000","150400",null,"松山区","150404","district",null,"150000","150400",null]
08:56:46.738 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1082 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["苇湖镇","659008","street",null,"650000","659008",null,"榆树庄镇","659008","street",null,"650000","659008",null,"金梁镇","659008","street",null,"650000","659008",null,"塔城市","654201","district",null,"650000","654200",null,"乌苏市","654202","district",null,"650000","654200",null,"沙湾市","654203","district",null,"650000","654200",null,"额敏县","654221","district",null,"650000","654200",null,"托里县","654224","district",null,"650000","654200",null,"裕民县","654225","district",null,"650000","654200",null,"和布克赛尔蒙古自治县","654226","district",null,"650000","654200",null,"老兵镇","659009","street",null,"650000","659009",null,"昆牧镇","659009","street",null,"650000","659009",null,"昆泉镇","659009","street",null,"650000","659009",null,"兵团二二四团","659009","street",null,"650000","659009",null,"和田市","653201","district",null,"650000","653200",null,"和田县","653221","district",null,"650000","653200",null,"墨玉县","653222","district",null,"650000","653200",null,"皮山县","653223","district",null,"650000","653200",null,"洛浦县","653224","district",null,"650000","653200",null,"策勒县","653225","district",null,"650000","653200",null,"于田县","653226","district",null,"650000","653200",null,"民丰县","653227","district",null,"650000","653200",null,"阿勒泰市","654301","district",null,"650000","654300",null,"布尔津县","654321","district",null,"650000","654300",null,"富蕴县","654322","district",null,"650000","654300",null,"福海县","654323","district",null,"650000","654300",null,"哈巴河县","654324","district",null,"650000","654300",null,"青河县","654325","district",null,"650000","654300",null,"吉木乃县","654326","district",null,"650000","654300",null,"兵团一五二团","659001","street",null,"650000","659001",null,"向阳街道","659001","street",null,"650000","659001",null,"红山街道","659001","street",null,"650000","659001",null,"兵团一四四团","659001","street",null,"650000","659001",null,"东城街道","659001","street",null,"650000","659001",null,"老街街道","659001","street",null,"650000","659001",null,"石河子镇","659001","street",null,"650000","659001",null,"新城街道","659001","street",null,"650000","659001",null,"北泉镇","659001","street",null,"650000","659001",null,"昌吉市","652301","district",null,"650000","652300",null,"阜康市","652302","district",null,"650000","652300",null,"呼图壁县","652323","district",null,"650000","652300",null,"玛纳斯县","652324","district",null,"650000","652300",null,"奇台县","652325","district",null,"650000","652300",null,"吉木萨尔县","652327","district",null,"650000","652300",null,"木垒哈萨克自治县","652328","district",null,"650000","652300",null,"库尔勒市","652801","district",null,"650000","652800",null,"轮台县","652822","district",null,"650000","652800",null,"尉犁县","652823","district",null,"650000","652800",null,"若羌县","652824","district",null,"650000","652800",null,"且末县","652825","district",null,"650000","652800",null,"焉耆回族自治县","652826","district",null,"650000","652800",null,"和静县","652827","district",null,"650000","652800",null,"和硕县","652828","district",null,"650000","652800",null,"博湖县","652829","district",null,"650000","652800",null,"伊宁市","654002","district",null,"650000","654000",null,"奎屯市","654003","district",null,"650000","654000",null,"霍尔果斯市","654004","district",null,"650000","654000",null,"伊宁县","654021","district",null,"650000","654000",null,"察布查尔锡伯自治县","654022","district",null,"650000","654000",null,"霍城县","654023","district",null,"650000","654000",null,"巩留县","654024","district",null,"650000","654000",null,"新源县","654025","district",null,"650000","654000",null,"昭苏县","654026","district",null,"650000","654000",null,"特克斯县","654027","district",null,"650000","654000",null,"尼勒克县","654028","district",null,"650000","654000",null,"玛滩镇","659002","street",null,"650000","659002",null,"永宁镇","659002","street",null,"650000","659002",null,"花桥镇","659002","street",null,"650000","659002",null,"沙河镇","659002","street",null,"650000","659002",null,"托喀依乡","659002","street",null,"650000","659002",null,"青松路街道","659002","street",null,"650000","659002",null,"塔门镇","659002","street",null,"650000","659002",null,"南口街道","659002","street",null,"650000","659002",null,"金杨镇","659002","street",null,"650000","659002",null,"新井子镇","659002","street",null,"650000","659002",null,"金银川镇","659002","street",null,"650000","659002",null,"甘泉镇","659002","street",null,"650000","659002",null,"兵团农一师沙井子水利管理处","659002","street",null,"650000","659002",null,"幸福路街道","659002","street",null,"650000","659002",null,"金银川路街道","659002","street",null,"650000","659002",null,"幸福镇","659002","street",null,"650000","659002",null,"塔南镇","659002","street",null,"650000","659002",null,"新开岭镇","659002","street",null,"650000","659002",null,"昌安镇","659002","street",null,"650000","659002",null,"双城镇","659002","street",null,"650000","659002",null,"喀什市","653101","district",null,"650000","653100",null,"疏附县","653121","district",null,"650000","653100",null,"疏勒县","653122","district",null,"650000","653100",null,"英吉沙县","653123","district",null,"650000","653100",null,"泽普县","653124","district",null,"650000","653100",null,"莎车县","653125","district",null,"650000","653100",null,"叶城县","653126","district",null,"650000","653100",null,"麦盖提县","653127","district",null,"650000","653100",null,"岳普湖县","653128","district",null,"650000","653100",null,"伽师县","653129","district",null,"650000","653100",null,"巴楚县","653130","district",null,"650000","653100",null,"塔什库尔干塔吉克自治县","653131","district",null,"650000","653100",null,"阿图什市","653001","district",null,"650000","653000",null,"阿克陶县","653022","district",null,"650000","653000",null,"阿合奇县","653023","district",null,"650000","653000",null]
08:56:50.945 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3224 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["平阴县","370124","district",null,"370000","370100",null,"商河县","370126","district",null,"370000","370100",null,"潍城区","370702","district",null,"370000","370700",null,"寒亭区","370703","district",null,"370000","370700",null,"坊子区","370704","district",null,"370000","370700",null,"奎文区","370705","district",null,"370000","370700",null,"临朐县","370724","district",null,"370000","370700",null,"昌乐县","370725","district",null,"370000","370700",null,"青州市","370781","district",null,"370000","370700",null,"诸城市","370782","district",null,"370000","370700",null,"寿光市","370783","district",null,"370000","370700",null,"安丘市","370784","district",null,"370000","370700",null,"高密市","370785","district",null,"370000","370700",null,"昌邑市","370786","district",null,"370000","370700",null,"市中区","370402","district",null,"370000","370400",null,"薛城区","370403","district",null,"370000","370400",null,"峄城区","370404","district",null,"370000","370400",null,"台儿庄区","370405","district",null,"370000","370400",null,"山亭区","370406","district",null,"370000","370400",null,"滕州市","370481","district",null,"370000","370400",null,"泰山区","370902","district",null,"370000","370900",null,"岱岳区","370911","district",null,"370000","370900",null,"宁阳县","370921","district",null,"370000","370900",null,"东平县","370923","district",null,"370000","370900",null,"新泰市","370982","district",null,"370000","370900",null,"肥城市","370983","district",null,"370000","370900",null,"任城区","370811","district",null,"370000","370800",null,"兖州区","370812","district",null,"370000","370800",null,"微山县","370826","district",null,"370000","370800",null,"鱼台县","370827","district",null,"370000","370800",null,"金乡县","370828","district",null,"370000","370800",null,"嘉祥县","370829","district",null,"370000","370800",null,"汶上县","370830","district",null,"370000","370800",null,"泗水县","370831","district",null,"370000","370800",null,"梁山县","370832","district",null,"370000","370800",null,"曲阜市","370881","district",null,"370000","370800",null,"邹城市","370883","district",null,"370000","370800",null,"兰山区","371302","district",null,"370000","371300",null,"罗庄区","371311","district",null,"370000","371300",null,"河东区","371312","district",null,"370000","371300",null,"沂南县","371321","district",null,"370000","371300",null,"郯城县","371322","district",null,"370000","371300",null,"沂水县","371323","district",null,"370000","371300",null,"兰陵县","371324","district",null,"370000","371300",null,"费县","371325","district",null,"370000","371300",null,"平邑县","371326","district",null,"370000","371300",null,"莒南县","371327","district",null,"370000","371300",null,"蒙阴县","371328","district",null,"370000","371300",null,"临沭县","371329","district",null,"370000","371300",null,"牡丹区","371702","district",null,"370000","371700",null,"定陶区","371703","district",null,"370000","371700",null,"曹县","371721","district",null,"370000","371700",null,"单县","371722","district",null,"370000","371700",null,"成武县","371723","district",null,"370000","371700",null,"巨野县","371724","district",null,"370000","371700",null,"郓城县","371725","district",null,"370000","371700",null,"鄄城县","371726","district",null,"370000","371700",null,"东明县","371728","district",null,"370000","371700",null,"德城区","371402","district",null,"370000","371400",null,"陵城区","371403","district",null,"370000","371400",null,"宁津县","371422","district",null,"370000","371400",null,"庆云县","371423","district",null,"370000","371400",null,"临邑县","371424","district",null,"370000","371400",null,"齐河县","371425","district",null,"370000","371400",null,"平原县","371426","district",null,"370000","371400",null,"夏津县","371427","district",null,"370000","371400",null,"武城县","371428","district",null,"370000","371400",null,"乐陵市","371481","district",null,"370000","371400",null,"禹城市","371482","district",null,"370000","371400",null,"黄浦区","310101","district",null,"310000","310100",null,"徐汇区","310104","district",null,"310000","310100",null,"长宁区","310105","district",null,"310000","310100",null,"静安区","310106","district",null,"310000","310100",null,"普陀区","310107","district",null,"310000","310100",null,"虹口区","310109","district",null,"310000","310100",null,"杨浦区","310110","district",null,"310000","310100",null,"闵行区","310112","district",null,"310000","310100",null,"宝山区","310113","district",null,"310000","310100",null,"嘉定区","310114","district",null,"310000","310100",null,"浦东新区","310115","district",null,"310000","310100",null,"金山区","310116","district",null,"310000","310100",null,"松江区","310117","district",null,"310000","310100",null,"青浦区","310118","district",null,"310000","310100",null,"奉贤区","310120","district",null,"310000","310100",null,"崇明区","310151","district",null,"310000","310100",null,"红花岗区","520302","district",null,"520000","520300",null,"汇川区","520303","district",null,"520000","520300",null,"播州区","520304","district",null,"520000","520300",null,"桐梓县","520322","district",null,"520000","520300",null,"绥阳县","520323","district",null,"520000","520300",null,"正安县","520324","district",null,"520000","520300",null,"道真仡佬族苗族自治县","520325","district",null,"520000","520300",null,"务川仡佬族苗族自治县","520326","district",null,"520000","520300",null,"凤冈县","520327","district",null,"520000","520300",null,"湄潭县","520328","district",null,"520000","520300",null,"余庆县","520329","district",null,"520000","520300",null,"习水县","520330","district",null,"520000","520300",null,"赤水市","520381","district",null,"520000","520300",null,"仁怀市","520382","district",null,"520000","520300",null,"碧江区","520602","district",null,"520000","520600",null]
08:56:52.404 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1450 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["万山区","520603","district",null,"520000","520600",null,"江口县","520621","district",null,"520000","520600",null,"玉屏侗族自治县","520622","district",null,"520000","520600",null,"石阡县","520623","district",null,"520000","520600",null,"思南县","520624","district",null,"520000","520600",null,"印江土家族苗族自治县","520625","district",null,"520000","520600",null,"德江县","520626","district",null,"520000","520600",null,"沿河土家族自治县","520627","district",null,"520000","520600",null,"松桃苗族自治县","520628","district",null,"520000","520600",null,"钟山区","520201","district",null,"520000","520200",null,"六枝特区","520203","district",null,"520000","520200",null,"水城区","520204","district",null,"520000","520200",null,"盘州市","520281","district",null,"520000","520200",null,"都匀市","522701","district",null,"520000","522700",null,"福泉市","522702","district",null,"520000","522700",null,"荔波县","522722","district",null,"520000","522700",null,"贵定县","522723","district",null,"520000","522700",null,"瓮安县","522725","district",null,"520000","522700",null,"独山县","522726","district",null,"520000","522700",null,"平塘县","522727","district",null,"520000","522700",null,"罗甸县","522728","district",null,"520000","522700",null,"长顺县","522729","district",null,"520000","522700",null,"龙里县","522730","district",null,"520000","522700",null,"惠水县","522731","district",null,"520000","522700",null,"三都水族自治县","522732","district",null,"520000","522700",null,"西秀区","520402","district",null,"520000","520400",null,"平坝区","520403","district",null,"520000","520400",null,"普定县","520422","district",null,"520000","520400",null,"镇宁布依族苗族自治县","520423","district",null,"520000","520400",null,"关岭布依族苗族自治县","520424","district",null,"520000","520400",null,"紫云苗族布依族自治县","520425","district",null,"520000","520400",null,"兴义市","522301","district",null,"520000","522300",null,"兴仁市","522302","district",null,"520000","522300",null,"普安县","522323","district",null,"520000","522300",null,"晴隆县","522324","district",null,"520000","522300",null,"贞丰县","522325","district",null,"520000","522300",null,"望谟县","522326","district",null,"520000","522300",null,"册亨县","522327","district",null,"520000","522300",null,"安龙县","522328","district",null,"520000","522300",null,"凯里市","522601","district",null,"520000","522600",null,"黄平县","522622","district",null,"520000","522600",null,"施秉县","522623","district",null,"520000","522600",null,"三穗县","522624","district",null,"520000","522600",null,"镇远县","522625","district",null,"520000","522600",null,"岑巩县","522626","district",null,"520000","522600",null,"天柱县","522627","district",null,"520000","522600",null,"锦屏县","522628","district",null,"520000","522600",null,"剑河县","522629","district",null,"520000","522600",null,"台江县","522630","district",null,"520000","522600",null,"黎平县","522631","district",null,"520000","522600",null,"榕江县","522632","district",null,"520000","522600",null,"从江县","522633","district",null,"520000","522600",null,"雷山县","522634","district",null,"520000","522600",null,"麻江县","522635","district",null,"520000","522600",null,"丹寨县","522636","district",null,"520000","522600",null,"南明区","520102","district",null,"520000","520100",null,"云岩区","520103","district",null,"520000","520100",null,"花溪区","520111","district",null,"520000","520100",null,"乌当区","520112","district",null,"520000","520100",null,"白云区","520113","district",null,"520000","520100",null,"观山湖区","520115","district",null,"520000","520100",null,"开阳县","520121","district",null,"520000","520100",null,"息烽县","520122","district",null,"520000","520100",null,"修文县","520123","district",null,"520000","520100",null,"清镇市","520181","district",null,"520000","520100",null,"七星关区","520502","district",null,"520000","520500",null,"大方县","520521","district",null,"520000","520500",null,"金沙县","520523","district",null,"520000","520500",null,"织金县","520524","district",null,"520000","520500",null,"纳雍县","520525","district",null,"520000","520500",null,"威宁彝族回族苗族自治县","520526","district",null,"520000","520500",null,"赫章县","520527","district",null,"520000","520500",null,"黔西市","520581","district",null,"520000","520500",null,"城口县","500229","district",null,"500000","500200",null,"丰都县","500230","district",null,"500000","500200",null,"垫江县","500231","district",null,"500000","500200",null,"忠县","500233","district",null,"500000","500200",null,"云阳县","500235","district",null,"500000","500200",null,"奉节县","500236","district",null,"500000","500200",null,"巫山县","500237","district",null,"500000","500200",null,"巫溪县","500238","district",null,"500000","500200",null,"石柱土家族自治县","500240","district",null,"500000","500200",null,"秀山土家族苗族自治县","500241","district",null,"500000","500200",null,"酉阳土家族苗族自治县","500242","district",null,"500000","500200",null,"彭水苗族土家族自治县","500243","district",null,"500000","500200",null,"万州区","500101","district",null,"500000","500100",null,"涪陵区","500102","district",null,"500000","500100",null,"渝中区","500103","district",null,"500000","500100",null,"大渡口区","500104","district",null,"500000","500100",null,"江北区","500105","district",null,"500000","500100",null,"沙坪坝区","500106","district",null,"500000","500100",null,"九龙坡区","500107","district",null,"500000","500100",null,"南岸区","500108","district",null,"500000","500100",null,"北碚区","500109","district",null,"500000","500100",null,"綦江区","500110","district",null,"500000","500100",null,"大足区","500111","district",null,"500000","500100",null,"渝北区","500112","district",null,"500000","500100",null,"巴南区","500113","district",null,"500000","500100",null,"黔江区","500114","district",null,"500000","500100",null,"长寿区","500115","district",null,"500000","500100",null]
08:56:55.400 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2987 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["江津区","500116","district",null,"500000","500100",null,"合川区","500117","district",null,"500000","500100",null,"永川区","500118","district",null,"500000","500100",null,"南川区","500119","district",null,"500000","500100",null,"璧山区","500120","district",null,"500000","500100",null,"铜梁区","500151","district",null,"500000","500100",null,"潼南区","500152","district",null,"500000","500100",null,"荣昌区","500153","district",null,"500000","500100",null,"开州区","500154","district",null,"500000","500100",null,"梁平区","500155","district",null,"500000","500100",null,"武隆区","500156","district",null,"500000","500100",null,"铜官区","340705","district",null,"340000","340700",null,"义安区","340706","district",null,"340000","340700",null,"郊区","340711","district",null,"340000","340700",null,"枞阳县","340722","district",null,"340000","340700",null,"贵池区","341702","district",null,"340000","341700",null,"东至县","341721","district",null,"340000","341700",null,"石台县","341722","district",null,"340000","341700",null,"青阳县","341723","district",null,"340000","341700",null,"迎江区","340802","district",null,"340000","340800",null,"大观区","340803","district",null,"340000","340800",null,"宜秀区","340811","district",null,"340000","340800",null,"怀宁县","340822","district",null,"340000","340800",null,"太湖县","340825","district",null,"340000","340800",null,"宿松县","340826","district",null,"340000","340800",null,"望江县","340827","district",null,"340000","340800",null,"岳西县","340828","district",null,"340000","340800",null,"桐城市","340881","district",null,"340000","340800",null,"潜山市","340882","district",null,"340000","340800",null,"杜集区","340602","district",null,"340000","340600",null,"相山区","340603","district",null,"340000","340600",null,"烈山区","340604","district",null,"340000","340600",null,"濉溪县","340621","district",null,"340000","340600",null,"花山区","340503","district",null,"340000","340500",null,"雨山区","340504","district",null,"340000","340500",null,"博望区","340506","district",null,"340000","340500",null,"当涂县","340521","district",null,"340000","340500",null,"含山县","340522","district",null,"340000","340500",null,"和县","340523","district",null,"340000","340500",null,"屯溪区","341002","district",null,"340000","341000",null,"黄山区","341003","district",null,"340000","341000",null,"徽州区","341004","district",null,"340000","341000",null,"歙县","341021","district",null,"340000","341000",null,"休宁县","341022","district",null,"340000","341000",null,"黟县","341023","district",null,"340000","341000",null,"祁门县","341024","district",null,"340000","341000",null,"谯城区","341602","district",null,"340000","341600",null,"涡阳县","341621","district",null,"340000","341600",null,"蒙城县","341622","district",null,"340000","341600",null,"利辛县","341623","district",null,"340000","341600",null,"金安区","341502","district",null,"340000","341500",null,"裕安区","341503","district",null,"340000","341500",null,"叶集区","341504","district",null,"340000","341500",null,"霍邱县","341522","district",null,"340000","341500",null,"舒城县","341523","district",null,"340000","341500",null,"金寨县","341524","district",null,"340000","341500",null,"霍山县","341525","district",null,"340000","341500",null,"颍州区","341202","district",null,"340000","341200",null,"颍东区","341203","district",null,"340000","341200",null,"颍泉区","341204","district",null,"340000","341200",null,"临泉县","341221","district",null,"340000","341200",null,"太和县","341222","district",null,"340000","341200",null,"阜南县","341225","district",null,"340000","341200",null,"颍上县","341226","district",null,"340000","341200",null,"界首市","341282","district",null,"340000","341200",null,"大通区","340402","district",null,"340000","340400",null,"田家庵区","340403","district",null,"340000","340400",null,"谢家集区","340404","district",null,"340000","340400",null,"八公山区","340405","district",null,"340000","340400",null,"潘集区","340406","district",null,"340000","340400",null,"凤台县","340421","district",null,"340000","340400",null,"寿县","340422","district",null,"340000","340400",null,"埇桥区","341302","district",null,"340000","341300",null,"砀山县","341321","district",null,"340000","341300",null,"萧县","341322","district",null,"340000","341300",null,"灵璧县","341323","district",null,"340000","341300",null,"泗县","341324","district",null,"340000","341300",null,"龙子湖区","340302","district",null,"340000","340300",null,"蚌山区","340303","district",null,"340000","340300",null,"禹会区","340304","district",null,"340000","340300",null,"淮上区","340311","district",null,"340000","340300",null,"怀远县","340321","district",null,"340000","340300",null,"五河县","340322","district",null,"340000","340300",null,"固镇县","340323","district",null,"340000","340300",null,"镜湖区","340202","district",null,"340000","340200",null,"鸠江区","340207","district",null,"340000","340200",null,"弋江区","340209","district",null,"340000","340200",null,"湾沚区","340210","district",null,"340000","340200",null,"繁昌区","340212","district",null,"340000","340200",null,"南陵县","340223","district",null,"340000","340200",null,"无为市","340281","district",null,"340000","340200",null,"宣州区","341802","district",null,"340000","341800",null,"郎溪县","341821","district",null,"340000","341800",null,"泾县","341823","district",null,"340000","341800",null,"绩溪县","341824","district",null,"340000","341800",null,"旌德县","341825","district",null,"340000","341800",null,"宁国市","341881","district",null,"340000","341800",null,"广德市","341882","district",null,"340000","341800",null,"琅琊区","341102","district",null,"340000","341100",null,"南谯区","341103","district",null,"340000","341100",null]
08:56:58.781 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1518 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["祁东县","430426","district",null,"430000","430400",null,"耒阳市","430481","district",null,"430000","430400",null,"常宁市","430482","district",null,"430000","430400",null,"武陵区","430702","district",null,"430000","430700",null,"鼎城区","430703","district",null,"430000","430700",null,"安乡县","430721","district",null,"430000","430700",null,"汉寿县","430722","district",null,"430000","430700",null,"澧县","430723","district",null,"430000","430700",null,"临澧县","430724","district",null,"430000","430700",null,"桃源县","430725","district",null,"430000","430700",null,"石门县","430726","district",null,"430000","430700",null,"津市市","430781","district",null,"430000","430700",null,"芙蓉区","430102","district",null,"430000","430100",null,"天心区","430103","district",null,"430000","430100",null,"岳麓区","430104","district",null,"430000","430100",null,"开福区","430105","district",null,"430000","430100",null,"雨花区","430111","district",null,"430000","430100",null,"望城区","430112","district",null,"430000","430100",null,"长沙县","430121","district",null,"430000","430100",null,"浏阳市","430181","district",null,"430000","430100",null,"宁乡市","430182","district",null,"430000","430100",null,"西昌镇","469022","street",null,"460000","469022",null,"南坤镇","469022","street",null,"460000","469022",null,"坡心镇","469022","street",null,"460000","469022",null,"南吕镇","469022","street",null,"460000","469022",null,"乌坡镇","469022","street",null,"460000","469022",null,"枫木镇","469022","street",null,"460000","469022",null,"屯城镇","469022","street",null,"460000","469022",null,"新兴镇","469022","street",null,"460000","469022",null,"南开乡","469025","street",null,"460000","469025",null,"阜龙乡","469025","street",null,"460000","469025",null,"细水乡","469025","street",null,"460000","469025",null,"七坊镇","469025","street",null,"460000","469025",null,"打安镇","469025","street",null,"460000","469025",null,"元门乡","469025","street",null,"460000","469025",null,"金波乡","469025","street",null,"460000","469025",null,"青松乡","469025","street",null,"460000","469025",null,"牙叉镇","469025","street",null,"460000","469025",null,"荣邦乡","469025","street",null,"460000","469025",null,"邦溪镇","469025","street",null,"460000","469025",null,"什运乡","469030","street",null,"460000","469030",null,"吊罗山乡","469030","street",null,"460000","469030",null,"和平镇","469030","street",null,"460000","469030",null,"中平镇","469030","street",null,"460000","469030",null,"黎母山镇","469030","street",null,"460000","469030",null,"上安乡","469030","street",null,"460000","469030",null,"红毛镇","469030","street",null,"460000","469030",null,"营根镇","469030","street",null,"460000","469030",null,"长征镇","469030","street",null,"460000","469030",null,"湾岭镇","469030","street",null,"460000","469030",null,"莺歌海镇","469027","street",null,"460000","469027",null,"佛罗镇","469027","street",null,"460000","469027",null,"黄流镇","469027","street",null,"460000","469027",null,"尖峰镇","469027","street",null,"460000","469027",null,"志仲镇","469027","street",null,"460000","469027",null,"大安镇","469027","street",null,"460000","469027",null,"九所镇","469027","street",null,"460000","469027",null,"利国镇","469027","street",null,"460000","469027",null,"抱由镇","469027","street",null,"460000","469027",null,"千家镇","469027","street",null,"460000","469027",null,"万冲镇","469027","street",null,"460000","469027",null,"大田镇","469007","street",null,"460000","469007",null,"八所镇","469007","street",null,"460000","469007",null,"四更镇","469007","street",null,"460000","469007",null,"三家镇","469007","street",null,"460000","469007",null,"新龙镇","469007","street",null,"460000","469007",null,"感城镇","469007","street",null,"460000","469007",null,"江边乡","469007","street",null,"460000","469007",null,"板桥镇","469007","street",null,"460000","469007",null,"天安乡","469007","street",null,"460000","469007",null,"东河镇","469007","street",null,"460000","469007",null,"椰林镇","469028","street",null,"460000","469028",null,"新村镇","469028","street",null,"460000","469028",null,"黎安镇","469028","street",null,"460000","469028",null,"文罗镇","469028","street",null,"460000","469028",null,"群英乡","469028","street",null,"460000","469028",null,"提蒙乡","469028","street",null,"460000","469028",null,"三才镇","469028","street",null,"460000","469028",null,"隆广镇","469028","street",null,"460000","469028",null,"英州镇","469028","street",null,"460000","469028",null,"本号镇","469028","street",null,"460000","469028",null,"光坡镇","469028","street",null,"460000","469028",null,"西沙区","460301","district",null,"460000","460300",null,"南沙区","460302","district",null,"460000","460300",null,"文教镇","469005","street",null,"460000","469005",null,"铺前镇","469005","street",null,"460000","469005",null,"东郊镇","469005","street",null,"460000","469005",null,"龙楼镇","469005","street",null,"460000","469005",null,"昌洒镇","469005","street",null,"460000","469005",null,"锦山镇","469005","street",null,"460000","469005",null,"东阁镇","469005","street",null,"460000","469005",null,"冯坡镇","469005","street",null,"460000","469005",null,"公坡镇","469005","street",null,"460000","469005",null,"会文镇","469005","street",null,"460000","469005",null,"东路镇","469005","street",null,"460000","469005",null,"翁田镇","469005","street",null,"460000","469005",null,"潭牛镇","469005","street",null,"460000","469005",null,"文城镇","469005","street",null,"460000","469005",null,"抱罗镇","469005","street",null,"460000","469005",null,"蓬莱镇","469005","street",null,"460000","469005",null]
08:56:59.955 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1167 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["重兴镇","469005","street",null,"460000","469005",null,"海棠区","460202","district",null,"460000","460200",null,"吉阳区","460203","district",null,"460000","460200",null,"天涯区","460204","district",null,"460000","460200",null,"崖州区","460205","district",null,"460000","460200",null,"博厚镇","469024","street",null,"460000","469024",null,"南宝镇","469024","street",null,"460000","469024",null,"新盈镇","469024","street",null,"460000","469024",null,"和舍镇","469024","street",null,"460000","469024",null,"调楼镇","469024","street",null,"460000","469024",null,"东英镇","469024","street",null,"460000","469024",null,"临城镇","469024","street",null,"460000","469024",null,"加来镇","469024","street",null,"460000","469024",null,"多文镇","469024","street",null,"460000","469024",null,"皇桐镇","469024","street",null,"460000","469024",null,"波莲镇","469024","street",null,"460000","469024",null,"毛阳镇","469001","street",null,"460000","469001",null,"水满乡","469001","street",null,"460000","469001",null,"番阳镇","469001","street",null,"460000","469001",null,"毛道乡","469001","street",null,"460000","469001",null,"通什镇","469001","street",null,"460000","469001",null,"南圣镇","469001","street",null,"460000","469001",null,"畅好乡","469001","street",null,"460000","469001",null,"南林乡","469029","street",null,"460000","469029",null,"毛感乡","469029","street",null,"460000","469029",null,"加茂镇","469029","street",null,"460000","469029",null,"响水镇","469029","street",null,"460000","469029",null,"六弓乡","469029","street",null,"460000","469029",null,"保城镇","469029","street",null,"460000","469029",null,"什玲镇","469029","street",null,"460000","469029",null,"新政镇","469029","street",null,"460000","469029",null,"三道镇","469029","street",null,"460000","469029",null,"乌烈镇","469026","street",null,"460000","469026",null,"石碌镇","469026","street",null,"460000","469026",null,"叉河镇","469026","street",null,"460000","469026",null,"王下乡","469026","street",null,"460000","469026",null,"昌化镇","469026","street",null,"460000","469026",null,"十月田镇","469026","street",null,"460000","469026",null,"七叉镇","469026","street",null,"460000","469026",null,"海尾镇","469026","street",null,"460000","469026",null,"老城镇","469023","street",null,"460000","469023",null,"大丰镇","469023","street",null,"460000","469023",null,"加乐镇","469023","street",null,"460000","469023",null,"中兴镇","469023","street",null,"460000","469023",null,"瑞溪镇","469023","street",null,"460000","469023",null,"仁兴镇","469023","street",null,"460000","469023",null,"金江镇","469023","street",null,"460000","469023",null,"福山镇","469023","street",null,"460000","469023",null,"桥头镇","469023","street",null,"460000","469023",null,"文儒镇","469023","street",null,"460000","469023",null,"永发镇","469023","street",null,"460000","469023",null,"岭口镇","469021","street",null,"460000","469021",null,"龙门镇","469021","street",null,"460000","469021",null,"定城镇","469021","street",null,"460000","469021",null,"雷鸣镇","469021","street",null,"460000","469021",null,"黄竹镇","469021","street",null,"460000","469021",null,"龙河镇","469021","street",null,"460000","469021",null,"富文镇","469021","street",null,"460000","469021",null,"翰林镇","469021","street",null,"460000","469021",null,"新竹镇","469021","street",null,"460000","469021",null,"龙湖镇","469021","street",null,"460000","469021",null,"秀英区","460105","district",null,"460000","460100",null,"龙华区","460106","district",null,"460000","460100",null,"琼山区","460107","district",null,"460000","460100",null,"美兰区","460108","district",null,"460000","460100",null,"阳江镇","469002","street",null,"460000","469002",null,"龙江镇","469002","street",null,"460000","469002",null,"长坡镇","469002","street",null,"460000","469002",null,"石壁镇","469002","street",null,"460000","469002",null,"会山镇","469002","street",null,"460000","469002",null,"博鳌镇","469002","street",null,"460000","469002",null,"潭门镇","469002","street",null,"460000","469002",null,"大路镇","469002","street",null,"460000","469002",null,"嘉积镇","469002","street",null,"460000","469002",null,"万泉镇","469002","street",null,"460000","469002",null,"中原镇","469002","street",null,"460000","469002",null,"塔洋镇","469002","street",null,"460000","469002",null,"海头镇","460400","street",null,"460000","460400",null,"新州镇","460400","street",null,"460000","460400",null,"中和镇","460400","street",null,"460000","460400",null,"峨蔓镇","460400","street",null,"460000","460400",null,"三都镇","460400","street",null,"460000","460400",null,"木棠镇","460400","street",null,"460000","460400",null,"王五镇","460400","street",null,"460000","460400",null,"雅星镇","460400","street",null,"460000","460400",null,"大成镇","460400","street",null,"460000","460400",null,"东成镇","460400","street",null,"460000","460400",null,"和庆镇","460400","street",null,"460000","460400",null,"兰洋镇","460400","street",null,"460000","460400",null,"南丰镇","460400","street",null,"460000","460400",null,"光村镇","460400","street",null,"460000","460400",null,"那大镇","460400","street",null,"460000","460400",null,"排浦镇","460400","street",null,"460000","460400",null,"白马井镇","460400","street",null,"460000","460400",null,"后安镇","469006","street",null,"460000","469006",null,"和乐镇","469006","street",null,"460000","469006",null,"东澳镇","469006","street",null,"460000","469006",null,"龙滚镇","469006","street",null,"460000","469006",null,"山根镇","469006","street",null,"460000","469006",null,"北大镇","469006","street",null,"460000","469006",null]
08:57:01.603 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1640 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["大茂镇","469006","street",null,"460000","469006",null,"礼纪镇","469006","street",null,"460000","469006",null,"万城镇","469006","street",null,"460000","469006",null,"三更罗镇","469006","street",null,"460000","469006",null,"长丰镇","469006","street",null,"460000","469006",null,"南桥镇","469006","street",null,"460000","469006",null,"连云区","320703","district",null,"320000","320700",null,"海州区","320706","district",null,"320000","320700",null,"赣榆区","320707","district",null,"320000","320700",null,"东海县","320722","district",null,"320000","320700",null,"灌云县","320723","district",null,"320000","320700",null,"灌南县","320724","district",null,"320000","320700",null,"通州区","320612","district",null,"320000","320600",null,"崇川区","320613","district",null,"320000","320600",null,"海门区","320614","district",null,"320000","320600",null,"如东县","320623","district",null,"320000","320600",null,"启东市","320681","district",null,"320000","320600",null,"如皋市","320682","district",null,"320000","320600",null,"海安市","320685","district",null,"320000","320600",null,"玄武区","320102","district",null,"320000","320100",null,"秦淮区","320104","district",null,"320000","320100",null,"建邺区","320105","district",null,"320000","320100",null,"鼓楼区","320106","district",null,"320000","320100",null,"浦口区","320111","district",null,"320000","320100",null,"栖霞区","320113","district",null,"320000","320100",null,"雨花台区","320114","district",null,"320000","320100",null,"江宁区","320115","district",null,"320000","320100",null,"六合区","320116","district",null,"320000","320100",null,"溧水区","320117","district",null,"320000","320100",null,"高淳区","320118","district",null,"320000","320100",null,"虎丘区","320505","district",null,"320000","320500",null,"吴中区","320506","district",null,"320000","320500",null,"相城区","320507","district",null,"320000","320500",null,"姑苏区","320508","district",null,"320000","320500",null,"吴江区","320509","district",null,"320000","320500",null,"常熟市","320581","district",null,"320000","320500",null,"张家港市","320582","district",null,"320000","320500",null,"昆山市","320583","district",null,"320000","320500",null,"太仓市","320585","district",null,"320000","320500",null,"海陵区","321202","district",null,"320000","321200",null,"高港区","321203","district",null,"320000","321200",null,"姜堰区","321204","district",null,"320000","321200",null,"兴化市","321281","district",null,"320000","321200",null,"靖江市","321282","district",null,"320000","321200",null,"泰兴市","321283","district",null,"320000","321200",null,"宿城区","321302","district",null,"320000","321300",null,"宿豫区","321311","district",null,"320000","321300",null,"沭阳县","321322","district",null,"320000","321300",null,"泗阳县","321323","district",null,"320000","321300",null,"泗洪县","321324","district",null,"320000","321300",null,"广陵区","321002","district",null,"320000","321000",null,"邗江区","321003","district",null,"320000","321000",null,"江都区","321012","district",null,"320000","321000",null,"宝应县","321023","district",null,"320000","321000",null,"仪征市","321081","district",null,"320000","321000",null,"高邮市","321084","district",null,"320000","321000",null,"京口区","321102","district",null,"320000","321100",null,"润州区","321111","district",null,"320000","321100",null,"丹徒区","321112","district",null,"320000","321100",null,"丹阳市","321181","district",null,"320000","321100",null,"扬中市","321182","district",null,"320000","321100",null,"句容市","321183","district",null,"320000","321100",null,"亭湖区","320902","district",null,"320000","320900",null,"盐都区","320903","district",null,"320000","320900",null,"大丰区","320904","district",null,"320000","320900",null,"响水县","320921","district",null,"320000","320900",null,"滨海县","320922","district",null,"320000","320900",null,"阜宁县","320923","district",null,"320000","320900",null,"射阳县","320924","district",null,"320000","320900",null,"建湖县","320925","district",null,"320000","320900",null,"东台市","320981","district",null,"320000","320900",null,"淮安区","320803","district",null,"320000","320800",null,"淮阴区","320804","district",null,"320000","320800",null,"清江浦区","320812","district",null,"320000","320800",null,"洪泽区","320813","district",null,"320000","320800",null,"涟水县","320826","district",null,"320000","320800",null,"盱眙县","320830","district",null,"320000","320800",null,"金湖县","320831","district",null,"320000","320800",null,"鼓楼区","320302","district",null,"320000","320300",null,"云龙区","320303","district",null,"320000","320300",null,"贾汪区","320305","district",null,"320000","320300",null,"泉山区","320311","district",null,"320000","320300",null,"铜山区","320312","district",null,"320000","320300",null,"丰县","320321","district",null,"320000","320300",null,"沛县","320322","district",null,"320000","320300",null,"睢宁县","320324","district",null,"320000","320300",null,"新沂市","320381","district",null,"320000","320300",null,"邳州市","320382","district",null,"320000","320300",null,"天宁区","320402","district",null,"320000","320400",null,"钟楼区","320404","district",null,"320000","320400",null,"新北区","320411","district",null,"320000","320400",null,"武进区","320412","district",null,"320000","320400",null,"金坛区","320413","district",null,"320000","320400",null,"溧阳市","320481","district",null,"320000","320400",null,"锡山区","320205","district",null,"320000","320200",null,"惠山区","320206","district",null,"320000","320200",null,"滨湖区","320211","district",null,"320000","320200",null,"梁溪区","320213","district",null,"320000","320200",null,"新吴区","320214","district",null,"320000","320200",null,"江阴市","320281","district",null,"320000","320200",null]
08:57:04.270 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2661 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["宜兴市","320282","district",null,"320000","320200",null,"乐都区","630202","district",null,"630000","630200",null,"平安区","630203","district",null,"630000","630200",null,"民和回族土族自治县","630222","district",null,"630000","630200",null,"互助土族自治县","630223","district",null,"630000","630200",null,"化隆回族自治县","630224","district",null,"630000","630200",null,"循化撒拉族自治县","630225","district",null,"630000","630200",null,"共和县","632521","district",null,"630000","632500",null,"同德县","632522","district",null,"630000","632500",null,"贵德县","632523","district",null,"630000","632500",null,"兴海县","632524","district",null,"630000","632500",null,"贵南县","632525","district",null,"630000","632500",null,"格尔木市","632801","district",null,"630000","632800",null,"德令哈市","632802","district",null,"630000","632800",null,"茫崖市","632803","district",null,"630000","632800",null,"乌兰县","632821","district",null,"630000","632800",null,"都兰县","632822","district",null,"630000","632800",null,"天峻县","632823","district",null,"630000","632800",null,"海西蒙古族藏族自治州直辖","632825","district",null,"630000","632800",null,"玉树市","632701","district",null,"630000","632700",null,"杂多县","632722","district",null,"630000","632700",null,"称多县","632723","district",null,"630000","632700",null,"治多县","632724","district",null,"630000","632700",null,"囊谦县","632725","district",null,"630000","632700",null,"曲麻莱县","632726","district",null,"630000","632700",null,"同仁市","632301","district",null,"630000","632300",null,"尖扎县","632322","district",null,"630000","632300",null,"泽库县","632323","district",null,"630000","632300",null,"河南蒙古族自治县","632324","district",null,"630000","632300",null,"玛沁县","632621","district",null,"630000","632600",null,"班玛县","632622","district",null,"630000","632600",null,"甘德县","632623","district",null,"630000","632600",null,"达日县","632624","district",null,"630000","632600",null,"久治县","632625","district",null,"630000","632600",null,"玛多县","632626","district",null,"630000","632600",null,"城东区","630102","district",null,"630000","630100",null,"城中区","630103","district",null,"630000","630100",null,"城西区","630104","district",null,"630000","630100",null,"城北区","630105","district",null,"630000","630100",null,"湟中区","630106","district",null,"630000","630100",null,"大通回族土族自治县","630121","district",null,"630000","630100",null,"湟源县","630123","district",null,"630000","630100",null,"门源回族自治县","632221","district",null,"630000","632200",null,"祁连县","632222","district",null,"630000","632200",null,"海晏县","632223","district",null,"630000","632200",null,"刚察县","632224","district",null,"630000","632200",null,"右江区","451002","district",null,"450000","451000",null,"田阳区","451003","district",null,"450000","451000",null,"田东县","451022","district",null,"450000","451000",null,"德保县","451024","district",null,"450000","451000",null,"那坡县","451026","district",null,"450000","451000",null,"凌云县","451027","district",null,"450000","451000",null,"乐业县","451028","district",null,"450000","451000",null,"田林县","451029","district",null,"450000","451000",null,"西林县","451030","district",null,"450000","451000",null,"隆林各族自治县","451031","district",null,"450000","451000",null,"靖西市","451081","district",null,"450000","451000",null,"平果市","451082","district",null,"450000","451000",null,"钦南区","450702","district",null,"450000","450700",null,"钦北区","450703","district",null,"450000","450700",null,"灵山县","450721","district",null,"450000","450700",null,"浦北县","450722","district",null,"450000","450700",null,"海城区","450502","district",null,"450000","450500",null,"银海区","450503","district",null,"450000","450500",null,"铁山港区","450512","district",null,"450000","450500",null,"合浦县","450521","district",null,"450000","450500",null,"港口区","450602","district",null,"450000","450600",null,"防城区","450603","district",null,"450000","450600",null,"上思县","450621","district",null,"450000","450600",null,"东兴市","450681","district",null,"450000","450600",null,"金城江区","451202","district",null,"450000","451200",null,"宜州区","451203","district",null,"450000","451200",null,"南丹县","451221","district",null,"450000","451200",null,"天峨县","451222","district",null,"450000","451200",null,"凤山县","451223","district",null,"450000","451200",null,"东兰县","451224","district",null,"450000","451200",null,"罗城仫佬族自治县","451225","district",null,"450000","451200",null,"环江毛南族自治县","451226","district",null,"450000","451200",null,"巴马瑶族自治县","451227","district",null,"450000","451200",null,"都安瑶族自治县","451228","district",null,"450000","451200",null,"大化瑶族自治县","451229","district",null,"450000","451200",null,"城中区","450202","district",null,"450000","450200",null,"鱼峰区","450203","district",null,"450000","450200",null,"柳南区","450204","district",null,"450000","450200",null,"柳北区","450205","district",null,"450000","450200",null,"柳江区","450206","district",null,"450000","450200",null,"柳城县","450222","district",null,"450000","450200",null,"鹿寨县","450223","district",null,"450000","450200",null,"融安县","450224","district",null,"450000","450200",null,"融水苗族自治县","450225","district",null,"450000","450200",null,"三江侗族自治县","450226","district",null,"450000","450200",null,"兴宾区","451302","district",null,"450000","451300",null,"忻城县","451321","district",null,"450000","451300",null,"象州县","451322","district",null,"450000","451300",null,"武宣县","451323","district",null,"450000","451300",null,"金秀瑶族自治县","451324","district",null,"450000","451300",null,"合山市","451381","district",null,"450000","451300",null,"秀峰区","450302","district",null,"450000","450300",null,"叠彩区","450303","district",null,"450000","450300",null,"象山区","450304","district",null,"450000","450300",null]
08:57:05.536 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1256 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["七星区","450305","district",null,"450000","450300",null,"雁山区","450311","district",null,"450000","450300",null,"临桂区","450312","district",null,"450000","450300",null,"阳朔县","450321","district",null,"450000","450300",null,"灵川县","450323","district",null,"450000","450300",null,"全州县","450324","district",null,"450000","450300",null,"兴安县","450325","district",null,"450000","450300",null,"永福县","450326","district",null,"450000","450300",null,"灌阳县","450327","district",null,"450000","450300",null,"龙胜各族自治县","450328","district",null,"450000","450300",null,"资源县","450329","district",null,"450000","450300",null,"平乐县","450330","district",null,"450000","450300",null,"恭城瑶族自治县","450332","district",null,"450000","450300",null,"荔浦市","450381","district",null,"450000","450300",null,"兴宁区","450102","district",null,"450000","450100",null,"青秀区","450103","district",null,"450000","450100",null,"江南区","450105","district",null,"450000","450100",null,"西乡塘区","450107","district",null,"450000","450100",null,"良庆区","450108","district",null,"450000","450100",null,"邕宁区","450109","district",null,"450000","450100",null,"武鸣区","450110","district",null,"450000","450100",null,"隆安县","450123","district",null,"450000","450100",null,"马山县","450124","district",null,"450000","450100",null,"上林县","450125","district",null,"450000","450100",null,"宾阳县","450126","district",null,"450000","450100",null,"横州市","450181","district",null,"450000","450100",null,"江州区","451402","district",null,"450000","451400",null,"扶绥县","451421","district",null,"450000","451400",null,"宁明县","451422","district",null,"450000","451400",null,"龙州县","451423","district",null,"450000","451400",null,"大新县","451424","district",null,"450000","451400",null,"天等县","451425","district",null,"450000","451400",null,"凭祥市","451481","district",null,"450000","451400",null,"万秀区","450403","district",null,"450000","450400",null,"长洲区","450405","district",null,"450000","450400",null,"龙圩区","450406","district",null,"450000","450400",null,"苍梧县","450421","district",null,"450000","450400",null,"藤县","450422","district",null,"450000","450400",null,"蒙山县","450423","district",null,"450000","450400",null,"岑溪市","450481","district",null,"450000","450400",null,"港北区","450802","district",null,"450000","450800",null,"港南区","450803","district",null,"450000","450800",null,"覃塘区","450804","district",null,"450000","450800",null,"平南县","450821","district",null,"450000","450800",null,"桂平市","450881","district",null,"450000","450800",null,"八步区","451102","district",null,"450000","451100",null,"平桂区","451103","district",null,"450000","451100",null,"昭平县","451121","district",null,"450000","451100",null,"钟山县","451122","district",null,"450000","451100",null,"富川瑶族自治县","451123","district",null,"450000","451100",null,"玉州区","450902","district",null,"450000","450900",null,"福绵区","450903","district",null,"450000","450900",null,"容县","450921","district",null,"450000","450900",null,"陆川县","450922","district",null,"450000","450900",null,"博白县","450923","district",null,"450000","450900",null,"兴业县","450924","district",null,"450000","450900",null,"北流市","450981","district",null,"450000","450900",null,"原州区","640402","district",null,"640000","640400",null,"西吉县","640422","district",null,"640000","640400",null,"隆德县","640423","district",null,"640000","640400",null,"泾源县","640424","district",null,"640000","640400",null,"彭阳县","640425","district",null,"640000","640400",null,"大武口区","640202","district",null,"640000","640200",null,"惠农区","640205","district",null,"640000","640200",null,"平罗县","640221","district",null,"640000","640200",null,"沙坡头区","640502","district",null,"640000","640500",null,"中宁县","640521","district",null,"640000","640500",null,"海原县","640522","district",null,"640000","640500",null,"利通区","640302","district",null,"640000","640300",null,"红寺堡区","640303","district",null,"640000","640300",null,"盐池县","640323","district",null,"640000","640300",null,"同心县","640324","district",null,"640000","640300",null,"青铜峡市","640381","district",null,"640000","640300",null,"兴庆区","640104","district",null,"640000","640100",null,"西夏区","640105","district",null,"640000","640100",null,"金凤区","640106","district",null,"640000","640100",null,"永宁县","640121","district",null,"640000","640100",null,"贺兰县","640122","district",null,"640000","640100",null,"灵武市","640181","district",null,"640000","640100",null,"定海区","330902","district",null,"330000","330900",null,"普陀区","330903","district",null,"330000","330900",null,"岱山县","330921","district",null,"330000","330900",null,"嵊泗县","330922","district",null,"330000","330900",null,"南湖区","330402","district",null,"330000","330400",null,"秀洲区","330411","district",null,"330000","330400",null,"嘉善县","330421","district",null,"330000","330400",null,"海盐县","330424","district",null,"330000","330400",null,"海宁市","330481","district",null,"330000","330400",null,"平湖市","330482","district",null,"330000","330400",null,"桐乡市","330483","district",null,"330000","330400",null,"海曙区","330203","district",null,"330000","330200",null,"江北区","330205","district",null,"330000","330200",null,"北仑区","330206","district",null,"330000","330200",null,"镇海区","330211","district",null,"330000","330200",null,"鄞州区","330212","district",null,"330000","330200",null,"奉化区","330213","district",null,"330000","330200",null,"象山县","330225","district",null,"330000","330200",null,"宁海县","330226","district",null,"330000","330200",null,"余姚市","330281","district",null,"330000","330200",null,"慈溪市","330282","district",null,"330000","330200",null]
08:57:06.601 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1059 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["椒江区","331002","district",null,"330000","331000",null,"黄岩区","331003","district",null,"330000","331000",null,"路桥区","331004","district",null,"330000","331000",null,"三门县","331022","district",null,"330000","331000",null,"天台县","331023","district",null,"330000","331000",null,"仙居县","331024","district",null,"330000","331000",null,"温岭市","331081","district",null,"330000","331000",null,"临海市","331082","district",null,"330000","331000",null,"玉环市","331083","district",null,"330000","331000",null,"鹿城区","330302","district",null,"330000","330300",null,"龙湾区","330303","district",null,"330000","330300",null,"瓯海区","330304","district",null,"330000","330300",null,"洞头区","330305","district",null,"330000","330300",null,"永嘉县","330324","district",null,"330000","330300",null,"平阳县","330326","district",null,"330000","330300",null,"苍南县","330327","district",null,"330000","330300",null,"文成县","330328","district",null,"330000","330300",null,"泰顺县","330329","district",null,"330000","330300",null,"瑞安市","330381","district",null,"330000","330300",null,"乐清市","330382","district",null,"330000","330300",null,"龙港市","330383","district",null,"330000","330300",null,"莲都区","331102","district",null,"330000","331100",null,"青田县","331121","district",null,"330000","331100",null,"缙云县","331122","district",null,"330000","331100",null,"遂昌县","331123","district",null,"330000","331100",null,"松阳县","331124","district",null,"330000","331100",null,"云和县","331125","district",null,"330000","331100",null,"庆元县","331126","district",null,"330000","331100",null,"景宁畲族自治县","331127","district",null,"330000","331100",null,"龙泉市","331181","district",null,"330000","331100",null,"柯城区","330802","district",null,"330000","330800",null,"衢江区","330803","district",null,"330000","330800",null,"常山县","330822","district",null,"330000","330800",null,"开化县","330824","district",null,"330000","330800",null,"龙游县","330825","district",null,"330000","330800",null,"江山市","330881","district",null,"330000","330800",null,"婺城区","330702","district",null,"330000","330700",null,"金东区","330703","district",null,"330000","330700",null,"武义县","330723","district",null,"330000","330700",null,"浦江县","330726","district",null,"330000","330700",null,"磐安县","330727","district",null,"330000","330700",null,"兰溪市","330781","district",null,"330000","330700",null,"义乌市","330782","district",null,"330000","330700",null,"东阳市","330783","district",null,"330000","330700",null,"永康市","330784","district",null,"330000","330700",null,"吴兴区","330502","district",null,"330000","330500",null,"南浔区","330503","district",null,"330000","330500",null,"德清县","330521","district",null,"330000","330500",null,"长兴县","330522","district",null,"330000","330500",null,"安吉县","330523","district",null,"330000","330500",null,"上城区","330102","district",null,"330000","330100",null,"拱墅区","330105","district",null,"330000","330100",null,"西湖区","330106","district",null,"330000","330100",null,"滨江区","330108","district",null,"330000","330100",null,"萧山区","330109","district",null,"330000","330100",null,"余杭区","330110","district",null,"330000","330100",null,"富阳区","330111","district",null,"330000","330100",null,"临安区","330112","district",null,"330000","330100",null,"临平区","330113","district",null,"330000","330100",null,"钱塘区","330114","district",null,"330000","330100",null,"桐庐县","330122","district",null,"330000","330100",null,"淳安县","330127","district",null,"330000","330100",null,"建德市","330182","district",null,"330000","330100",null,"越城区","330602","district",null,"330000","330600",null,"柯桥区","330603","district",null,"330000","330600",null,"上虞区","330604","district",null,"330000","330600",null,"新昌县","330624","district",null,"330000","330600",null,"诸暨市","330681","district",null,"330000","330600",null,"嵊州市","330683","district",null,"330000","330600",null,"路南区","130202","district",null,"130000","130200",null,"路北区","130203","district",null,"130000","130200",null,"古冶区","130204","district",null,"130000","130200",null,"开平区","130205","district",null,"130000","130200",null,"丰南区","130207","district",null,"130000","130200",null,"丰润区","130208","district",null,"130000","130200",null,"曹妃甸区","130209","district",null,"130000","130200",null,"滦南县","130224","district",null,"130000","130200",null,"乐亭县","130225","district",null,"130000","130200",null,"迁西县","130227","district",null,"130000","130200",null,"玉田县","130229","district",null,"130000","130200",null,"遵化市","130281","district",null,"130000","130200",null,"迁安市","130283","district",null,"130000","130200",null,"滦州市","130284","district",null,"130000","130200",null,"海港区","130302","district",null,"130000","130300",null,"山海关区","130303","district",null,"130000","130300",null,"北戴河区","130304","district",null,"130000","130300",null,"抚宁区","130306","district",null,"130000","130300",null,"青龙满族自治县","130321","district",null,"130000","130300",null,"昌黎县","130322","district",null,"130000","130300",null,"卢龙县","130324","district",null,"130000","130300",null,"双桥区","130802","district",null,"130000","130800",null,"双滦区","130803","district",null,"130000","130800",null,"鹰手营子矿区","130804","district",null,"130000","130800",null,"承德县","130821","district",null,"130000","130800",null,"兴隆县","130822","district",null,"130000","130800",null,"滦平县","130824","district",null,"130000","130800",null,"隆化县","130825","district",null,"130000","130800",null,"丰宁满族自治县","130826","district",null,"130000","130800",null,"宽城满族自治县","130827","district",null,"130000","130800",null,"围场满族蒙古族自治县","130828","district",null,"130000","130800",null]
08:57:08.966 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2355 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["平泉市","130881","district",null,"130000","130800",null,"安次区","131002","district",null,"130000","131000",null,"广阳区","131003","district",null,"130000","131000",null,"固安县","131022","district",null,"130000","131000",null,"永清县","131023","district",null,"130000","131000",null,"香河县","131024","district",null,"130000","131000",null,"大城县","131025","district",null,"130000","131000",null,"文安县","131026","district",null,"130000","131000",null,"大厂回族自治县","131028","district",null,"130000","131000",null,"霸州市","131081","district",null,"130000","131000",null,"三河市","131082","district",null,"130000","131000",null,"新华区","130902","district",null,"130000","130900",null,"运河区","130903","district",null,"130000","130900",null,"沧县","130921","district",null,"130000","130900",null,"青县","130922","district",null,"130000","130900",null,"东光县","130923","district",null,"130000","130900",null,"海兴县","130924","district",null,"130000","130900",null,"盐山县","130925","district",null,"130000","130900",null,"肃宁县","130926","district",null,"130000","130900",null,"南皮县","130927","district",null,"130000","130900",null,"吴桥县","130928","district",null,"130000","130900",null,"献县","130929","district",null,"130000","130900",null,"孟村回族自治县","130930","district",null,"130000","130900",null,"泊头市","130981","district",null,"130000","130900",null,"任丘市","130982","district",null,"130000","130900",null,"黄骅市","130983","district",null,"130000","130900",null,"河间市","130984","district",null,"130000","130900",null,"桃城区","131102","district",null,"130000","131100",null,"冀州区","131103","district",null,"130000","131100",null,"枣强县","131121","district",null,"130000","131100",null,"武邑县","131122","district",null,"130000","131100",null,"武强县","131123","district",null,"130000","131100",null,"饶阳县","131124","district",null,"130000","131100",null,"安平县","131125","district",null,"130000","131100",null,"故城县","131126","district",null,"130000","131100",null,"景县","131127","district",null,"130000","131100",null,"阜城县","131128","district",null,"130000","131100",null,"深州市","131182","district",null,"130000","131100",null,"襄都区","130502","district",null,"130000","130500",null,"信都区","130503","district",null,"130000","130500",null,"任泽区","130505","district",null,"130000","130500",null,"南和区","130506","district",null,"130000","130500",null,"临城县","130522","district",null,"130000","130500",null,"内丘县","130523","district",null,"130000","130500",null,"柏乡县","130524","district",null,"130000","130500",null,"隆尧县","130525","district",null,"130000","130500",null,"宁晋县","130528","district",null,"130000","130500",null,"巨鹿县","130529","district",null,"130000","130500",null,"新河县","130530","district",null,"130000","130500",null,"广宗县","130531","district",null,"130000","130500",null,"平乡县","130532","district",null,"130000","130500",null,"威县","130533","district",null,"130000","130500",null,"清河县","130534","district",null,"130000","130500",null,"临西县","130535","district",null,"130000","130500",null,"南宫市","130581","district",null,"130000","130500",null,"沙河市","130582","district",null,"130000","130500",null,"邯山区","130402","district",null,"130000","130400",null,"丛台区","130403","district",null,"130000","130400",null,"复兴区","130404","district",null,"130000","130400",null,"峰峰矿区","130406","district",null,"130000","130400",null,"肥乡区","130407","district",null,"130000","130400",null,"永年区","130408","district",null,"130000","130400",null,"临漳县","130423","district",null,"130000","130400",null,"成安县","130424","district",null,"130000","130400",null,"大名县","130425","district",null,"130000","130400",null,"涉县","130426","district",null,"130000","130400",null,"磁县","130427","district",null,"130000","130400",null,"邱县","130430","district",null,"130000","130400",null,"鸡泽县","130431","district",null,"130000","130400",null,"广平县","130432","district",null,"130000","130400",null,"馆陶县","130433","district",null,"130000","130400",null,"魏县","130434","district",null,"130000","130400",null,"曲周县","130435","district",null,"130000","130400",null,"武安市","130481","district",null,"130000","130400",null,"长安区","130102","district",null,"130000","130100",null,"桥西区","130104","district",null,"130000","130100",null,"新华区","130105","district",null,"130000","130100",null,"井陉矿区","130107","district",null,"130000","130100",null,"裕华区","130108","district",null,"130000","130100",null,"藁城区","130109","district",null,"130000","130100",null,"鹿泉区","130110","district",null,"130000","130100",null,"栾城区","130111","district",null,"130000","130100",null,"井陉县","130121","district",null,"130000","130100",null,"正定县","130123","district",null,"130000","130100",null,"行唐县","130125","district",null,"130000","130100",null,"灵寿县","130126","district",null,"130000","130100",null,"高邑县","130127","district",null,"130000","130100",null,"深泽县","130128","district",null,"130000","130100",null,"赞皇县","130129","district",null,"130000","130100",null,"无极县","130130","district",null,"130000","130100",null,"平山县","130131","district",null,"130000","130100",null,"元氏县","130132","district",null,"130000","130100",null,"赵县","130133","district",null,"130000","130100",null,"辛集市","130181","district",null,"130000","130100",null,"晋州市","130183","district",null,"130000","130100",null,"新乐市","130184","district",null,"130000","130100",null,"竞秀区","130602","district",null,"130000","130600",null,"莲池区","130606","district",null,"130000","130600",null,"满城区","130607","district",null,"130000","130600",null,"清苑区","130608","district",null,"130000","130600",null]
08:57:10.082 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1110 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["徐水区","130609","district",null,"130000","130600",null,"涞水县","130623","district",null,"130000","130600",null,"阜平县","130624","district",null,"130000","130600",null,"定兴县","130626","district",null,"130000","130600",null,"唐县","130627","district",null,"130000","130600",null,"高阳县","130628","district",null,"130000","130600",null,"容城县","130629","district",null,"130000","130600",null,"涞源县","130630","district",null,"130000","130600",null,"望都县","130631","district",null,"130000","130600",null,"安新县","130632","district",null,"130000","130600",null,"易县","130633","district",null,"130000","130600",null,"曲阳县","130634","district",null,"130000","130600",null,"蠡县","130635","district",null,"130000","130600",null,"顺平县","130636","district",null,"130000","130600",null,"博野县","130637","district",null,"130000","130600",null,"雄县","130638","district",null,"130000","130600",null,"涿州市","130681","district",null,"130000","130600",null,"定州市","130682","district",null,"130000","130600",null,"安国市","130683","district",null,"130000","130600",null,"高碑店市","130684","district",null,"130000","130600",null,"桥东区","130702","district",null,"130000","130700",null,"桥西区","130703","district",null,"130000","130700",null,"宣化区","130705","district",null,"130000","130700",null,"下花园区","130706","district",null,"130000","130700",null,"万全区","130708","district",null,"130000","130700",null,"崇礼区","130709","district",null,"130000","130700",null,"张北县","130722","district",null,"130000","130700",null,"康保县","130723","district",null,"130000","130700",null,"沽源县","130724","district",null,"130000","130700",null,"尚义县","130725","district",null,"130000","130700",null,"蔚县","130726","district",null,"130000","130700",null,"阳原县","130727","district",null,"130000","130700",null,"怀安县","130728","district",null,"130000","130700",null,"怀来县","130730","district",null,"130000","130700",null,"涿鹿县","130731","district",null,"130000","130700",null,"赤城县","130732","district",null,"130000","130700",null,"峪泉镇","620200","street",null,"620000","620200",null,"新城镇","620200","street",null,"620000","620200",null,"文殊镇","620200","street",null,"620000","620200",null,"钢城街道","620200","street",null,"620000","620200",null,"雄关街道","620200","street",null,"620000","620200",null,"肃州区","620902","district",null,"620000","620900",null,"金塔县","620921","district",null,"620000","620900",null,"瓜州县","620922","district",null,"620000","620900",null,"肃北蒙古族自治县","620923","district",null,"620000","620900",null,"阿克塞哈萨克族自治县","620924","district",null,"620000","620900",null,"玉门市","620981","district",null,"620000","620900",null,"敦煌市","620982","district",null,"620000","620900",null,"城关区","620102","district",null,"620000","620100",null,"七里河区","620103","district",null,"620000","620100",null,"西固区","620104","district",null,"620000","620100",null,"安宁区","620105","district",null,"620000","620100",null,"红古区","620111","district",null,"620000","620100",null,"永登县","620121","district",null,"620000","620100",null,"皋兰县","620122","district",null,"620000","620100",null,"榆中县","620123","district",null,"620000","620100",null,"崆峒区","620802","district",null,"620000","620800",null,"泾川县","620821","district",null,"620000","620800",null,"灵台县","620822","district",null,"620000","620800",null,"崇信县","620823","district",null,"620000","620800",null,"庄浪县","620825","district",null,"620000","620800",null,"静宁县","620826","district",null,"620000","620800",null,"华亭市","620881","district",null,"620000","620800",null,"金川区","620302","district",null,"620000","620300",null,"永昌县","620321","district",null,"620000","620300",null,"秦州区","620502","district",null,"620000","620500",null,"麦积区","620503","district",null,"620000","620500",null,"清水县","620521","district",null,"620000","620500",null,"秦安县","620522","district",null,"620000","620500",null,"甘谷县","620523","district",null,"620000","620500",null,"武山县","620524","district",null,"620000","620500",null,"张家川回族自治县","620525","district",null,"620000","620500",null,"白银区","620402","district",null,"620000","620400",null,"平川区","620403","district",null,"620000","620400",null,"靖远县","620421","district",null,"620000","620400",null,"会宁县","620422","district",null,"620000","620400",null,"景泰县","620423","district",null,"620000","620400",null,"凉州区","620602","district",null,"620000","620600",null,"民勤县","620621","district",null,"620000","620600",null,"古浪县","620622","district",null,"620000","620600",null,"天祝藏族自治县","620623","district",null,"620000","620600",null,"甘州区","620702","district",null,"620000","620700",null,"肃南裕固族自治县","620721","district",null,"620000","620700",null,"民乐县","620722","district",null,"620000","620700",null,"临泽县","620723","district",null,"620000","620700",null,"高台县","620724","district",null,"620000","620700",null,"山丹县","620725","district",null,"620000","620700",null,"安定区","621102","district",null,"620000","621100",null,"通渭县","621121","district",null,"620000","621100",null,"陇西县","621122","district",null,"620000","621100",null,"渭源县","621123","district",null,"620000","621100",null,"临洮县","621124","district",null,"620000","621100",null,"漳县","621125","district",null,"620000","621100",null,"岷县","621126","district",null,"620000","621100",null,"合作市","623001","district",null,"620000","623000",null,"临潭县","623021","district",null,"620000","623000",null,"卓尼县","623022","district",null,"620000","623000",null,"舟曲县","623023","district",null,"620000","623000",null,"迭部县","623024","district",null,"620000","623000",null,"玛曲县","623025","district",null,"620000","623000",null]
08:57:12.235 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1215 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["射洪市","510981","district",null,"510000","510900",null,"雁江区","512002","district",null,"510000","512000",null,"安岳县","512021","district",null,"510000","512000",null,"乐至县","512022","district",null,"510000","512000",null,"东坡区","511402","district",null,"510000","511400",null,"彭山区","511403","district",null,"510000","511400",null,"仁寿县","511421","district",null,"510000","511400",null,"洪雅县","511423","district",null,"510000","511400",null,"丹棱县","511424","district",null,"510000","511400",null,"青神县","511425","district",null,"510000","511400",null,"市中区","511002","district",null,"510000","511000",null,"东兴区","511011","district",null,"510000","511000",null,"威远县","511024","district",null,"510000","511000",null,"资中县","511025","district",null,"510000","511000",null,"隆昌市","511083","district",null,"510000","511000",null,"市中区","511102","district",null,"510000","511100",null,"沙湾区","511111","district",null,"510000","511100",null,"五通桥区","511112","district",null,"510000","511100",null,"金口河区","511113","district",null,"510000","511100",null,"犍为县","511123","district",null,"510000","511100",null,"井研县","511124","district",null,"510000","511100",null,"夹江县","511126","district",null,"510000","511100",null,"沐川县","511129","district",null,"510000","511100",null,"峨边彝族自治县","511132","district",null,"510000","511100",null,"马边彝族自治县","511133","district",null,"510000","511100",null,"峨眉山市","511181","district",null,"510000","511100",null,"自流井区","510302","district",null,"510000","510300",null,"贡井区","510303","district",null,"510000","510300",null,"大安区","510304","district",null,"510000","510300",null,"沿滩区","510311","district",null,"510000","510300",null,"荣县","510321","district",null,"510000","510300",null,"富顺县","510322","district",null,"510000","510300",null,"江阳区","510502","district",null,"510000","510500",null,"纳溪区","510503","district",null,"510000","510500",null,"龙马潭区","510504","district",null,"510000","510500",null,"泸县","510521","district",null,"510000","510500",null,"合江县","510522","district",null,"510000","510500",null,"叙永县","510524","district",null,"510000","510500",null,"古蔺县","510525","district",null,"510000","510500",null,"翠屏区","511502","district",null,"510000","511500",null,"南溪区","511503","district",null,"510000","511500",null,"叙州区","511504","district",null,"510000","511500",null,"江安县","511523","district",null,"510000","511500",null,"长宁县","511524","district",null,"510000","511500",null,"高县","511525","district",null,"510000","511500",null,"珙县","511526","district",null,"510000","511500",null,"筠连县","511527","district",null,"510000","511500",null,"兴文县","511528","district",null,"510000","511500",null,"屏山县","511529","district",null,"510000","511500",null,"西昌市","513401","district",null,"510000","513400",null,"会理市","513402","district",null,"510000","513400",null,"木里藏族自治县","513422","district",null,"510000","513400",null,"盐源县","513423","district",null,"510000","513400",null,"德昌县","513424","district",null,"510000","513400",null,"会东县","513426","district",null,"510000","513400",null,"宁南县","513427","district",null,"510000","513400",null,"普格县","513428","district",null,"510000","513400",null,"布拖县","513429","district",null,"510000","513400",null,"金阳县","513430","district",null,"510000","513400",null,"昭觉县","513431","district",null,"510000","513400",null,"喜德县","513432","district",null,"510000","513400",null,"冕宁县","513433","district",null,"510000","513400",null,"越西县","513434","district",null,"510000","513400",null,"甘洛县","513435","district",null,"510000","513400",null,"美姑县","513436","district",null,"510000","513400",null,"雷波县","513437","district",null,"510000","513400",null,"东区","510402","district",null,"510000","510400",null,"西区","510403","district",null,"510000","510400",null,"仁和区","510411","district",null,"510000","510400",null,"米易县","510421","district",null,"510000","510400",null,"盐边县","510422","district",null,"510000","510400",null,"康定市","513301","district",null,"510000","513300",null,"泸定县","513322","district",null,"510000","513300",null,"丹巴县","513323","district",null,"510000","513300",null,"九龙县","513324","district",null,"510000","513300",null,"雅江县","513325","district",null,"510000","513300",null,"道孚县","513326","district",null,"510000","513300",null,"炉霍县","513327","district",null,"510000","513300",null,"甘孜县","513328","district",null,"510000","513300",null,"新龙县","513329","district",null,"510000","513300",null,"德格县","513330","district",null,"510000","513300",null,"白玉县","513331","district",null,"510000","513300",null,"石渠县","513332","district",null,"510000","513300",null,"色达县","513333","district",null,"510000","513300",null,"理塘县","513334","district",null,"510000","513300",null,"巴塘县","513335","district",null,"510000","513300",null,"乡城县","513336","district",null,"510000","513300",null,"稻城县","513337","district",null,"510000","513300",null,"得荣县","513338","district",null,"510000","513300",null,"雨城区","511802","district",null,"510000","511800",null,"名山区","511803","district",null,"510000","511800",null,"荥经县","511822","district",null,"510000","511800",null,"汉源县","511823","district",null,"510000","511800",null,"石棉县","511824","district",null,"510000","511800",null,"天全县","511825","district",null,"510000","511800",null,"芦山县","511826","district",null,"510000","511800",null,"宝兴县","511827","district",null,"510000","511800",null,"马尔康市","513201","district",null,"510000","513200",null,"汶川县","513221","district",null,"510000","513200",null,"理县","513222","district",null,"510000","513200",null]
08:57:15.258 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3012 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["茂县","513223","district",null,"510000","513200",null,"松潘县","513224","district",null,"510000","513200",null,"九寨沟县","513225","district",null,"510000","513200",null,"金川县","513226","district",null,"510000","513200",null,"小金县","513227","district",null,"510000","513200",null,"黑水县","513228","district",null,"510000","513200",null,"壤塘县","513230","district",null,"510000","513200",null,"阿坝县","513231","district",null,"510000","513200",null,"若尔盖县","513232","district",null,"510000","513200",null,"红原县","513233","district",null,"510000","513200",null,"和平区","120101","district",null,"120000","120100",null,"河东区","120102","district",null,"120000","120100",null,"河西区","120103","district",null,"120000","120100",null,"南开区","120104","district",null,"120000","120100",null,"河北区","120105","district",null,"120000","120100",null,"红桥区","120106","district",null,"120000","120100",null,"东丽区","120110","district",null,"120000","120100",null,"西青区","120111","district",null,"120000","120100",null,"津南区","120112","district",null,"120000","120100",null,"北辰区","120113","district",null,"120000","120100",null,"武清区","120114","district",null,"120000","120100",null,"宝坻区","120115","district",null,"120000","120100",null,"滨海新区","120116","district",null,"120000","120100",null,"宁河区","120117","district",null,"120000","120100",null,"静海区","120118","district",null,"120000","120100",null,"蓟州区","120119","district",null,"120000","120100",null,"东城区","110101","district",null,"110000","110100",null,"西城区","110102","district",null,"110000","110100",null,"朝阳区","110105","district",null,"110000","110100",null,"丰台区","110106","district",null,"110000","110100",null,"石景山区","110107","district",null,"110000","110100",null,"海淀区","110108","district",null,"110000","110100",null,"门头沟区","110109","district",null,"110000","110100",null,"房山区","110111","district",null,"110000","110100",null,"通州区","110112","district",null,"110000","110100",null,"顺义区","110113","district",null,"110000","110100",null,"昌平区","110114","district",null,"110000","110100",null,"大兴区","110115","district",null,"110000","110100",null,"怀柔区","110116","district",null,"110000","110100",null,"平谷区","110117","district",null,"110000","110100",null,"密云区","110118","district",null,"110000","110100",null,"延庆区","110119","district",null,"110000","110100",null,"卡若区","540302","district",null,"540000","540300",null,"江达县","540321","district",null,"540000","540300",null,"贡觉县","540322","district",null,"540000","540300",null,"类乌齐县","540323","district",null,"540000","540300",null,"丁青县","540324","district",null,"540000","540300",null,"察雅县","540325","district",null,"540000","540300",null,"八宿县","540326","district",null,"540000","540300",null,"左贡县","540327","district",null,"540000","540300",null,"芒康县","540328","district",null,"540000","540300",null,"洛隆县","540329","district",null,"540000","540300",null,"边坝县","540330","district",null,"540000","540300",null,"色尼区","540602","district",null,"540000","540600",null,"嘉黎县","540621","district",null,"540000","540600",null,"比如县","540622","district",null,"540000","540600",null,"聂荣县","540623","district",null,"540000","540600",null,"安多县","540624","district",null,"540000","540600",null,"申扎县","540625","district",null,"540000","540600",null,"索县","540626","district",null,"540000","540600",null,"班戈县","540627","district",null,"540000","540600",null,"巴青县","540628","district",null,"540000","540600",null,"尼玛县","540629","district",null,"540000","540600",null,"双湖县","540630","district",null,"540000","540600",null,"城关区","540102","district",null,"540000","540100",null,"堆龙德庆区","540103","district",null,"540000","540100",null,"达孜区","540104","district",null,"540000","540100",null,"林周县","540121","district",null,"540000","540100",null,"当雄县","540122","district",null,"540000","540100",null,"尼木县","540123","district",null,"540000","540100",null,"曲水县","540124","district",null,"540000","540100",null,"墨竹工卡县","540127","district",null,"540000","540100",null,"桑珠孜区","540202","district",null,"540000","540200",null,"南木林县","540221","district",null,"540000","540200",null,"江孜县","540222","district",null,"540000","540200",null,"定日县","540223","district",null,"540000","540200",null,"萨迦县","540224","district",null,"540000","540200",null,"拉孜县","540225","district",null,"540000","540200",null,"昂仁县","540226","district",null,"540000","540200",null,"谢通门县","540227","district",null,"540000","540200",null,"白朗县","540228","district",null,"540000","540200",null,"仁布县","540229","district",null,"540000","540200",null,"康马县","540230","district",null,"540000","540200",null,"定结县","540231","district",null,"540000","540200",null,"仲巴县","540232","district",null,"540000","540200",null,"亚东县","540233","district",null,"540000","540200",null,"吉隆县","540234","district",null,"540000","540200",null,"聂拉木县","540235","district",null,"540000","540200",null,"萨嘎县","540236","district",null,"540000","540200",null,"岗巴县","540237","district",null,"540000","540200",null,"乃东区","540502","district",null,"540000","540500",null,"扎囊县","540521","district",null,"540000","540500",null,"贡嘎县","540522","district",null,"540000","540500",null,"桑日县","540523","district",null,"540000","540500",null,"琼结县","540524","district",null,"540000","540500",null,"曲松县","540525","district",null,"540000","540500",null,"措美县","540526","district",null,"540000","540500",null,"洛扎县","540527","district",null,"540000","540500",null,"加查县","540528","district",null,"540000","540500",null,"隆子县","540529","district",null,"540000","540500",null]
08:57:21.207 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2970 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["五华区","530102","district",null,"530000","530100",null,"盘龙区","530103","district",null,"530000","530100",null,"官渡区","530111","district",null,"530000","530100",null,"西山区","530112","district",null,"530000","530100",null,"东川区","530113","district",null,"530000","530100",null,"呈贡区","530114","district",null,"530000","530100",null,"晋宁区","530115","district",null,"530000","530100",null,"富民县","530124","district",null,"530000","530100",null,"宜良县","530125","district",null,"530000","530100",null,"石林彝族自治县","530126","district",null,"530000","530100",null,"嵩明县","530127","district",null,"530000","530100",null,"禄劝彝族苗族自治县","530128","district",null,"530000","530100",null,"寻甸回族彝族自治县","530129","district",null,"530000","530100",null,"安宁市","530181","district",null,"530000","530100",null,"思茅区","530802","district",null,"530000","530800",null,"宁洱哈尼族彝族自治县","530821","district",null,"530000","530800",null,"墨江哈尼族自治县","530822","district",null,"530000","530800",null,"景东彝族自治县","530823","district",null,"530000","530800",null,"景谷傣族彝族自治县","530824","district",null,"530000","530800",null,"镇沅彝族哈尼族拉祜族自治县","530825","district",null,"530000","530800",null,"江城哈尼族彝族自治县","530826","district",null,"530000","530800",null,"孟连傣族拉祜族佤族自治县","530827","district",null,"530000","530800",null,"澜沧拉祜族自治县","530828","district",null,"530000","530800",null,"西盟佤族自治县","530829","district",null,"530000","530800",null,"瑞丽市","533102","district",null,"530000","533100",null,"芒市","533103","district",null,"530000","533100",null,"梁河县","533122","district",null,"530000","533100",null,"盈江县","533123","district",null,"530000","533100",null,"陇川县","533124","district",null,"530000","533100",null]
08:57:23.412 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2168 millis. select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time
        from sys_dept d[]
08:57:32.551 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1318 millis. SELECT * FROM QRTZ_LOCKS WHERE SCHED_NAME = 'RuoyiScheduler' AND LOCK_NAME = ? FOR UPDATE["TRIGGER_ACCESS"]
08:57:34.798 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1853 millis. INSERT INTO QRTZ_JOB_DETAILS (SCHED_NAME, JOB_NAME, JOB_GROUP, DESCRIPTION, JOB_CLASS_NAME, IS_DURABLE, IS_NONCONCURRENT, IS_UPDATE_DATA, REQUESTS_RECOVERY, JOB_DATA)  VALUES('RuoyiScheduler', ?, ?, ?, ?, ?, ?, ?, ?, ?)["TASK_CLASS_NAME2","DEFAULT",null,"com.wanou.common.utils.job.QuartzDisallowConcurrentExecution",false,true,false,false,"<[B>"]
08:57:37.381 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1115 millis. SELECT * FROM QRTZ_CRON_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?["TASK_CLASS_NAME2","DEFAULT"]
08:58:08.529 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393843587_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1353 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748393875175,"WAITING"]
08:58:14.927 [http-nio-8080-exec-9] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1691 millis. select * from device_store
         WHERE is_available = 1[]
08:59:10.668 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 49436 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748393930498,1748393889231]
08:59:11.107 [http-nio-8080-exec-13] ERROR druid.sql.Statement - [statementLogError,149] - {conn-10006, pstmt-20157} execute error. insert into orders (user_id, coupon_id, order_sn,
      create_time, member_username, total_amount, 
      pay_amount, freight_amount, promotion_amount, 
      integration_amount, coupon_amount, discount_amount, 
      pay_type, source_type, status, 
      order_type, delivery_company, delivery_sn, 
      auto_confirm_day, integration, growth, 
      promotion_info, bill_type, bill_header, 
      bill_content, bill_receiver_phone, bill_receiver_email, 
      receiver_name, receiver_phone, receiver_post_code, 
      receiver_province, receiver_city, receiver_region, 
      receiver_detail_address, note, confirm_status, 
      delete_status, use_integration, payment_time, 
      delivery_time, receive_time, comment_time, 
      modify_time)
    values (?, ?, ?,
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?)
java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy247.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$6a483781.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
08:59:11.159 [http-nio-8080-exec-13] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
; Column 'user_id' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
; Column 'user_id' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$6a483781.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'user_id' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy247.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 113 common frames omitted
08:59:59.542 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1395 millis. select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark
		from sys_dict_data
     
		where status = '0' and dict_type = ? order by dict_sort asc["live_state"]
09:00:11.991 [http-nio-8080-exec-1] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - JSON parse error: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token; nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token; nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:285)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:243)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:205)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:158)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:131)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
	at com.fasterxml.jackson.databind.exc.MismatchedInputException.from(MismatchedInputException.java:59)
	at com.fasterxml.jackson.databind.DeserializationContext.reportInputMismatch(DeserializationContext.java:1445)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnexpectedToken(DeserializationContext.java:1219)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._deserializeFromArray(StdDeserializer.java:714)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:40)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:10)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:369)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:159)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:286)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:245)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:27)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:369)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:159)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4218)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3267)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:274)
	... 102 common frames omitted
09:00:35.243 [http-nio-8080-exec-7] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - JSON parse error: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token; nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token; nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:285)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:243)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:205)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:158)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:131)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:121)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:167)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize instance of `java.lang.String` out of START_ARRAY token
 at [Source: (PushbackInputStream); line: 1, column: 428] (through reference chain: com.wanou.project.system.domain.dto.OrderDetail["orderItemList"]->java.util.ArrayList[0]->com.wanou.project.system.domain.OrderItem["productPic"])
	at com.fasterxml.jackson.databind.exc.MismatchedInputException.from(MismatchedInputException.java:59)
	at com.fasterxml.jackson.databind.DeserializationContext.reportInputMismatch(DeserializationContext.java:1445)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnexpectedToken(DeserializationContext.java:1219)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer._deserializeFromArray(StdDeserializer.java:714)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:40)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:10)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:369)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:159)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:286)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:245)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:27)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:369)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:159)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4218)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3267)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:274)
	... 102 common frames omitted
09:01:31.041 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1117 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748394119502,1748394077924]
09:02:10.699 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1364 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394117334,"WAITING"]
09:02:36.551 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2828 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394141722,"WAITING"]
09:02:36.552 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2835 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748394182406,1748394141716]
09:03:01.513 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3053 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394166460,"WAITING"]
09:03:02.782 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1648 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
09:03:07.885 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 5102 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748394182783,"LAPTOP-JHBER3JS1748393986986"]
09:03:15.032 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 5691 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748394211911,1748394177339]
09:03:31.395 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2561 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394196831,"WAITING"]
09:03:32.056 [http-nio-8080-exec-10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3221 millis. select * from device_store where id = ?[3]
09:03:32.644 [http-nio-8080-exec-11] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
09:03:36.247 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2252 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394201994,"WAITING"]
09:03:37.558 [http-nio-8080-exec-16] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - Request method 'GET' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
09:03:38.333 [http-nio-8080-exec-13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1007 millis. select * from device_store
         WHERE is_available = 1[]
09:03:48.250 [http-nio-8080-exec-17] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - Request method 'GET' not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:213)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:422)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:367)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:110)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:59)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:396)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1234)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1016)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
09:03:51.512 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1955 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748394229555,"LAPTOP-JHBER3JS1748393986986"]
09:03:58.876 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1198 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394225677,"WAITING"]
09:03:59.099 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1977 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748394265852,1748394225120]
09:04:04.896 [http-nio-8080-exec-18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1794 millis. select * from device_store
         WHERE is_available = 1[]
09:04:06.098 [http-nio-8080-exec-19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1190 millis. select * from device_store
         WHERE is_available = 1[]
09:04:30.110 [http-nio-8080-exec-23] ERROR druid.sql.Statement - [statementLogError,149] - {conn-10004, pstmt-20207} execute error. insert into orders (user_id, coupon_id, order_sn,
      create_time, member_username, total_amount, 
      pay_amount, freight_amount, promotion_amount, 
      integration_amount, coupon_amount, discount_amount, 
      pay_type, source_type, status, 
      order_type, delivery_company, delivery_sn, 
      auto_confirm_day, integration, growth, 
      promotion_info, bill_type, bill_header, 
      bill_content, bill_receiver_phone, bill_receiver_email, 
      receiver_name, receiver_phone, receiver_post_code, 
      receiver_province, receiver_city, receiver_region, 
      receiver_detail_address, note, confirm_status, 
      delete_status, use_integration, payment_time, 
      delivery_time, receive_time, comment_time, 
      modify_time)
    values (?, ?, ?,
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?)
java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy248.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$144e0b1f.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
09:04:30.147 [http-nio-8080-exec-23] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
; Column 'delete_status' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
; Column 'delete_status' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$144e0b1f.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy248.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 113 common frames omitted
09:08:37.595 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1046 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748394546088,1748394504548]
09:08:43.337 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748393986986_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1175 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748394510161,"WAITING"]
09:08:48.473 [http-nio-8080-exec-29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3282 millis. select * from device_store
         WHERE is_available = 1[]
09:08:51.348 [http-nio-8080-exec-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1337 millis. select * from device_store where id = ?[3]
09:08:51.586 [http-nio-8080-exec-30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3097 millis. select * from device_store
         WHERE is_available = 1[]
09:43:54.960 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1500 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748396662969,1748396621455]
10:14:55.251 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1598 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748398493652,"LAPTOP-JHBER3JS1748396206097"]
10:14:56.315 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1056 millis. INSERT INTO QRTZ_SCHEDULER_STATE (SCHED_NAME, INSTANCE_NAME, LAST_CHECKIN_TIME, CHECKIN_INTERVAL) VALUES('RuoyiScheduler', ?, ?, ?)["LAPTOP-JHBER3JS1748396206097",1748398493652,15000]
10:22:25.939 [Druid-ConnectionPool-Create-89961399] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:156)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:787)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:38)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:227)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1659)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:340)
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1348)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 12 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at java.base/sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1701)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1519)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:456)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:427)
	at com.mysql.cj.protocol.ExportControlled.performTlsHandshake(ExportControlled.java:317)
	at com.mysql.cj.protocol.StandardSocketFactory.performTlsHandshake(StandardSocketFactory.java:188)
	at com.mysql.cj.protocol.a.NativeSocketConnection.performTlsHandshake(NativeSocketConnection.java:97)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:331)
	... 17 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1511)
	... 24 common frames omitted
10:22:45.595 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_MisfireHandler] ERROR druid.sql.Statement - [statementLogError,149] - {conn-10015, pstmt-20795} execute error. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1003)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3240)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.executeQuery(PreparedStatementProxyImpl.java:181)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:227)
	at org.quartz.impl.jdbcjobstore.StdJDBCDelegate.countMisfiredTriggersInState(StdJDBCDelegate.java:393)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3253)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:544)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:708)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:647)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:946)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1075)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:930)
	... 12 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:484)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1459)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1070)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:538)
	... 17 common frames omitted
10:22:45.596 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_MisfireHandler] ERROR c.a.d.p.DruidPooledStatement - [errorCheck,367] - CommunicationsException, druid version 1.2.5, jdbcUrl : ********************************************************************************************************************************************************************************, testWhileIdle true, idle millis 467014, minIdle 10, poolingCount 2, timeBetweenEvictionRunsMillis 60000, lastValidIdleMillis 467014, driver com.mysql.cj.jdbc.Driver, exceptionSorter com.alibaba.druid.pool.vendor.MySqlExceptionSorter
10:22:45.597 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_MisfireHandler] ERROR c.a.d.p.DruidDataSource - [handleFatalError,1859] - discard connection
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1003)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3240)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.executeQuery(PreparedStatementProxyImpl.java:181)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:227)
	at org.quartz.impl.jdbcjobstore.StdJDBCDelegate.countMisfiredTriggersInState(StdJDBCDelegate.java:393)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3253)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:544)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:708)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:647)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:946)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1075)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:930)
	... 12 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:484)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1459)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1070)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:538)
	... 17 common frames omitted
10:22:45.602 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748396206097_MisfireHandler] ERROR o.s.s.q.LocalDataSourceJobStore - [manage,4017] - MisfireHandler: Error handling misfires: Database error recovering from misfires.
org.quartz.JobPersistenceException: Database error recovering from misfires.
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3274)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1003)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3240)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_executeQuery(FilterEventAdapter.java:465)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_executeQuery(FilterChainImpl.java:3237)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.executeQuery(PreparedStatementProxyImpl.java:181)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:227)
	at org.quartz.impl.jdbcjobstore.StdJDBCDelegate.countMisfiredTriggersInState(StdJDBCDelegate.java:393)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3253)
	... 2 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 19,679 milliseconds ago. The last packet sent successfully to the server was 467,012 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:544)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:708)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:647)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:946)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1075)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:930)
	... 12 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:484)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1459)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1070)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:538)
	... 17 common frames omitted
10:49:42.955 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748399865961_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3241 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
11:00:29.905 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2466 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748401256706,1748401215436]
11:00:36.548 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401076455_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3770 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748401220778,"WAITING"]
11:00:41.636 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401076455_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1349 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
11:00:43.726 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401076455_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2090 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748401241636,"LAPTOP-JHBER3JS1748401076455"]
11:00:46.546 [Druid-ConnectionPool-Create-254310990] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state null
java.sql.SQLException: validateConnection false
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1418)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1733)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1398)
	... 2 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:666)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:134)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.ping(NativeSession.java:1151)
	at com.mysql.cj.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:1526)
	... 8 common frames omitted
11:04:08.290 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401076455_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1649 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748401434640,"WAITING"]
11:05:21.786 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401494257_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2953 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748401506832,"WAITING"]
11:05:23.296 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748401494257_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1801 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
11:37:40.328 [Druid-ConnectionPool-Create-1398164044] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state null
java.sql.SQLException: validateConnection false
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1418)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1733)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1398)
	... 2 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:666)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:134)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.ping(NativeSession.java:1151)
	at com.mysql.cj.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:1526)
	... 8 common frames omitted
11:38:00.413 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748402867741_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1153 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748403467258,"WAITING"]
11:38:10.414 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748402867741_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1221 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
11:38:11.673 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748402867741_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1258 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748403490414,"LAPTOP-JHBER3JS1748402867741"]
11:38:12.262 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748402867741_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1532 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748403478729,"WAITING"]
11:52:06.018 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748402867741_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1460 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
12:00:29.575 [http-nio-8080-exec-19] ERROR druid.sql.Statement - [statementLogError,149] - {conn-10017, pstmt-20829} execute error. insert into orders (user_id, coupon_id, order_sn,
      create_time, member_username, total_amount, 
      pay_amount, freight_amount, promotion_amount, 
      integration_amount, coupon_amount, discount_amount, 
      pay_type, source_type, status, 
      order_type, delivery_company, delivery_sn, 
      auto_confirm_day, integration, growth, 
      promotion_info, bill_type, bill_header, 
      bill_content, bill_receiver_phone, bill_receiver_email, 
      receiver_name, receiver_phone, receiver_post_code, 
      receiver_province, receiver_city, receiver_region, 
      receiver_detail_address, note, confirm_status, 
      delete_status, use_integration, payment_time, 
      delivery_time, receive_time, comment_time, 
      modify_time)
    values (?, ?, ?,
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?, ?, ?, 
      ?)
java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy249.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$3de471c6.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
12:00:29.634 [http-nio-8080-exec-19] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
; Column 'delete_status' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
### The error may exist in file [D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes\mybatis\system\OrderMapper.xml]
### The error may involve com.wanou.project.system.mapper.OrderMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into orders (user_id, coupon_id, order_sn,       create_time, member_username, total_amount,        pay_amount, freight_amount, promotion_amount,        integration_amount, coupon_amount, discount_amount,        pay_type, source_type, status,        order_type, delivery_company, delivery_sn,        auto_confirm_day, integration, growth,        promotion_info, bill_type, bill_header,        bill_content, bill_receiver_phone, bill_receiver_email,        receiver_name, receiver_phone, receiver_post_code,        receiver_province, receiver_city, receiver_region,        receiver_detail_address, note, confirm_status,        delete_status, use_integration, payment_time,        delivery_time, receive_time, comment_time,        modify_time)     values (?, ?, ?,       ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?, ?, ?,        ?)
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
; Column 'delete_status' cannot be null; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy128.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy190.insert(Unknown Source)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl.addOrder(OrderAppServiceImpl.java:187)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$FastClassBySpringCGLIB$$3db7ffe6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.wanou.project.system.service.impl.OrderAppServiceImpl$$EnhancerBySpringCGLIB$$3de471c6.addOrder(<generated>)
	at com.wanou.project.system.controller.OrderAppController.addOrder(OrderAppController.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_status' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3461)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3459)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor70.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy138.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.ReuseExecutor.doUpdate(ReuseExecutor.java:52)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy249.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at jdk.internal.reflect.GeneratedMethodAccessor73.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 113 common frames omitted
14:23:07.423 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2530 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748413414570,1748413372890]
14:28:48.407 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1110 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
14:31:07.969 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1225 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748413895914,1748413854743]
15:20:55.642 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1039 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748416842602,"WAITING"]
16:05:57.729 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2998 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748419542729,"WAITING"]
16:09:35.820 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1904 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748419773911,"LAPTOP-JHBER3JS1748413179948"]
16:09:50.713 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1208 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
16:11:08.213 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1491 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748419854721,"WAITING"]
16:13:08.577 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1643 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748419974934,"WAITING"]
16:13:14.754 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1028 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
16:13:15.885 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1130 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748419994755,"LAPTOP-JHBER3JS1748413179948"]
16:22:37.346 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1427 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
16:47:07.852 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1031 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748422014817,"WAITING"]
17:23:21.082 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748413179948_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1410 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748424187670,"WAITING"]
17:23:33.565 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2228 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748424238104,1748424199337]
