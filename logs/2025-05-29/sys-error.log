08:51:38.201 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1722 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748479926321,1748479884468]
09:04:32.537 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.<PERSON>at<PERSON> - [internalAfterStatementExecute,487] - slow sql 1254 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748480659282,"WAITING"]
09:05:36.813 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1240 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748480723573,"WAITING"]
09:08:32.276 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1191 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748480936045,1748480899084]
09:08:38.572 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 5856 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748480900715,"WAITING"]
10:06:32.499 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1976 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748484378522,"WAITING"]
10:06:45.900 [Druid-ConnectionPool-Create-1871293699] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:156)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:787)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:38)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:227)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1659)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:340)
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1348)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 12 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at java.base/sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1701)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1519)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:456)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:427)
	at com.mysql.cj.protocol.ExportControlled.performTlsHandshake(ExportControlled.java:317)
	at com.mysql.cj.protocol.StandardSocketFactory.performTlsHandshake(StandardSocketFactory.java:188)
	at com.mysql.cj.protocol.a.NativeSocketConnection.performTlsHandshake(NativeSocketConnection.java:97)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:331)
	... 17 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1511)
	... 24 common frames omitted
10:07:35.681 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1616 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748484442063,"WAITING"]
11:30:49.311 [http-nio-8080-exec-15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3406 millis. select * from mini_user_info where user_id=?["1899027399878774784"]
11:30:49.346 [http-nio-8080-exec-16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3442 millis. select * from mini_user_info where user_id=?["1899027399878774784"]
11:51:29.927 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2153 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748490717133,1748490675766]
14:43:05.387 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1089 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748500984294,"LAPTOP-JHBER3JS1748479548103"]
14:46:49.970 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1141 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748501238558,1748501196827]
14:56:14.836 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2570 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748501800837,1748501760265]
14:59:37.156 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1165 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:04:11.514 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1651 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:09:17.237 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1023 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:09:17.237 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1025 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748502544212,"WAITING"]
15:11:18.798 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1390 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748502707047,1748502665408]
15:11:38.647 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1179 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748502697463,"LAPTOP-JHBER3JS1748479548103"]
15:11:43.864 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1654 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748502690209,"WAITING"]
15:12:41.526 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1103 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748502789456,1748502748423]
15:14:33.102 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1080 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:14:44.034 [Druid-ConnectionPool-Create-1871293699] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state null
java.sql.SQLException: validateConnection false
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1418)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1733)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: java.lang.reflect.InvocationTargetException: null
	at jdk.internal.reflect.GeneratedMethodAccessor419.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1398)
	... 2 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:666)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:134)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.ping(NativeSession.java:1151)
	at com.mysql.cj.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:1526)
	... 7 common frames omitted
15:14:45.957 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1203 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748502872753,"WAITING"]
15:14:50.491 [Druid-ConnectionPool-Create-1871293699] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: ********************************************************************************************************************************************************************************, errorCode 0, state null
java.sql.SQLException: validateConnection false
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1418)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1733)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: java.lang.reflect.InvocationTargetException: null
	at jdk.internal.reflect.GeneratedMethodAccessor419.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1398)
	... 2 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:666)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:134)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.ping(NativeSession.java:1151)
	at com.mysql.cj.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:1526)
	... 7 common frames omitted
15:14:51.628 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2083 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748502889545,"LAPTOP-JHBER3JS1748479548103"]
15:35:53.590 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748479548103_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1230 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748504140359,"WAITING"]
15:54:09.379 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1653 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748505277421,1748505235725]
