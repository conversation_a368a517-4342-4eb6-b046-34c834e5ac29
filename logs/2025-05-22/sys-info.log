14:48:28.718 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
14:48:28.761 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 40856 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
14:48:28.763 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
14:48:37.253 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:48:37.253 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:37.253 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
14:48:37.505 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:43.728 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
14:48:44.273 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
14:48:49.972 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
14:48:50.474 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:48:50.513 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:48:50.513 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:48:50.522 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1747896530477'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:48:50.523 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
14:48:50.523 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:48:50.526 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@d0538b3
14:48:59.861 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:48:59.913 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 32.03 seconds (JVM running for 42.365)
14:49:00.226 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:49:00.857 [http-nio-8080-exec-2] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
14:49:01.177 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1747896530477 started.
