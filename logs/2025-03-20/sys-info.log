10:53:58.901 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
10:53:58.930 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 38488 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by <PERSON><PERSON> in D:\wangoproject\zhyl\retirement_service_djf2)
10:53:58.931 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
10:54:02.738 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
10:54:02.739 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:54:02.739 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
10:54:02.988 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:54:07.724 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
10:54:08.203 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
10:54:12.172 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
10:54:12.419 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:54:12.432 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:54:12.432 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:54:12.439 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1742439252420'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:54:12.439 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
10:54:12.439 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:54:12.442 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5b8ba1d2
10:54:20.845 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
10:54:20.912 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 22.497 seconds (JVM running for 26.379)
10:54:22.098 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439252420 started.
10:54:48.966 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:57:10.048 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439252420 paused.
10:57:10.054 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439252420 shutting down.
10:57:10.054 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439252420 paused.
10:57:10.054 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439252420 shutdown complete.
10:57:10.054 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:57:10.058 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
10:57:10.063 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
10:57:16.213 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
10:57:16.227 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 36608 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
10:57:16.228 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
10:57:18.812 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
10:57:18.812 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:57:18.812 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
10:57:18.963 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:57:22.349 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
10:57:22.657 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
10:57:26.203 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
10:57:26.397 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:57:26.409 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:57:26.410 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:57:26.416 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1742439446398'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:57:26.416 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
10:57:26.416 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:57:26.417 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@58829c2c
10:57:32.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
10:57:32.129 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 16.182 seconds (JVM running for 17.62)
10:57:33.439 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1742439446398 started.
10:59:31.910 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:59:32.341 [http-nio-8080-exec-1] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
10:59:46.455 [http-nio-8080-exec-2] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:00:17.877 [http-nio-8080-exec-3] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:13:49.583 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
11:35:38.736 [http-nio-8080-exec-13] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:42:34.888 [http-nio-8080-exec-4] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
13:32:24.643 [Druid-ConnectionPool-Create-1578732608] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
13:32:43.309 [Druid-ConnectionPool-Create-1578732608] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
