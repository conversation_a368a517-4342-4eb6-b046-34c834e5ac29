14:49:54.456 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
14:49:54.493 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 24140 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
14:49:54.494 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
14:49:57.765 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
14:49:57.765 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:49:57.765 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
14:49:57.934 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:50:02.422 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
14:50:02.781 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
14:50:07.152 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
14:50:07.371 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:50:07.384 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:50:07.385 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:50:07.391 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748242207374'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:50:07.392 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
14:50:07.392 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:50:07.394 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@78c91d2a
14:50:14.730 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
14:50:14.761 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.672 seconds (JVM running for 23.179)
14:50:15.707 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:50:16.129 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748242207374 started.
14:50:21.413 [http-nio-8080-exec-2] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:23:12.704 [http-nio-8080-exec-11] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:38:30.460 [Druid-ConnectionPool-Create-859897724] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
15:38:36.734 [Druid-ConnectionPool-Create-859897724] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
15:50:19.656 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748242207374 paused.
15:50:19.702 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748242207374 shutting down.
15:50:19.704 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748242207374 paused.
15:50:20.830 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748242207374 shutdown complete.
15:50:20.833 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:50:20.849 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
15:50:20.860 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
15:50:30.470 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
15:50:30.506 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 38372 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
15:50:30.507 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
15:50:33.511 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
15:50:33.511 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:50:33.511 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
15:50:33.680 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:50:56.065 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
15:50:56.862 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
15:51:30.343 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
15:51:31.022 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:51:31.036 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:51:31.036 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:51:31.043 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748245891025'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:51:31.043 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
15:51:31.043 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:51:31.045 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5b7d5eca
15:52:09.780 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
15:52:09.810 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 99.648 seconds (JVM running for 101.265)
15:52:13.055 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748245891025 started.
15:52:36.145 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:55:14.965 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748245891025 paused.
15:55:14.972 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748245891025 shutting down.
15:55:14.972 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748245891025 paused.
15:55:16.575 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748245891025 shutdown complete.
15:55:16.575 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:55:16.580 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
15:55:16.584 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
15:56:17.625 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
15:56:17.646 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 5864 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
15:56:17.646 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
15:56:20.399 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
15:56:20.399 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:56:20.399 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
15:56:20.574 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:56:43.451 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
15:56:44.185 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
15:57:33.177 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
15:57:33.891 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:57:33.905 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:57:33.905 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:57:33.912 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748246253893'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:57:33.912 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
15:57:33.912 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:57:33.914 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@648bfebe
15:58:13.613 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
15:58:13.645 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 116.301 seconds (JVM running for 117.904)
15:58:17.704 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748246253893 started.
15:58:23.519 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:58:25.388 [http-nio-8080-exec-1] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
16:10:22.410 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748246253893 paused.
16:10:22.423 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748246253893 shutting down.
16:10:22.423 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748246253893 paused.
16:10:22.424 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748246253893 shutdown complete.
16:10:22.425 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
16:10:22.438 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
16:10:22.446 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
16:10:28.162 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
16:10:28.188 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 36856 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
16:10:28.189 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
16:10:31.599 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
16:10:31.599 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:10:31.600 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
16:10:31.828 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:10:43.963 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
16:10:44.422 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
16:10:55.945 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
16:10:56.249 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:10:56.263 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:10:56.263 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:10:56.271 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748247056252'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

16:10:56.271 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
16:10:56.271 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:10:56.273 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@2a49753
16:11:12.238 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
16:11:12.280 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 44.444 seconds (JVM running for 46.592)
16:11:14.264 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748247056252 started.
16:11:52.361 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:38:22.585 [http-nio-8080-exec-30] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
17:16:26.507 [http-nio-8080-exec-27] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
