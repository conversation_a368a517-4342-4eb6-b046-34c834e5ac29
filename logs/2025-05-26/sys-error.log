15:03:14.481 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1196 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748243022939,1748242981278]
15:08:38.132 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2786 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748243342301,1748243303346]
15:31:55.221 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1104 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748244743556,1748244702117]
15:32:50.322 [http-nio-8080-exec-2] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
15:36:39.020 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1371 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:36:40.163 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2511 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245026133,1748244985652]
15:36:40.163 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1138 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748244999024,"LAPTOP-JHBER3JS1748242207374"]
15:36:48.455 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 6942 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748244989510,"WAITING"]
15:37:00.149 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 4057 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:37:00.149 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 3591 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245004559,"WAITING"]
15:37:05.425 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 5274 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748245020149,"LAPTOP-JHBER3JS1748242207374"]
15:37:38.749 [Druid-ConnectionPool-Create-859897724] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: *****************************************************************************************************************************************************%2B8&allowMultiQueries=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:156)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:787)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:38)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:227)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:150)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1659)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:340)
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1348)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 12 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at java.base/sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1701)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1519)
	at java.base/sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1421)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:456)
	at java.base/sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:427)
	at com.mysql.cj.protocol.ExportControlled.performTlsHandshake(ExportControlled.java:317)
	at com.mysql.cj.protocol.StandardSocketFactory.performTlsHandshake(StandardSocketFactory.java:188)
	at com.mysql.cj.protocol.a.NativeSocketConnection.performTlsHandshake(NativeSocketConnection.java:97)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:331)
	... 17 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:489)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:160)
	at java.base/sun.security.ssl.SSLTransport.decode(SSLTransport.java:111)
	at java.base/sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1511)
	... 24 common frames omitted
15:37:43.346 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 21199 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245056927,1748245030146]
15:37:48.279 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 4933 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245051346,"WAITING"]
15:38:07.872 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2472 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:38:10.623 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2746 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748245087876,"LAPTOP-JHBER3JS1748242207374"]
15:38:23.755 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 13127 millis. INSERT INTO QRTZ_SCHEDULER_STATE (SCHED_NAME, INSTANCE_NAME, LAST_CHECKIN_TIME, CHECKIN_INTERVAL) VALUES('RuoyiScheduler', ?, ?, ?)["LAPTOP-JHBER3JS1748242207374",1748245087876,15000]
15:38:30.460 [Druid-ConnectionPool-Create-859897724] ERROR c.a.d.p.DruidDataSource - [run,2803] - create connection SQLException, url: *****************************************************************************************************************************************************%2B8&allowMultiQueries=true, errorCode 0, state null
java.sql.SQLException: validateConnection false
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1418)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1733)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2801)
Caused by: java.lang.reflect.InvocationTargetException: null
	at jdk.internal.reflect.GeneratedMethodAccessor385.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at com.alibaba.druid.pool.vendor.MySqlValidConnectionChecker.isValidConnection(MySqlValidConnectionChecker.java:107)
	at com.alibaba.druid.pool.DruidAbstractDataSource.validateConnection(DruidAbstractDataSource.java:1398)
	... 2 common frames omitted
Caused by: java.lang.NullPointerException: null
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:666)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:134)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.ping(NativeSession.java:1151)
	at com.mysql.cj.jdbc.ConnectionImpl.pingInternal(ConnectionImpl.java:1526)
	... 7 common frames omitted
15:38:50.438 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1620 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245158376,1748245116818]
15:38:57.995 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1077 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245124917,"WAITING"]
15:38:59.351 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1361 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748245137990,"LAPTOP-JHBER3JS1748242207374"]
15:39:20.926 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1085 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245147840,"WAITING"]
15:40:33.323 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1078 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:43:00.087 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1342 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245407852,1748245366740]
15:43:58.591 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1625 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245466435,1748245424965]
15:44:56.958 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1077 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245483880,"WAITING"]
15:45:33.954 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1071 millis. SELECT * FROM QRTZ_SCHEDULER_STATE WHERE SCHED_NAME = 'RuoyiScheduler'[]
15:46:23.579 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2482 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748245569097,"WAITING"]
15:46:23.579 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 6330 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748245606554,1748245565249]
15:47:05.542 [http-nio-8080-exec-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1332 millis. select * from device_store
         WHERE is_available = 1[]
15:49:40.125 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748242207374_ClusterManager] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1666 millis. UPDATE QRTZ_SCHEDULER_STATE SET LAST_CHECKIN_TIME = ? WHERE SCHED_NAME = 'RuoyiScheduler' AND INSTANCE_NAME = ?[1748245778458,"LAPTOP-JHBER3JS1748242207374"]
15:51:04.552 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1364 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["咸宁市","421200","city",null,"420000",null,null,"随州市","421300","city",null,"420000",null,null,"恩施土家族苗族自治州","422800","city",null,"420000",null,null,"仙桃市","429004","city",null,"420000",null,null,"潜江市","429005","city",null,"420000",null,null,"天门市","429006","city",null,"420000",null,null,"神农架林区","429021","city",null,"420000",null,null,"沈阳市","210100","city",null,"210000",null,null,"大连市","210200","city",null,"210000",null,null,"鞍山市","210300","city",null,"210000",null,null,"抚顺市","210400","city",null,"210000",null,null,"本溪市","210500","city",null,"210000",null,null,"丹东市","210600","city",null,"210000",null,null,"锦州市","210700","city",null,"210000",null,null,"营口市","210800","city",null,"210000",null,null,"阜新市","210900","city",null,"210000",null,null,"辽阳市","211000","city",null,"210000",null,null,"盘锦市","211100","city",null,"210000",null,null,"铁岭市","211200","city",null,"210000",null,null,"朝阳市","211300","city",null,"210000",null,null,"葫芦岛市","211400","city",null,"210000",null,null,"济南市","370100","city",null,"370000",null,null,"青岛市","370200","city",null,"370000",null,null,"淄博市","370300","city",null,"370000",null,null,"枣庄市","370400","city",null,"370000",null,null,"东营市","370500","city",null,"370000",null,null,"烟台市","370600","city",null,"370000",null,null,"潍坊市","370700","city",null,"370000",null,null,"济宁市","370800","city",null,"370000",null,null,"泰安市","370900","city",null,"370000",null,null,"威海市","371000","city",null,"370000",null,null,"日照市","371100","city",null,"370000",null,null,"临沂市","371300","city",null,"370000",null,null,"德州市","371400","city",null,"370000",null,null,"聊城市","371500","city",null,"370000",null,null,"滨州市","371600","city",null,"370000",null,null,"菏泽市","371700","city",null,"370000",null,null,"上海城区","310100","city",null,"310000",null,null,"贵阳市","520100","city",null,"520000",null,null,"六盘水市","520200","city",null,"520000",null,null,"遵义市","520300","city",null,"520000",null,null,"安顺市","520400","city",null,"520000",null,null,"毕节市","520500","city",null,"520000",null,null,"铜仁市","520600","city",null,"520000",null,null,"黔西南布依族苗族自治州","522300","city",null,"520000",null,null,"黔东南苗族侗族自治州","522600","city",null,"520000",null,null,"黔南布依族苗族自治州","522700","city",null,"520000",null,null,"重庆城区","500100","city",null,"500000",null,null,"重庆郊县","500200","city",null,"500000",null,null,"合肥市","340100","city",null,"340000",null,null,"芜湖市","340200","city",null,"340000",null,null,"蚌埠市","340300","city",null,"340000",null,null,"淮南市","340400","city",null,"340000",null,null,"马鞍山市","340500","city",null,"340000",null,null,"淮北市","340600","city",null,"340000",null,null,"铜陵市","340700","city",null,"340000",null,null,"安庆市","340800","city",null,"340000",null,null,"黄山市","341000","city",null,"340000",null,null,"滁州市","341100","city",null,"340000",null,null,"阜阳市","341200","city",null,"340000",null,null,"宿州市","341300","city",null,"340000",null,null,"六安市","341500","city",null,"340000",null,null,"亳州市","341600","city",null,"340000",null,null,"池州市","341700","city",null,"340000",null,null,"宣城市","341800","city",null,"340000",null,null,"福州市","350100","city",null,"350000",null,null,"厦门市","350200","city",null,"350000",null,null,"莆田市","350300","city",null,"350000",null,null,"三明市","350400","city",null,"350000",null,null,"泉州市","350500","city",null,"350000",null,null,"漳州市","350600","city",null,"350000",null,null,"南平市","350700","city",null,"350000",null,null,"龙岩市","350800","city",null,"350000",null,null,"宁德市","350900","city",null,"350000",null,null,"长沙市","430100","city",null,"430000",null,null,"株洲市","430200","city",null,"430000",null,null,"湘潭市","430300","city",null,"430000",null,null,"衡阳市","430400","city",null,"430000",null,null,"邵阳市","430500","city",null,"430000",null,null,"岳阳市","430600","city",null,"430000",null,null,"常德市","430700","city",null,"430000",null,null,"张家界市","430800","city",null,"430000",null,null,"益阳市","430900","city",null,"430000",null,null,"郴州市","431000","city",null,"430000",null,null,"永州市","431100","city",null,"430000",null,null,"怀化市","431200","city",null,"430000",null,null,"娄底市","431300","city",null,"430000",null,null,"湘西土家族苗族自治州","433100","city",null,"430000",null,null,"海口市","460100","city",null,"460000",null,null,"三亚市","460200","city",null,"460000",null,null,"三沙市","460300","city",null,"460000",null,null,"儋州市","460400","city",null,"460000",null,null,"五指山市","469001","city",null,"460000",null,null,"琼海市","469002","city",null,"460000",null,null,"文昌市","469005","city",null,"460000",null,null,"万宁市","469006","city",null,"460000",null,null,"东方市","469007","city",null,"460000",null,null,"定安县","469021","city",null,"460000",null,null,"屯昌县","469022","city",null,"460000",null,null,"澄迈县","469023","city",null,"460000",null,null]
15:51:06.483 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1113 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["武威市","620600","city",null,"620000",null,null,"张掖市","620700","city",null,"620000",null,null,"平凉市","620800","city",null,"620000",null,null,"酒泉市","620900","city",null,"620000",null,null,"庆阳市","621000","city",null,"620000",null,null,"定西市","621100","city",null,"620000",null,null,"陇南市","621200","city",null,"620000",null,null,"临夏回族自治州","622900","city",null,"620000",null,null,"甘南藏族自治州","623000","city",null,"620000",null,null,"成都市","510100","city",null,"510000",null,null,"自贡市","510300","city",null,"510000",null,null,"攀枝花市","510400","city",null,"510000",null,null,"泸州市","510500","city",null,"510000",null,null,"德阳市","510600","city",null,"510000",null,null,"绵阳市","510700","city",null,"510000",null,null,"广元市","510800","city",null,"510000",null,null,"遂宁市","510900","city",null,"510000",null,null,"内江市","511000","city",null,"510000",null,null,"乐山市","511100","city",null,"510000",null,null,"南充市","511300","city",null,"510000",null,null,"眉山市","511400","city",null,"510000",null,null,"宜宾市","511500","city",null,"510000",null,null,"广安市","511600","city",null,"510000",null,null,"达州市","511700","city",null,"510000",null,null,"雅安市","511800","city",null,"510000",null,null,"巴中市","511900","city",null,"510000",null,null,"资阳市","512000","city",null,"510000",null,null,"阿坝藏族羌族自治州","513200","city",null,"510000",null,null,"甘孜藏族自治州","513300","city",null,"510000",null,null,"凉山彝族自治州","513400","city",null,"510000",null,null,"天津城区","120100","city",null,"120000",null,null,"北京城区","110100","city",null,"110000",null,null,"拉萨市","540100","city",null,"540000",null,null,"日喀则市","540200","city",null,"540000",null,null,"昌都市","540300","city",null,"540000",null,null,"林芝市","540400","city",null,"540000",null,null,"山南市","540500","city",null,"540000",null,null,"那曲市","540600","city",null,"540000",null,null,"阿里地区","542500","city",null,"540000",null,null,"长春市","220100","city",null,"220000",null,null,"吉林市","220200","city",null,"220000",null,null,"四平市","220300","city",null,"220000",null,null,"辽源市","220400","city",null,"220000",null,null,"通化市","220500","city",null,"220000",null,null,"白山市","220600","city",null,"220000",null,null,"松原市","220700","city",null,"220000",null,null,"白城市","220800","city",null,"220000",null,null,"延边朝鲜族自治州","222400","city",null,"220000",null,null,"太原市","140100","city",null,"140000",null,null,"大同市","140200","city",null,"140000",null,null,"阳泉市","140300","city",null,"140000",null,null,"长治市","140400","city",null,"140000",null,null,"晋城市","140500","city",null,"140000",null,null,"朔州市","140600","city",null,"140000",null,null,"晋中市","140700","city",null,"140000",null,null,"运城市","140800","city",null,"140000",null,null,"忻州市","140900","city",null,"140000",null,null,"临汾市","141000","city",null,"140000",null,null,"吕梁市","141100","city",null,"140000",null,null,"南昌市","360100","city",null,"360000",null,null,"景德镇市","360200","city",null,"360000",null,null,"萍乡市","360300","city",null,"360000",null,null,"九江市","360400","city",null,"360000",null,null,"新余市","360500","city",null,"360000",null,null,"鹰潭市","360600","city",null,"360000",null,null,"赣州市","360700","city",null,"360000",null,null,"吉安市","360800","city",null,"360000",null,null,"宜春市","360900","city",null,"360000",null,null,"抚州市","361000","city",null,"360000",null,null,"上饶市","361100","city",null,"360000",null,null,"西安市","610100","city",null,"610000",null,null,"铜川市","610200","city",null,"610000",null,null,"宝鸡市","610300","city",null,"610000",null,null,"咸阳市","610400","city",null,"610000",null,null,"渭南市","610500","city",null,"610000",null,null,"延安市","610600","city",null,"610000",null,null,"汉中市","610700","city",null,"610000",null,null,"榆林市","610800","city",null,"610000",null,null,"安康市","610900","city",null,"610000",null,null,"商洛市","611000","city",null,"610000",null,null,"昆明市","530100","city",null,"530000",null,null,"曲靖市","530300","city",null,"530000",null,null,"玉溪市","530400","city",null,"530000",null,null,"保山市","530500","city",null,"530000",null,null,"昭通市","530600","city",null,"530000",null,null,"丽江市","530700","city",null,"530000",null,null,"普洱市","530800","city",null,"530000",null,null,"临沧市","530900","city",null,"530000",null,null,"楚雄彝族自治州","532300","city",null,"530000",null,null,"红河哈尼族彝族自治州","532500","city",null,"530000",null,null,"文山壮族苗族自治州","532600","city",null,"530000",null,null,"西双版纳傣族自治州","532800","city",null,"530000",null,null,"大理白族自治州","532900","city",null,"530000",null,null,"德宏傣族景颇族自治州","533100","city",null,"530000",null,null,"怒江傈僳族自治州","533300","city",null,"530000",null,null,"迪庆藏族自治州","533400","city",null,"530000",null,null]
15:51:07.859 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1367 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["老城区","410302","district",null,"410000","410300",null,"西工区","410303","district",null,"410000","410300",null,"瀍河回族区","410304","district",null,"410000","410300",null,"涧西区","410305","district",null,"410000","410300",null,"偃师区","410307","district",null,"410000","410300",null,"孟津区","410308","district",null,"410000","410300",null,"洛龙区","410311","district",null,"410000","410300",null,"新安县","410323","district",null,"410000","410300",null,"栾川县","410324","district",null,"410000","410300",null,"嵩县","410325","district",null,"410000","410300",null,"汝阳县","410326","district",null,"410000","410300",null,"宜阳县","410327","district",null,"410000","410300",null,"洛宁县","410328","district",null,"410000","410300",null,"伊川县","410329","district",null,"410000","410300",null,"源汇区","411102","district",null,"410000","411100",null,"郾城区","411103","district",null,"410000","411100",null,"召陵区","411104","district",null,"410000","411100",null,"舞阳县","411121","district",null,"410000","411100",null,"临颍县","411122","district",null,"410000","411100",null,"宛城区","411302","district",null,"410000","411300",null,"卧龙区","411303","district",null,"410000","411300",null,"南召县","411321","district",null,"410000","411300",null,"方城县","411322","district",null,"410000","411300",null,"西峡县","411323","district",null,"410000","411300",null,"镇平县","411324","district",null,"410000","411300",null,"内乡县","411325","district",null,"410000","411300",null,"淅川县","411326","district",null,"410000","411300",null,"社旗县","411327","district",null,"410000","411300",null,"唐河县","411328","district",null,"410000","411300",null,"新野县","411329","district",null,"410000","411300",null,"桐柏县","411330","district",null,"410000","411300",null,"邓州市","411381","district",null,"410000","411300",null,"浉河区","411502","district",null,"410000","411500",null,"平桥区","411503","district",null,"410000","411500",null,"罗山县","411521","district",null,"410000","411500",null,"光山县","411522","district",null,"410000","411500",null,"新县","411523","district",null,"410000","411500",null,"商城县","411524","district",null,"410000","411500",null,"固始县","411525","district",null,"410000","411500",null,"潢川县","411526","district",null,"410000","411500",null,"淮滨县","411527","district",null,"410000","411500",null,"息县","411528","district",null,"410000","411500",null,"华龙区","410902","district",null,"410000","410900",null,"清丰县","410922","district",null,"410000","410900",null,"南乐县","410923","district",null,"410000","410900",null,"范县","410926","district",null,"410000","410900",null,"台前县","410927","district",null,"410000","410900",null,"濮阳县","410928","district",null,"410000","410900",null,"中原区","410102","district",null,"410000","410100",null,"二七区","410103","district",null,"410000","410100",null,"管城回族区","410104","district",null,"410000","410100",null,"金水区","410105","district",null,"410000","410100",null,"上街区","410106","district",null,"410000","410100",null,"惠济区","410108","district",null,"410000","410100",null,"中牟县","410122","district",null,"410000","410100",null,"巩义市","410181","district",null,"410000","410100",null,"荥阳市","410182","district",null,"410000","410100",null,"新密市","410183","district",null,"410000","410100",null,"新郑市","410184","district",null,"410000","410100",null,"登封市","410185","district",null,"410000","410100",null,"川汇区","411602","district",null,"410000","411600",null,"淮阳区","411603","district",null,"410000","411600",null,"扶沟县","411621","district",null,"410000","411600",null,"西华县","411622","district",null,"410000","411600",null,"商水县","411623","district",null,"410000","411600",null,"沈丘县","411624","district",null,"410000","411600",null,"郸城县","411625","district",null,"410000","411600",null,"太康县","411627","district",null,"410000","411600",null,"鹿邑县","411628","district",null,"410000","411600",null,"项城市","411681","district",null,"410000","411600",null,"驿城区","411702","district",null,"410000","411700",null,"西平县","411721","district",null,"410000","411700",null,"上蔡县","411722","district",null,"410000","411700",null,"平舆县","411723","district",null,"410000","411700",null,"正阳县","411724","district",null,"410000","411700",null,"确山县","411725","district",null,"410000","411700",null,"泌阳县","411726","district",null,"410000","411700",null,"汝南县","411727","district",null,"410000","411700",null,"遂平县","411728","district",null,"410000","411700",null,"新蔡县","411729","district",null,"410000","411700",null,"新华区","410402","district",null,"410000","410400",null,"卫东区","410403","district",null,"410000","410400",null,"石龙区","410404","district",null,"410000","410400",null,"湛河区","410411","district",null,"410000","410400",null,"宝丰县","410421","district",null,"410000","410400",null,"叶县","410422","district",null,"410000","410400",null,"鲁山县","410423","district",null,"410000","410400",null,"郏县","410425","district",null,"410000","410400",null,"舞钢市","410481","district",null,"410000","410400",null,"汝州市","410482","district",null,"410000","410400",null,"湖滨区","411202","district",null,"410000","411200",null,"陕州区","411203","district",null,"410000","411200",null,"渑池县","411221","district",null,"410000","411200",null,"卢氏县","411224","district",null,"410000","411200",null,"义马市","411281","district",null,"410000","411200",null,"灵宝市","411282","district",null,"410000","411200",null,"坡头镇","419001","street",null,"410000","419001",null,"梨林镇","419001","street",null,"410000","419001",null,"思礼镇","419001","street",null,"410000","419001",null,"五龙口镇","419001","street",null,"410000","419001",null]
15:51:08.969 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1102 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["王屋镇","419001","street",null,"410000","419001",null,"玉泉街道","419001","street",null,"410000","419001",null,"轵城镇","419001","street",null,"410000","419001",null,"济水街道","419001","street",null,"410000","419001",null,"沁园街道","419001","street",null,"410000","419001",null,"下冶镇","419001","street",null,"410000","419001",null,"克井镇","419001","street",null,"410000","419001",null,"天坛街道","419001","street",null,"410000","419001",null,"邵原镇","419001","street",null,"410000","419001",null,"北海街道","419001","street",null,"410000","419001",null,"承留镇","419001","street",null,"410000","419001",null,"大峪镇","419001","street",null,"410000","419001",null,"红旗区","410702","district",null,"410000","410700",null,"卫滨区","410703","district",null,"410000","410700",null,"凤泉区","410704","district",null,"410000","410700",null,"牧野区","410711","district",null,"410000","410700",null,"新乡县","410721","district",null,"410000","410700",null,"获嘉县","410724","district",null,"410000","410700",null,"原阳县","410725","district",null,"410000","410700",null,"延津县","410726","district",null,"410000","410700",null,"封丘县","410727","district",null,"410000","410700",null,"卫辉市","410781","district",null,"410000","410700",null,"辉县市","410782","district",null,"410000","410700",null,"长垣市","410783","district",null,"410000","410700",null,"解放区","410802","district",null,"410000","410800",null,"中站区","410803","district",null,"410000","410800",null,"马村区","410804","district",null,"410000","410800",null,"山阳区","410811","district",null,"410000","410800",null,"修武县","410821","district",null,"410000","410800",null,"博爱县","410822","district",null,"410000","410800",null,"武陟县","410823","district",null,"410000","410800",null,"温县","410825","district",null,"410000","410800",null,"沁阳市","410882","district",null,"410000","410800",null,"孟州市","410883","district",null,"410000","410800",null,"文峰区","410502","district",null,"410000","410500",null,"北关区","410503","district",null,"410000","410500",null,"殷都区","410505","district",null,"410000","410500",null,"龙安区","410506","district",null,"410000","410500",null,"安阳县","410522","district",null,"410000","410500",null,"汤阴县","410523","district",null,"410000","410500",null,"滑县","410526","district",null,"410000","410500",null,"内黄县","410527","district",null,"410000","410500",null,"林州市","410581","district",null,"410000","410500",null,"鹤山区","410602","district",null,"410000","410600",null,"山城区","410603","district",null,"410000","410600",null,"淇滨区","410611","district",null,"410000","410600",null,"浚县","410621","district",null,"410000","410600",null,"淇县","410622","district",null,"410000","410600",null,"魏都区","411002","district",null,"410000","411000",null,"建安区","411003","district",null,"410000","411000",null,"鄢陵县","411024","district",null,"410000","411000",null,"襄城县","411025","district",null,"410000","411000",null,"禹州市","411081","district",null,"410000","411000",null,"长葛市","411082","district",null,"410000","411000",null,"梁园区","411402","district",null,"410000","411400",null,"睢阳区","411403","district",null,"410000","411400",null,"民权县","411421","district",null,"410000","411400",null,"睢县","411422","district",null,"410000","411400",null,"宁陵县","411423","district",null,"410000","411400",null,"柘城县","411424","district",null,"410000","411400",null,"虞城县","411425","district",null,"410000","411400",null,"夏邑县","411426","district",null,"410000","411400",null,"永城市","411481","district",null,"410000","411400",null,"龙亭区","410202","district",null,"410000","410200",null,"顺河回族区","410203","district",null,"410000","410200",null,"鼓楼区","410204","district",null,"410000","410200",null,"禹王台区","410205","district",null,"410000","410200",null,"祥符区","410212","district",null,"410000","410200",null,"杞县","410221","district",null,"410000","410200",null,"通许县","410222","district",null,"410000","410200",null,"尉氏县","410223","district",null,"410000","410200",null,"兰考县","410225","district",null,"410000","410200",null,"龙湖区","440507","district",null,"440000","440500",null,"金平区","440511","district",null,"440000","440500",null,"濠江区","440512","district",null,"440000","440500",null,"潮阳区","440513","district",null,"440000","440500",null,"潮南区","440514","district",null,"440000","440500",null,"澄海区","440515","district",null,"440000","440500",null,"南澳县","440523","district",null,"440000","440500",null,"禅城区","440604","district",null,"440000","440600",null,"南海区","440605","district",null,"440000","440600",null,"顺德区","440606","district",null,"440000","440600",null,"三水区","440607","district",null,"440000","440600",null,"高明区","440608","district",null,"440000","440600",null,"端州区","441202","district",null,"440000","441200",null,"鼎湖区","441203","district",null,"440000","441200",null,"高要区","441204","district",null,"440000","441200",null,"广宁县","441223","district",null,"440000","441200",null,"怀集县","441224","district",null,"440000","441200",null,"封开县","441225","district",null,"440000","441200",null,"德庆县","441226","district",null,"440000","441200",null,"四会市","441284","district",null,"440000","441200",null,"惠城区","441302","district",null,"440000","441300",null,"惠阳区","441303","district",null,"440000","441300",null,"博罗县","441322","district",null,"440000","441300",null,"惠东县","441323","district",null,"440000","441300",null,"龙门县","441324","district",null,"440000","441300",null,"罗湖区","440303","district",null,"440000","440300",null,"福田区","440304","district",null,"440000","440300",null,"南山区","440305","district",null,"440000","440300",null]
15:51:12.545 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1372 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["宝山区","230506","district",null,"230000","230500",null,"集贤县","230521","district",null,"230000","230500",null,"友谊县","230522","district",null,"230000","230500",null,"宝清县","230523","district",null,"230000","230500",null,"饶河县","230524","district",null,"230000","230500",null,"爱辉区","231102","district",null,"230000","231100",null,"逊克县","231123","district",null,"230000","231100",null,"孙吴县","231124","district",null,"230000","231100",null,"北安市","231181","district",null,"230000","231100",null,"五大连池市","231182","district",null,"230000","231100",null,"嫩江市","231183","district",null,"230000","231100",null,"东安区","231002","district",null,"230000","231000",null,"阳明区","231003","district",null,"230000","231000",null,"爱民区","231004","district",null,"230000","231000",null,"西安区","231005","district",null,"230000","231000",null,"林口县","231025","district",null,"230000","231000",null,"绥芬河市","231081","district",null,"230000","231000",null,"海林市","231083","district",null,"230000","231000",null,"宁安市","231084","district",null,"230000","231000",null,"穆棱市","231085","district",null,"230000","231000",null,"东宁市","231086","district",null,"230000","231000",null,"伊美区","230717","district",null,"230000","230700",null,"乌翠区","230718","district",null,"230000","230700",null,"友好区","230719","district",null,"230000","230700",null,"嘉荫县","230722","district",null,"230000","230700",null,"汤旺县","230723","district",null,"230000","230700",null,"丰林县","230724","district",null,"230000","230700",null,"大箐山县","230725","district",null,"230000","230700",null,"南岔县","230726","district",null,"230000","230700",null,"金林区","230751","district",null,"230000","230700",null,"铁力市","230781","district",null,"230000","230700",null,"鸡冠区","230302","district",null,"230000","230300",null,"恒山区","230303","district",null,"230000","230300",null,"滴道区","230304","district",null,"230000","230300",null,"梨树区","230305","district",null,"230000","230300",null,"城子河区","230306","district",null,"230000","230300",null,"麻山区","230307","district",null,"230000","230300",null,"鸡东县","230321","district",null,"230000","230300",null,"虎林市","230381","district",null,"230000","230300",null,"密山市","230382","district",null,"230000","230300",null,"龙沙区","230202","district",null,"230000","230200",null,"建华区","230203","district",null,"230000","230200",null,"铁锋区","230204","district",null,"230000","230200",null,"昂昂溪区","230205","district",null,"230000","230200",null,"富拉尔基区","230206","district",null,"230000","230200",null,"碾子山区","230207","district",null,"230000","230200",null,"梅里斯达斡尔族区","230208","district",null,"230000","230200",null,"龙江县","230221","district",null,"230000","230200",null,"依安县","230223","district",null,"230000","230200",null,"泰来县","230224","district",null,"230000","230200",null,"甘南县","230225","district",null,"230000","230200",null,"富裕县","230227","district",null,"230000","230200",null,"克山县","230229","district",null,"230000","230200",null,"克东县","230230","district",null,"230000","230200",null,"拜泉县","230231","district",null,"230000","230200",null,"讷河市","230281","district",null,"230000","230200",null,"北林区","231202","district",null,"230000","231200",null,"望奎县","231221","district",null,"230000","231200",null,"兰西县","231222","district",null,"230000","231200",null,"青冈县","231223","district",null,"230000","231200",null,"庆安县","231224","district",null,"230000","231200",null,"明水县","231225","district",null,"230000","231200",null,"绥棱县","231226","district",null,"230000","231200",null,"安达市","231281","district",null,"230000","231200",null,"肇东市","231282","district",null,"230000","231200",null,"海伦市","231283","district",null,"230000","231200",null,"萨尔图区","230602","district",null,"230000","230600",null,"龙凤区","230603","district",null,"230000","230600",null,"让胡路区","230604","district",null,"230000","230600",null,"红岗区","230605","district",null,"230000","230600",null,"大同区","230606","district",null,"230000","230600",null,"肇州县","230621","district",null,"230000","230600",null,"肇源县","230622","district",null,"230000","230600",null,"林甸县","230623","district",null,"230000","230600",null,"杜尔伯特蒙古族自治县","230624","district",null,"230000","230600",null,"海川镇","659005","street",null,"650000","659005",null,"丰庆镇","659005","street",null,"650000","659005",null,"双渠镇","659005","street",null,"650000","659005",null,"北屯镇","659005","street",null,"650000","659005",null,"米兰镇","659006","street",null,"650000","659006",null,"南屯镇","659006","street",null,"650000","659006",null,"天湖镇","659006","street",null,"650000","659006",null,"开泽镇","659006","street",null,"650000","659006",null,"高桥镇","659006","street",null,"650000","659006",null,"博古其镇","659006","street",null,"650000","659006",null,"双丰镇","659006","street",null,"650000","659006",null,"河畔镇","659006","street",null,"650000","659006",null,"金山镇","659006","street",null,"650000","659006",null,"兵团八十九团","659007","street",null,"650000","659007",null,"博河镇","659007","street",null,"650000","659007",null,"双桥镇","659007","street",null,"650000","659007",null,"石峪镇","659007","street",null,"650000","659007",null,"双乐镇","659007","street",null,"650000","659007",null,"博乐市","652701","district",null,"650000","652700",null,"阿拉山口市","652702","district",null,"650000","652700",null,"精河县","652722","district",null,"650000","652700",null,"温泉县","652723","district",null,"650000","652700",null,"长丰镇","659008","street",null,"650000","659008",null,"金屯镇","659008","street",null,"650000","659008",null,"都拉塔口岸","659008","street",null,"650000","659008",null]
15:51:15.016 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1071 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["英山县","421124","district",null,"420000","421100",null,"浠水县","421125","district",null,"420000","421100",null,"蕲春县","421126","district",null,"420000","421100",null,"黄梅县","421127","district",null,"420000","421100",null,"麻城市","421181","district",null,"420000","421100",null,"武穴市","421182","district",null,"420000","421100",null,"胡市镇","429006","street",null,"420000","429006",null,"多祥镇","429006","street",null,"420000","429006",null,"黄潭镇","429006","street",null,"420000","429006",null,"沉湖管委会","429006","street",null,"420000","429006",null,"干驿镇","429006","street",null,"420000","429006",null,"横林镇","429006","street",null,"420000","429006",null,"马湾镇","429006","street",null,"420000","429006",null,"蒋湖农场","429006","street",null,"420000","429006",null,"小板镇","429006","street",null,"420000","429006",null,"岳口镇","429006","street",null,"420000","429006",null,"蒋场镇","429006","street",null,"420000","429006",null,"石家河镇","429006","street",null,"420000","429006",null,"彭市镇","429006","street",null,"420000","429006",null,"佛子山镇","429006","street",null,"420000","429006",null,"九真镇","429006","street",null,"420000","429006",null,"竟陵街道","429006","street",null,"420000","429006",null,"侯口街道","429006","street",null,"420000","429006",null,"麻洋镇","429006","street",null,"420000","429006",null,"杨林街道","429006","street",null,"420000","429006",null,"汪场镇","429006","street",null,"420000","429006",null,"白茅湖农场","429006","street",null,"420000","429006",null,"皂市镇","429006","street",null,"420000","429006",null,"拖市镇","429006","street",null,"420000","429006",null,"净潭乡","429006","street",null,"420000","429006",null,"卢市镇","429006","street",null,"420000","429006",null,"渔薪镇","429006","street",null,"420000","429006",null,"张港镇","429006","street",null,"420000","429006",null,"多宝镇","429006","street",null,"420000","429006",null,"孝南区","420902","district",null,"420000","420900",null,"孝昌县","420921","district",null,"420000","420900",null,"大悟县","420922","district",null,"420000","420900",null,"云梦县","420923","district",null,"420000","420900",null,"应城市","420981","district",null,"420000","420900",null,"安陆市","420982","district",null,"420000","420900",null,"汉川市","420984","district",null,"420000","420900",null,"东宝区","420802","district",null,"420000","420800",null,"掇刀区","420804","district",null,"420000","420800",null,"沙洋县","420822","district",null,"420000","420800",null,"钟祥市","420881","district",null,"420000","420800",null,"京山市","420882","district",null,"420000","420800",null,"泰丰街道","429005","street",null,"420000","429005",null,"广华寺街道","429005","street",null,"420000","429005",null,"周矶管理区","429005","street",null,"420000","429005",null,"高场街道","429005","street",null,"420000","429005",null,"总口管理区","429005","street",null,"420000","429005",null,"运粮湖管理区","429005","street",null,"420000","429005",null,"园林街道","429005","street",null,"420000","429005",null,"白鹭湖管理区","429005","street",null,"420000","429005",null,"竹根滩镇","429005","street",null,"420000","429005",null,"渔洋镇","429005","street",null,"420000","429005",null,"熊口镇","429005","street",null,"420000","429005",null,"熊口管理区","429005","street",null,"420000","429005",null,"后湖管理区","429005","street",null,"420000","429005",null,"杨市街道","429005","street",null,"420000","429005",null,"高石碑镇","429005","street",null,"420000","429005",null,"周矶街道","429005","street",null,"420000","429005",null,"泽口街道","429005","street",null,"420000","429005",null,"老新镇","429005","street",null,"420000","429005",null,"龙湾镇","429005","street",null,"420000","429005",null,"张金镇","429005","street",null,"420000","429005",null,"积玉口镇","429005","street",null,"420000","429005",null,"浩口镇","429005","street",null,"420000","429005",null,"王场镇","429005","street",null,"420000","429005",null,"通海口镇","429004","street",null,"420000","429004",null,"胡场镇","429004","street",null,"420000","429004",null,"干河街道","429004","street",null,"420000","429004",null,"杨林尾镇","429004","street",null,"420000","429004",null,"彭场镇","429004","street",null,"420000","429004",null,"沔城回族镇","429004","street",null,"420000","429004",null,"龙华山街道","429004","street",null,"420000","429004",null,"沙湖原种场","429004","street",null,"420000","429004",null,"郑场镇","429004","street",null,"420000","429004",null,"郭河镇","429004","street",null,"420000","429004",null,"排湖风景区","429004","street",null,"420000","429004",null,"沙嘴街道","429004","street",null,"420000","429004",null,"毛嘴镇","429004","street",null,"420000","429004",null,"张沟镇","429004","street",null,"420000","429004",null,"三伏潭镇","429004","street",null,"420000","429004",null,"西流河镇","429004","street",null,"420000","429004",null,"五湖渔场","429004","street",null,"420000","429004",null,"长埫口镇","429004","street",null,"420000","429004",null,"杜湖街道","429004","street",null,"420000","429004",null,"陈场镇","429004","street",null,"420000","429004",null,"剅河镇","429004","street",null,"420000","429004",null,"沙湖镇","429004","street",null,"420000","429004",null,"恩施市","422801","district",null,"420000","422800",null,"利川市","422802","district",null,"420000","422800",null,"建始县","422822","district",null,"420000","422800",null,"巴东县","422823","district",null,"420000","422800",null,"宣恩县","422825","district",null,"420000","422800",null,"咸丰县","422826","district",null,"420000","422800",null,"来凤县","422827","district",null,"420000","422800",null,"鹤峰县","422828","district",null,"420000","422800",null,"沙市区","421002","district",null,"420000","421000",null]
15:51:41.036 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1111 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND JOB_NAME = ? AND JOB_GROUP = ?["TASK_CLASS_NAME1","DEFAULT"]
15:52:36.388 [http-nio-8080-exec-1] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - nested exception is org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy128.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy184.listCategroy(Unknown Source)
	at com.wanou.project.system.service.impl.DeviceStoreServiceImpl.queryDeviceByCategory(DeviceStoreServiceImpl.java:103)
	at com.wanou.project.system.controller.DevicestoreController.queryDeviceByCategory(DevicestoreController.java:92)
	at com.wanou.project.system.controller.DevicestoreController$$FastClassBySpringCGLIB$$e457c8e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.wanou.project.system.controller.DevicestoreController$$EnhancerBySpringCGLIB$$fe0b2e9a.queryDeviceByCategory(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:89)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:94)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:64)
	at org.apache.ibatis.executor.ReuseExecutor.prepareStatement(ReuseExecutor.java:92)
	at org.apache.ibatis.executor.ReuseExecutor.doQuery(ReuseExecutor.java:59)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:111)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy248.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at jdk.internal.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 120 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:75)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:87)
	... 136 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.StringTypeHandler.setNonNullParameter(StringTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:73)
	... 137 common frames omitted
15:55:16.582 [http-nio-8080-exec-2] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - nested exception is org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:96)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy128.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy184.listCategroy(Unknown Source)
	at com.wanou.project.system.service.impl.DeviceStoreServiceImpl.queryDeviceByCategory(DeviceStoreServiceImpl.java:103)
	at com.wanou.project.system.controller.DevicestoreController.queryDeviceByCategory(DevicestoreController.java:92)
	at com.wanou.project.system.controller.DevicestoreController$$FastClassBySpringCGLIB$$e457c8e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.wanou.project.system.controller.DevicestoreController$$EnhancerBySpringCGLIB$$fe0b2e9a.queryDeviceByCategory(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='category', mode=IN, javaType=class java.lang.String, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:89)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.parameterize(PreparedStatementHandler.java:94)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.parameterize(RoutingStatementHandler.java:64)
	at org.apache.ibatis.executor.ReuseExecutor.prepareStatement(ReuseExecutor.java:92)
	at org.apache.ibatis.executor.ReuseExecutor.doQuery(ReuseExecutor.java:59)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:111)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy248.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at jdk.internal.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 120 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Error setting non null for parameter #1 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:75)
	at org.apache.ibatis.scripting.defaults.DefaultParameterHandler.setParameters(DefaultParameterHandler.java:87)
	... 136 common frames omitted
Caused by: java.lang.ClassCastException: class java.lang.Integer cannot be cast to class java.lang.String (java.lang.Integer and java.lang.String are in module java.base of loader 'bootstrap')
	at org.apache.ibatis.type.StringTypeHandler.setNonNullParameter(StringTypeHandler.java:26)
	at org.apache.ibatis.type.BaseTypeHandler.setParameter(BaseTypeHandler.java:73)
	... 137 common frames omitted
15:56:52.172 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1077 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["武威市","620600","city",null,"620000",null,null,"张掖市","620700","city",null,"620000",null,null,"平凉市","620800","city",null,"620000",null,null,"酒泉市","620900","city",null,"620000",null,null,"庆阳市","621000","city",null,"620000",null,null,"定西市","621100","city",null,"620000",null,null,"陇南市","621200","city",null,"620000",null,null,"临夏回族自治州","622900","city",null,"620000",null,null,"甘南藏族自治州","623000","city",null,"620000",null,null,"成都市","510100","city",null,"510000",null,null,"自贡市","510300","city",null,"510000",null,null,"攀枝花市","510400","city",null,"510000",null,null,"泸州市","510500","city",null,"510000",null,null,"德阳市","510600","city",null,"510000",null,null,"绵阳市","510700","city",null,"510000",null,null,"广元市","510800","city",null,"510000",null,null,"遂宁市","510900","city",null,"510000",null,null,"内江市","511000","city",null,"510000",null,null,"乐山市","511100","city",null,"510000",null,null,"南充市","511300","city",null,"510000",null,null,"眉山市","511400","city",null,"510000",null,null,"宜宾市","511500","city",null,"510000",null,null,"广安市","511600","city",null,"510000",null,null,"达州市","511700","city",null,"510000",null,null,"雅安市","511800","city",null,"510000",null,null,"巴中市","511900","city",null,"510000",null,null,"资阳市","512000","city",null,"510000",null,null,"阿坝藏族羌族自治州","513200","city",null,"510000",null,null,"甘孜藏族自治州","513300","city",null,"510000",null,null,"凉山彝族自治州","513400","city",null,"510000",null,null,"天津城区","120100","city",null,"120000",null,null,"北京城区","110100","city",null,"110000",null,null,"拉萨市","540100","city",null,"540000",null,null,"日喀则市","540200","city",null,"540000",null,null,"昌都市","540300","city",null,"540000",null,null,"林芝市","540400","city",null,"540000",null,null,"山南市","540500","city",null,"540000",null,null,"那曲市","540600","city",null,"540000",null,null,"阿里地区","542500","city",null,"540000",null,null,"长春市","220100","city",null,"220000",null,null,"吉林市","220200","city",null,"220000",null,null,"四平市","220300","city",null,"220000",null,null,"辽源市","220400","city",null,"220000",null,null,"通化市","220500","city",null,"220000",null,null,"白山市","220600","city",null,"220000",null,null,"松原市","220700","city",null,"220000",null,null,"白城市","220800","city",null,"220000",null,null,"延边朝鲜族自治州","222400","city",null,"220000",null,null,"太原市","140100","city",null,"140000",null,null,"大同市","140200","city",null,"140000",null,null,"阳泉市","140300","city",null,"140000",null,null,"长治市","140400","city",null,"140000",null,null,"晋城市","140500","city",null,"140000",null,null,"朔州市","140600","city",null,"140000",null,null,"晋中市","140700","city",null,"140000",null,null,"运城市","140800","city",null,"140000",null,null,"忻州市","140900","city",null,"140000",null,null,"临汾市","141000","city",null,"140000",null,null,"吕梁市","141100","city",null,"140000",null,null,"南昌市","360100","city",null,"360000",null,null,"景德镇市","360200","city",null,"360000",null,null,"萍乡市","360300","city",null,"360000",null,null,"九江市","360400","city",null,"360000",null,null,"新余市","360500","city",null,"360000",null,null,"鹰潭市","360600","city",null,"360000",null,null,"赣州市","360700","city",null,"360000",null,null,"吉安市","360800","city",null,"360000",null,null,"宜春市","360900","city",null,"360000",null,null,"抚州市","361000","city",null,"360000",null,null,"上饶市","361100","city",null,"360000",null,null,"西安市","610100","city",null,"610000",null,null,"铜川市","610200","city",null,"610000",null,null,"宝鸡市","610300","city",null,"610000",null,null,"咸阳市","610400","city",null,"610000",null,null,"渭南市","610500","city",null,"610000",null,null,"延安市","610600","city",null,"610000",null,null,"汉中市","610700","city",null,"610000",null,null,"榆林市","610800","city",null,"610000",null,null,"安康市","610900","city",null,"610000",null,null,"商洛市","611000","city",null,"610000",null,null,"昆明市","530100","city",null,"530000",null,null,"曲靖市","530300","city",null,"530000",null,null,"玉溪市","530400","city",null,"530000",null,null,"保山市","530500","city",null,"530000",null,null,"昭通市","530600","city",null,"530000",null,null,"丽江市","530700","city",null,"530000",null,null,"普洱市","530800","city",null,"530000",null,null,"临沧市","530900","city",null,"530000",null,null,"楚雄彝族自治州","532300","city",null,"530000",null,null,"红河哈尼族彝族自治州","532500","city",null,"530000",null,null,"文山壮族苗族自治州","532600","city",null,"530000",null,null,"西双版纳傣族自治州","532800","city",null,"530000",null,null,"大理白族自治州","532900","city",null,"530000",null,null,"德宏傣族景颇族自治州","533100","city",null,"530000",null,null,"怒江傈僳族自治州","533300","city",null,"530000",null,null,"迪庆藏族自治州","533400","city",null,"530000",null,null]
15:56:56.812 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1088 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["苇湖镇","659008","street",null,"650000","659008",null,"榆树庄镇","659008","street",null,"650000","659008",null,"金梁镇","659008","street",null,"650000","659008",null,"塔城市","654201","district",null,"650000","654200",null,"乌苏市","654202","district",null,"650000","654200",null,"沙湾市","654203","district",null,"650000","654200",null,"额敏县","654221","district",null,"650000","654200",null,"托里县","654224","district",null,"650000","654200",null,"裕民县","654225","district",null,"650000","654200",null,"和布克赛尔蒙古自治县","654226","district",null,"650000","654200",null,"老兵镇","659009","street",null,"650000","659009",null,"昆牧镇","659009","street",null,"650000","659009",null,"昆泉镇","659009","street",null,"650000","659009",null,"兵团二二四团","659009","street",null,"650000","659009",null,"和田市","653201","district",null,"650000","653200",null,"和田县","653221","district",null,"650000","653200",null,"墨玉县","653222","district",null,"650000","653200",null,"皮山县","653223","district",null,"650000","653200",null,"洛浦县","653224","district",null,"650000","653200",null,"策勒县","653225","district",null,"650000","653200",null,"于田县","653226","district",null,"650000","653200",null,"民丰县","653227","district",null,"650000","653200",null,"阿勒泰市","654301","district",null,"650000","654300",null,"布尔津县","654321","district",null,"650000","654300",null,"富蕴县","654322","district",null,"650000","654300",null,"福海县","654323","district",null,"650000","654300",null,"哈巴河县","654324","district",null,"650000","654300",null,"青河县","654325","district",null,"650000","654300",null,"吉木乃县","654326","district",null,"650000","654300",null,"兵团一五二团","659001","street",null,"650000","659001",null,"向阳街道","659001","street",null,"650000","659001",null,"红山街道","659001","street",null,"650000","659001",null,"兵团一四四团","659001","street",null,"650000","659001",null,"东城街道","659001","street",null,"650000","659001",null,"老街街道","659001","street",null,"650000","659001",null,"石河子镇","659001","street",null,"650000","659001",null,"新城街道","659001","street",null,"650000","659001",null,"北泉镇","659001","street",null,"650000","659001",null,"昌吉市","652301","district",null,"650000","652300",null,"阜康市","652302","district",null,"650000","652300",null,"呼图壁县","652323","district",null,"650000","652300",null,"玛纳斯县","652324","district",null,"650000","652300",null,"奇台县","652325","district",null,"650000","652300",null,"吉木萨尔县","652327","district",null,"650000","652300",null,"木垒哈萨克自治县","652328","district",null,"650000","652300",null,"库尔勒市","652801","district",null,"650000","652800",null,"轮台县","652822","district",null,"650000","652800",null,"尉犁县","652823","district",null,"650000","652800",null,"若羌县","652824","district",null,"650000","652800",null,"且末县","652825","district",null,"650000","652800",null,"焉耆回族自治县","652826","district",null,"650000","652800",null,"和静县","652827","district",null,"650000","652800",null,"和硕县","652828","district",null,"650000","652800",null,"博湖县","652829","district",null,"650000","652800",null,"伊宁市","654002","district",null,"650000","654000",null,"奎屯市","654003","district",null,"650000","654000",null,"霍尔果斯市","654004","district",null,"650000","654000",null,"伊宁县","654021","district",null,"650000","654000",null,"察布查尔锡伯自治县","654022","district",null,"650000","654000",null,"霍城县","654023","district",null,"650000","654000",null,"巩留县","654024","district",null,"650000","654000",null,"新源县","654025","district",null,"650000","654000",null,"昭苏县","654026","district",null,"650000","654000",null,"特克斯县","654027","district",null,"650000","654000",null,"尼勒克县","654028","district",null,"650000","654000",null,"玛滩镇","659002","street",null,"650000","659002",null,"永宁镇","659002","street",null,"650000","659002",null,"花桥镇","659002","street",null,"650000","659002",null,"沙河镇","659002","street",null,"650000","659002",null,"托喀依乡","659002","street",null,"650000","659002",null,"青松路街道","659002","street",null,"650000","659002",null,"塔门镇","659002","street",null,"650000","659002",null,"南口街道","659002","street",null,"650000","659002",null,"金杨镇","659002","street",null,"650000","659002",null,"新井子镇","659002","street",null,"650000","659002",null,"金银川镇","659002","street",null,"650000","659002",null,"甘泉镇","659002","street",null,"650000","659002",null,"兵团农一师沙井子水利管理处","659002","street",null,"650000","659002",null,"幸福路街道","659002","street",null,"650000","659002",null,"金银川路街道","659002","street",null,"650000","659002",null,"幸福镇","659002","street",null,"650000","659002",null,"塔南镇","659002","street",null,"650000","659002",null,"新开岭镇","659002","street",null,"650000","659002",null,"昌安镇","659002","street",null,"650000","659002",null,"双城镇","659002","street",null,"650000","659002",null,"喀什市","653101","district",null,"650000","653100",null,"疏附县","653121","district",null,"650000","653100",null,"疏勒县","653122","district",null,"650000","653100",null,"英吉沙县","653123","district",null,"650000","653100",null,"泽普县","653124","district",null,"650000","653100",null,"莎车县","653125","district",null,"650000","653100",null,"叶城县","653126","district",null,"650000","653100",null,"麦盖提县","653127","district",null,"650000","653100",null,"岳普湖县","653128","district",null,"650000","653100",null,"伽师县","653129","district",null,"650000","653100",null,"巴楚县","653130","district",null,"650000","653100",null,"塔什库尔干塔吉克自治县","653131","district",null,"650000","653100",null,"阿图什市","653001","district",null,"650000","653000",null,"阿克陶县","653022","district",null,"650000","653000",null,"阿合奇县","653023","district",null,"650000","653000",null]
15:57:00.099 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1089 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["辽阳县","211021","district",null,"210000","211000",null,"灯塔市","211081","district",null,"210000","211000",null,"新抚区","210402","district",null,"210000","210400",null,"东洲区","210403","district",null,"210000","210400",null,"望花区","210404","district",null,"210000","210400",null,"顺城区","210411","district",null,"210000","210400",null,"抚顺县","210421","district",null,"210000","210400",null,"新宾满族自治县","210422","district",null,"210000","210400",null,"清原满族自治县","210423","district",null,"210000","210400",null,"双台子区","211102","district",null,"210000","211100",null,"兴隆台区","211103","district",null,"210000","211100",null,"大洼区","211104","district",null,"210000","211100",null,"盘山县","211122","district",null,"210000","211100",null,"双塔区","211302","district",null,"210000","211300",null,"龙城区","211303","district",null,"210000","211300",null,"朝阳县","211321","district",null,"210000","211300",null,"建平县","211322","district",null,"210000","211300",null,"喀喇沁左翼蒙古族自治县","211324","district",null,"210000","211300",null,"北票市","211381","district",null,"210000","211300",null,"凌源市","211382","district",null,"210000","211300",null,"铁东区","210302","district",null,"210000","210300",null,"铁西区","210303","district",null,"210000","210300",null,"立山区","210304","district",null,"210000","210300",null,"千山区","210311","district",null,"210000","210300",null,"台安县","210321","district",null,"210000","210300",null,"岫岩满族自治县","210323","district",null,"210000","210300",null,"海城市","210381","district",null,"210000","210300",null,"站前区","210802","district",null,"210000","210800",null,"西市区","210803","district",null,"210000","210800",null,"鲅鱼圈区","210804","district",null,"210000","210800",null,"老边区","210811","district",null,"210000","210800",null,"盖州市","210881","district",null,"210000","210800",null,"大石桥市","210882","district",null,"210000","210800",null,"东营区","370502","district",null,"370000","370500",null,"河口区","370503","district",null,"370000","370500",null,"垦利区","370505","district",null,"370000","370500",null,"利津县","370522","district",null,"370000","370500",null,"广饶县","370523","district",null,"370000","370500",null,"滨城区","371602","district",null,"370000","371600",null,"沾化区","371603","district",null,"370000","371600",null,"惠民县","371621","district",null,"370000","371600",null,"阳信县","371622","district",null,"370000","371600",null,"无棣县","371623","district",null,"370000","371600",null,"博兴县","371625","district",null,"370000","371600",null,"邹平市","371681","district",null,"370000","371600",null,"芝罘区","370602","district",null,"370000","370600",null,"福山区","370611","district",null,"370000","370600",null,"牟平区","370612","district",null,"370000","370600",null,"莱山区","370613","district",null,"370000","370600",null,"蓬莱区","370614","district",null,"370000","370600",null,"龙口市","370681","district",null,"370000","370600",null,"莱阳市","370682","district",null,"370000","370600",null,"莱州市","370683","district",null,"370000","370600",null,"招远市","370685","district",null,"370000","370600",null,"栖霞市","370686","district",null,"370000","370600",null,"海阳市","370687","district",null,"370000","370600",null,"环翠区","371002","district",null,"370000","371000",null,"文登区","371003","district",null,"370000","371000",null,"荣成市","371082","district",null,"370000","371000",null,"乳山市","371083","district",null,"370000","371000",null,"市南区","370202","district",null,"370000","370200",null,"市北区","370203","district",null,"370000","370200",null,"黄岛区","370211","district",null,"370000","370200",null,"崂山区","370212","district",null,"370000","370200",null,"李沧区","370213","district",null,"370000","370200",null,"城阳区","370214","district",null,"370000","370200",null,"即墨区","370215","district",null,"370000","370200",null,"胶州市","370281","district",null,"370000","370200",null,"平度市","370283","district",null,"370000","370200",null,"莱西市","370285","district",null,"370000","370200",null,"东昌府区","371502","district",null,"370000","371500",null,"茌平区","371503","district",null,"370000","371500",null,"阳谷县","371521","district",null,"370000","371500",null,"莘县","371522","district",null,"370000","371500",null,"东阿县","371524","district",null,"370000","371500",null,"冠县","371525","district",null,"370000","371500",null,"高唐县","371526","district",null,"370000","371500",null,"临清市","371581","district",null,"370000","371500",null,"淄川区","370302","district",null,"370000","370300",null,"张店区","370303","district",null,"370000","370300",null,"博山区","370304","district",null,"370000","370300",null,"临淄区","370305","district",null,"370000","370300",null,"周村区","370306","district",null,"370000","370300",null,"桓台县","370321","district",null,"370000","370300",null,"高青县","370322","district",null,"370000","370300",null,"沂源县","370323","district",null,"370000","370300",null,"东港区","371102","district",null,"370000","371100",null,"岚山区","371103","district",null,"370000","371100",null,"五莲县","371121","district",null,"370000","371100",null,"莒县","371122","district",null,"370000","371100",null,"历下区","370102","district",null,"370000","370100",null,"市中区","370103","district",null,"370000","370100",null,"槐荫区","370104","district",null,"370000","370100",null,"天桥区","370105","district",null,"370000","370100",null,"历城区","370112","district",null,"370000","370100",null,"长清区","370113","district",null,"370000","370100",null,"章丘区","370114","district",null,"370000","370100",null,"济阳区","370115","district",null,"370000","370100",null,"莱芜区","370116","district",null,"370000","370100",null,"钢城区","370117","district",null,"370000","370100",null]
15:57:01.187 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1077 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["平阴县","370124","district",null,"370000","370100",null,"商河县","370126","district",null,"370000","370100",null,"潍城区","370702","district",null,"370000","370700",null,"寒亭区","370703","district",null,"370000","370700",null,"坊子区","370704","district",null,"370000","370700",null,"奎文区","370705","district",null,"370000","370700",null,"临朐县","370724","district",null,"370000","370700",null,"昌乐县","370725","district",null,"370000","370700",null,"青州市","370781","district",null,"370000","370700",null,"诸城市","370782","district",null,"370000","370700",null,"寿光市","370783","district",null,"370000","370700",null,"安丘市","370784","district",null,"370000","370700",null,"高密市","370785","district",null,"370000","370700",null,"昌邑市","370786","district",null,"370000","370700",null,"市中区","370402","district",null,"370000","370400",null,"薛城区","370403","district",null,"370000","370400",null,"峄城区","370404","district",null,"370000","370400",null,"台儿庄区","370405","district",null,"370000","370400",null,"山亭区","370406","district",null,"370000","370400",null,"滕州市","370481","district",null,"370000","370400",null,"泰山区","370902","district",null,"370000","370900",null,"岱岳区","370911","district",null,"370000","370900",null,"宁阳县","370921","district",null,"370000","370900",null,"东平县","370923","district",null,"370000","370900",null,"新泰市","370982","district",null,"370000","370900",null,"肥城市","370983","district",null,"370000","370900",null,"任城区","370811","district",null,"370000","370800",null,"兖州区","370812","district",null,"370000","370800",null,"微山县","370826","district",null,"370000","370800",null,"鱼台县","370827","district",null,"370000","370800",null,"金乡县","370828","district",null,"370000","370800",null,"嘉祥县","370829","district",null,"370000","370800",null,"汶上县","370830","district",null,"370000","370800",null,"泗水县","370831","district",null,"370000","370800",null,"梁山县","370832","district",null,"370000","370800",null,"曲阜市","370881","district",null,"370000","370800",null,"邹城市","370883","district",null,"370000","370800",null,"兰山区","371302","district",null,"370000","371300",null,"罗庄区","371311","district",null,"370000","371300",null,"河东区","371312","district",null,"370000","371300",null,"沂南县","371321","district",null,"370000","371300",null,"郯城县","371322","district",null,"370000","371300",null,"沂水县","371323","district",null,"370000","371300",null,"兰陵县","371324","district",null,"370000","371300",null,"费县","371325","district",null,"370000","371300",null,"平邑县","371326","district",null,"370000","371300",null,"莒南县","371327","district",null,"370000","371300",null,"蒙阴县","371328","district",null,"370000","371300",null,"临沭县","371329","district",null,"370000","371300",null,"牡丹区","371702","district",null,"370000","371700",null,"定陶区","371703","district",null,"370000","371700",null,"曹县","371721","district",null,"370000","371700",null,"单县","371722","district",null,"370000","371700",null,"成武县","371723","district",null,"370000","371700",null,"巨野县","371724","district",null,"370000","371700",null,"郓城县","371725","district",null,"370000","371700",null,"鄄城县","371726","district",null,"370000","371700",null,"东明县","371728","district",null,"370000","371700",null,"德城区","371402","district",null,"370000","371400",null,"陵城区","371403","district",null,"370000","371400",null,"宁津县","371422","district",null,"370000","371400",null,"庆云县","371423","district",null,"370000","371400",null,"临邑县","371424","district",null,"370000","371400",null,"齐河县","371425","district",null,"370000","371400",null,"平原县","371426","district",null,"370000","371400",null,"夏津县","371427","district",null,"370000","371400",null,"武城县","371428","district",null,"370000","371400",null,"乐陵市","371481","district",null,"370000","371400",null,"禹城市","371482","district",null,"370000","371400",null,"黄浦区","310101","district",null,"310000","310100",null,"徐汇区","310104","district",null,"310000","310100",null,"长宁区","310105","district",null,"310000","310100",null,"静安区","310106","district",null,"310000","310100",null,"普陀区","310107","district",null,"310000","310100",null,"虹口区","310109","district",null,"310000","310100",null,"杨浦区","310110","district",null,"310000","310100",null,"闵行区","310112","district",null,"310000","310100",null,"宝山区","310113","district",null,"310000","310100",null,"嘉定区","310114","district",null,"310000","310100",null,"浦东新区","310115","district",null,"310000","310100",null,"金山区","310116","district",null,"310000","310100",null,"松江区","310117","district",null,"310000","310100",null,"青浦区","310118","district",null,"310000","310100",null,"奉贤区","310120","district",null,"310000","310100",null,"崇明区","310151","district",null,"310000","310100",null,"红花岗区","520302","district",null,"520000","520300",null,"汇川区","520303","district",null,"520000","520300",null,"播州区","520304","district",null,"520000","520300",null,"桐梓县","520322","district",null,"520000","520300",null,"绥阳县","520323","district",null,"520000","520300",null,"正安县","520324","district",null,"520000","520300",null,"道真仡佬族苗族自治县","520325","district",null,"520000","520300",null,"务川仡佬族苗族自治县","520326","district",null,"520000","520300",null,"凤冈县","520327","district",null,"520000","520300",null,"湄潭县","520328","district",null,"520000","520300",null,"余庆县","520329","district",null,"520000","520300",null,"习水县","520330","district",null,"520000","520300",null,"赤水市","520381","district",null,"520000","520300",null,"仁怀市","520382","district",null,"520000","520300",null,"碧江区","520602","district",null,"520000","520600",null]
15:57:04.221 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1117 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["云溪区","430603","district",null,"430000","430600",null,"君山区","430611","district",null,"430000","430600",null,"岳阳县","430621","district",null,"430000","430600",null,"华容县","430623","district",null,"430000","430600",null,"湘阴县","430624","district",null,"430000","430600",null,"平江县","430626","district",null,"430000","430600",null,"汨罗市","430681","district",null,"430000","430600",null,"临湘市","430682","district",null,"430000","430600",null,"鹤城区","431202","district",null,"430000","431200",null,"中方县","431221","district",null,"430000","431200",null,"沅陵县","431222","district",null,"430000","431200",null,"辰溪县","431223","district",null,"430000","431200",null,"溆浦县","431224","district",null,"430000","431200",null,"会同县","431225","district",null,"430000","431200",null,"麻阳苗族自治县","431226","district",null,"430000","431200",null,"新晃侗族自治县","431227","district",null,"430000","431200",null,"芷江侗族自治县","431228","district",null,"430000","431200",null,"靖州苗族侗族自治县","431229","district",null,"430000","431200",null,"通道侗族自治县","431230","district",null,"430000","431200",null,"洪江市","431281","district",null,"430000","431200",null,"吉首市","433101","district",null,"430000","433100",null,"泸溪县","433122","district",null,"430000","433100",null,"凤凰县","433123","district",null,"430000","433100",null,"花垣县","433124","district",null,"430000","433100",null,"保靖县","433125","district",null,"430000","433100",null,"古丈县","433126","district",null,"430000","433100",null,"永顺县","433127","district",null,"430000","433100",null,"龙山县","433130","district",null,"430000","433100",null,"资阳区","430902","district",null,"430000","430900",null,"赫山区","430903","district",null,"430000","430900",null,"南县","430921","district",null,"430000","430900",null,"桃江县","430922","district",null,"430000","430900",null,"安化县","430923","district",null,"430000","430900",null,"沅江市","430981","district",null,"430000","430900",null,"雨湖区","430302","district",null,"430000","430300",null,"岳塘区","430304","district",null,"430000","430300",null,"湘潭县","430321","district",null,"430000","430300",null,"湘乡市","430381","district",null,"430000","430300",null,"韶山市","430382","district",null,"430000","430300",null,"永定区","430802","district",null,"430000","430800",null,"武陵源区","430811","district",null,"430000","430800",null,"慈利县","430821","district",null,"430000","430800",null,"桑植县","430822","district",null,"430000","430800",null,"荷塘区","430202","district",null,"430000","430200",null,"芦淞区","430203","district",null,"430000","430200",null,"石峰区","430204","district",null,"430000","430200",null,"天元区","430211","district",null,"430000","430200",null,"渌口区","430212","district",null,"430000","430200",null,"攸县","430223","district",null,"430000","430200",null,"茶陵县","430224","district",null,"430000","430200",null,"炎陵县","430225","district",null,"430000","430200",null,"醴陵市","430281","district",null,"430000","430200",null,"娄星区","431302","district",null,"430000","431300",null,"双峰县","431321","district",null,"430000","431300",null,"新化县","431322","district",null,"430000","431300",null,"冷水江市","431381","district",null,"430000","431300",null,"涟源市","431382","district",null,"430000","431300",null,"北湖区","431002","district",null,"430000","431000",null,"苏仙区","431003","district",null,"430000","431000",null,"桂阳县","431021","district",null,"430000","431000",null,"宜章县","431022","district",null,"430000","431000",null,"永兴县","431023","district",null,"430000","431000",null,"嘉禾县","431024","district",null,"430000","431000",null,"临武县","431025","district",null,"430000","431000",null,"汝城县","431026","district",null,"430000","431000",null,"桂东县","431027","district",null,"430000","431000",null,"安仁县","431028","district",null,"430000","431000",null,"资兴市","431081","district",null,"430000","431000",null,"零陵区","431102","district",null,"430000","431100",null,"冷水滩区","431103","district",null,"430000","431100",null,"东安县","431122","district",null,"430000","431100",null,"双牌县","431123","district",null,"430000","431100",null,"道县","431124","district",null,"430000","431100",null,"江永县","431125","district",null,"430000","431100",null,"宁远县","431126","district",null,"430000","431100",null,"蓝山县","431127","district",null,"430000","431100",null,"新田县","431128","district",null,"430000","431100",null,"江华瑶族自治县","431129","district",null,"430000","431100",null,"祁阳市","431181","district",null,"430000","431100",null,"双清区","430502","district",null,"430000","430500",null,"大祥区","430503","district",null,"430000","430500",null,"北塔区","430511","district",null,"430000","430500",null,"新邵县","430522","district",null,"430000","430500",null,"邵阳县","430523","district",null,"430000","430500",null,"隆回县","430524","district",null,"430000","430500",null,"洞口县","430525","district",null,"430000","430500",null,"绥宁县","430527","district",null,"430000","430500",null,"新宁县","430528","district",null,"430000","430500",null,"城步苗族自治县","430529","district",null,"430000","430500",null,"武冈市","430581","district",null,"430000","430500",null,"邵东市","430582","district",null,"430000","430500",null,"珠晖区","430405","district",null,"430000","430400",null,"雁峰区","430406","district",null,"430000","430400",null,"石鼓区","430407","district",null,"430000","430400",null,"蒸湘区","430408","district",null,"430000","430400",null,"南岳区","430412","district",null,"430000","430400",null,"衡阳县","430421","district",null,"430000","430400",null,"衡南县","430422","district",null,"430000","430400",null,"衡山县","430423","district",null,"430000","430400",null,"衡东县","430424","district",null,"430000","430400",null]
15:57:06.652 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1075 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["大茂镇","469006","street",null,"460000","469006",null,"礼纪镇","469006","street",null,"460000","469006",null,"万城镇","469006","street",null,"460000","469006",null,"三更罗镇","469006","street",null,"460000","469006",null,"长丰镇","469006","street",null,"460000","469006",null,"南桥镇","469006","street",null,"460000","469006",null,"连云区","320703","district",null,"320000","320700",null,"海州区","320706","district",null,"320000","320700",null,"赣榆区","320707","district",null,"320000","320700",null,"东海县","320722","district",null,"320000","320700",null,"灌云县","320723","district",null,"320000","320700",null,"灌南县","320724","district",null,"320000","320700",null,"通州区","320612","district",null,"320000","320600",null,"崇川区","320613","district",null,"320000","320600",null,"海门区","320614","district",null,"320000","320600",null,"如东县","320623","district",null,"320000","320600",null,"启东市","320681","district",null,"320000","320600",null,"如皋市","320682","district",null,"320000","320600",null,"海安市","320685","district",null,"320000","320600",null,"玄武区","320102","district",null,"320000","320100",null,"秦淮区","320104","district",null,"320000","320100",null,"建邺区","320105","district",null,"320000","320100",null,"鼓楼区","320106","district",null,"320000","320100",null,"浦口区","320111","district",null,"320000","320100",null,"栖霞区","320113","district",null,"320000","320100",null,"雨花台区","320114","district",null,"320000","320100",null,"江宁区","320115","district",null,"320000","320100",null,"六合区","320116","district",null,"320000","320100",null,"溧水区","320117","district",null,"320000","320100",null,"高淳区","320118","district",null,"320000","320100",null,"虎丘区","320505","district",null,"320000","320500",null,"吴中区","320506","district",null,"320000","320500",null,"相城区","320507","district",null,"320000","320500",null,"姑苏区","320508","district",null,"320000","320500",null,"吴江区","320509","district",null,"320000","320500",null,"常熟市","320581","district",null,"320000","320500",null,"张家港市","320582","district",null,"320000","320500",null,"昆山市","320583","district",null,"320000","320500",null,"太仓市","320585","district",null,"320000","320500",null,"海陵区","321202","district",null,"320000","321200",null,"高港区","321203","district",null,"320000","321200",null,"姜堰区","321204","district",null,"320000","321200",null,"兴化市","321281","district",null,"320000","321200",null,"靖江市","321282","district",null,"320000","321200",null,"泰兴市","321283","district",null,"320000","321200",null,"宿城区","321302","district",null,"320000","321300",null,"宿豫区","321311","district",null,"320000","321300",null,"沭阳县","321322","district",null,"320000","321300",null,"泗阳县","321323","district",null,"320000","321300",null,"泗洪县","321324","district",null,"320000","321300",null,"广陵区","321002","district",null,"320000","321000",null,"邗江区","321003","district",null,"320000","321000",null,"江都区","321012","district",null,"320000","321000",null,"宝应县","321023","district",null,"320000","321000",null,"仪征市","321081","district",null,"320000","321000",null,"高邮市","321084","district",null,"320000","321000",null,"京口区","321102","district",null,"320000","321100",null,"润州区","321111","district",null,"320000","321100",null,"丹徒区","321112","district",null,"320000","321100",null,"丹阳市","321181","district",null,"320000","321100",null,"扬中市","321182","district",null,"320000","321100",null,"句容市","321183","district",null,"320000","321100",null,"亭湖区","320902","district",null,"320000","320900",null,"盐都区","320903","district",null,"320000","320900",null,"大丰区","320904","district",null,"320000","320900",null,"响水县","320921","district",null,"320000","320900",null,"滨海县","320922","district",null,"320000","320900",null,"阜宁县","320923","district",null,"320000","320900",null,"射阳县","320924","district",null,"320000","320900",null,"建湖县","320925","district",null,"320000","320900",null,"东台市","320981","district",null,"320000","320900",null,"淮安区","320803","district",null,"320000","320800",null,"淮阴区","320804","district",null,"320000","320800",null,"清江浦区","320812","district",null,"320000","320800",null,"洪泽区","320813","district",null,"320000","320800",null,"涟水县","320826","district",null,"320000","320800",null,"盱眙县","320830","district",null,"320000","320800",null,"金湖县","320831","district",null,"320000","320800",null,"鼓楼区","320302","district",null,"320000","320300",null,"云龙区","320303","district",null,"320000","320300",null,"贾汪区","320305","district",null,"320000","320300",null,"泉山区","320311","district",null,"320000","320300",null,"铜山区","320312","district",null,"320000","320300",null,"丰县","320321","district",null,"320000","320300",null,"沛县","320322","district",null,"320000","320300",null,"睢宁县","320324","district",null,"320000","320300",null,"新沂市","320381","district",null,"320000","320300",null,"邳州市","320382","district",null,"320000","320300",null,"天宁区","320402","district",null,"320000","320400",null,"钟楼区","320404","district",null,"320000","320400",null,"新北区","320411","district",null,"320000","320400",null,"武进区","320412","district",null,"320000","320400",null,"金坛区","320413","district",null,"320000","320400",null,"溧阳市","320481","district",null,"320000","320400",null,"锡山区","320205","district",null,"320000","320200",null,"惠山区","320206","district",null,"320000","320200",null,"滨湖区","320211","district",null,"320000","320200",null,"梁溪区","320213","district",null,"320000","320200",null,"新吴区","320214","district",null,"320000","320200",null,"江阴市","320281","district",null,"320000","320200",null]
15:57:09.408 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2746 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["宜兴市","320282","district",null,"320000","320200",null,"乐都区","630202","district",null,"630000","630200",null,"平安区","630203","district",null,"630000","630200",null,"民和回族土族自治县","630222","district",null,"630000","630200",null,"互助土族自治县","630223","district",null,"630000","630200",null,"化隆回族自治县","630224","district",null,"630000","630200",null,"循化撒拉族自治县","630225","district",null,"630000","630200",null,"共和县","632521","district",null,"630000","632500",null,"同德县","632522","district",null,"630000","632500",null,"贵德县","632523","district",null,"630000","632500",null,"兴海县","632524","district",null,"630000","632500",null,"贵南县","632525","district",null,"630000","632500",null,"格尔木市","632801","district",null,"630000","632800",null,"德令哈市","632802","district",null,"630000","632800",null,"茫崖市","632803","district",null,"630000","632800",null,"乌兰县","632821","district",null,"630000","632800",null,"都兰县","632822","district",null,"630000","632800",null,"天峻县","632823","district",null,"630000","632800",null,"海西蒙古族藏族自治州直辖","632825","district",null,"630000","632800",null,"玉树市","632701","district",null,"630000","632700",null,"杂多县","632722","district",null,"630000","632700",null,"称多县","632723","district",null,"630000","632700",null,"治多县","632724","district",null,"630000","632700",null,"囊谦县","632725","district",null,"630000","632700",null,"曲麻莱县","632726","district",null,"630000","632700",null,"同仁市","632301","district",null,"630000","632300",null,"尖扎县","632322","district",null,"630000","632300",null,"泽库县","632323","district",null,"630000","632300",null,"河南蒙古族自治县","632324","district",null,"630000","632300",null,"玛沁县","632621","district",null,"630000","632600",null,"班玛县","632622","district",null,"630000","632600",null,"甘德县","632623","district",null,"630000","632600",null,"达日县","632624","district",null,"630000","632600",null,"久治县","632625","district",null,"630000","632600",null,"玛多县","632626","district",null,"630000","632600",null,"城东区","630102","district",null,"630000","630100",null,"城中区","630103","district",null,"630000","630100",null,"城西区","630104","district",null,"630000","630100",null,"城北区","630105","district",null,"630000","630100",null,"湟中区","630106","district",null,"630000","630100",null,"大通回族土族自治县","630121","district",null,"630000","630100",null,"湟源县","630123","district",null,"630000","630100",null,"门源回族自治县","632221","district",null,"630000","632200",null,"祁连县","632222","district",null,"630000","632200",null,"海晏县","632223","district",null,"630000","632200",null,"刚察县","632224","district",null,"630000","632200",null,"右江区","451002","district",null,"450000","451000",null,"田阳区","451003","district",null,"450000","451000",null,"田东县","451022","district",null,"450000","451000",null,"德保县","451024","district",null,"450000","451000",null,"那坡县","451026","district",null,"450000","451000",null,"凌云县","451027","district",null,"450000","451000",null,"乐业县","451028","district",null,"450000","451000",null,"田林县","451029","district",null,"450000","451000",null,"西林县","451030","district",null,"450000","451000",null,"隆林各族自治县","451031","district",null,"450000","451000",null,"靖西市","451081","district",null,"450000","451000",null,"平果市","451082","district",null,"450000","451000",null,"钦南区","450702","district",null,"450000","450700",null,"钦北区","450703","district",null,"450000","450700",null,"灵山县","450721","district",null,"450000","450700",null,"浦北县","450722","district",null,"450000","450700",null,"海城区","450502","district",null,"450000","450500",null,"银海区","450503","district",null,"450000","450500",null,"铁山港区","450512","district",null,"450000","450500",null,"合浦县","450521","district",null,"450000","450500",null,"港口区","450602","district",null,"450000","450600",null,"防城区","450603","district",null,"450000","450600",null,"上思县","450621","district",null,"450000","450600",null,"东兴市","450681","district",null,"450000","450600",null,"金城江区","451202","district",null,"450000","451200",null,"宜州区","451203","district",null,"450000","451200",null,"南丹县","451221","district",null,"450000","451200",null,"天峨县","451222","district",null,"450000","451200",null,"凤山县","451223","district",null,"450000","451200",null,"东兰县","451224","district",null,"450000","451200",null,"罗城仫佬族自治县","451225","district",null,"450000","451200",null,"环江毛南族自治县","451226","district",null,"450000","451200",null,"巴马瑶族自治县","451227","district",null,"450000","451200",null,"都安瑶族自治县","451228","district",null,"450000","451200",null,"大化瑶族自治县","451229","district",null,"450000","451200",null,"城中区","450202","district",null,"450000","450200",null,"鱼峰区","450203","district",null,"450000","450200",null,"柳南区","450204","district",null,"450000","450200",null,"柳北区","450205","district",null,"450000","450200",null,"柳江区","450206","district",null,"450000","450200",null,"柳城县","450222","district",null,"450000","450200",null,"鹿寨县","450223","district",null,"450000","450200",null,"融安县","450224","district",null,"450000","450200",null,"融水苗族自治县","450225","district",null,"450000","450200",null,"三江侗族自治县","450226","district",null,"450000","450200",null,"兴宾区","451302","district",null,"450000","451300",null,"忻城县","451321","district",null,"450000","451300",null,"象州县","451322","district",null,"450000","451300",null,"武宣县","451323","district",null,"450000","451300",null,"金秀瑶族自治县","451324","district",null,"450000","451300",null,"合山市","451381","district",null,"450000","451300",null,"秀峰区","450302","district",null,"450000","450300",null,"叠彩区","450303","district",null,"450000","450300",null,"象山区","450304","district",null,"450000","450300",null]
15:57:12.977 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2188 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["平泉市","130881","district",null,"130000","130800",null,"安次区","131002","district",null,"130000","131000",null,"广阳区","131003","district",null,"130000","131000",null,"固安县","131022","district",null,"130000","131000",null,"永清县","131023","district",null,"130000","131000",null,"香河县","131024","district",null,"130000","131000",null,"大城县","131025","district",null,"130000","131000",null,"文安县","131026","district",null,"130000","131000",null,"大厂回族自治县","131028","district",null,"130000","131000",null,"霸州市","131081","district",null,"130000","131000",null,"三河市","131082","district",null,"130000","131000",null,"新华区","130902","district",null,"130000","130900",null,"运河区","130903","district",null,"130000","130900",null,"沧县","130921","district",null,"130000","130900",null,"青县","130922","district",null,"130000","130900",null,"东光县","130923","district",null,"130000","130900",null,"海兴县","130924","district",null,"130000","130900",null,"盐山县","130925","district",null,"130000","130900",null,"肃宁县","130926","district",null,"130000","130900",null,"南皮县","130927","district",null,"130000","130900",null,"吴桥县","130928","district",null,"130000","130900",null,"献县","130929","district",null,"130000","130900",null,"孟村回族自治县","130930","district",null,"130000","130900",null,"泊头市","130981","district",null,"130000","130900",null,"任丘市","130982","district",null,"130000","130900",null,"黄骅市","130983","district",null,"130000","130900",null,"河间市","130984","district",null,"130000","130900",null,"桃城区","131102","district",null,"130000","131100",null,"冀州区","131103","district",null,"130000","131100",null,"枣强县","131121","district",null,"130000","131100",null,"武邑县","131122","district",null,"130000","131100",null,"武强县","131123","district",null,"130000","131100",null,"饶阳县","131124","district",null,"130000","131100",null,"安平县","131125","district",null,"130000","131100",null,"故城县","131126","district",null,"130000","131100",null,"景县","131127","district",null,"130000","131100",null,"阜城县","131128","district",null,"130000","131100",null,"深州市","131182","district",null,"130000","131100",null,"襄都区","130502","district",null,"130000","130500",null,"信都区","130503","district",null,"130000","130500",null,"任泽区","130505","district",null,"130000","130500",null,"南和区","130506","district",null,"130000","130500",null,"临城县","130522","district",null,"130000","130500",null,"内丘县","130523","district",null,"130000","130500",null,"柏乡县","130524","district",null,"130000","130500",null,"隆尧县","130525","district",null,"130000","130500",null,"宁晋县","130528","district",null,"130000","130500",null,"巨鹿县","130529","district",null,"130000","130500",null,"新河县","130530","district",null,"130000","130500",null,"广宗县","130531","district",null,"130000","130500",null,"平乡县","130532","district",null,"130000","130500",null,"威县","130533","district",null,"130000","130500",null,"清河县","130534","district",null,"130000","130500",null,"临西县","130535","district",null,"130000","130500",null,"南宫市","130581","district",null,"130000","130500",null,"沙河市","130582","district",null,"130000","130500",null,"邯山区","130402","district",null,"130000","130400",null,"丛台区","130403","district",null,"130000","130400",null,"复兴区","130404","district",null,"130000","130400",null,"峰峰矿区","130406","district",null,"130000","130400",null,"肥乡区","130407","district",null,"130000","130400",null,"永年区","130408","district",null,"130000","130400",null,"临漳县","130423","district",null,"130000","130400",null,"成安县","130424","district",null,"130000","130400",null,"大名县","130425","district",null,"130000","130400",null,"涉县","130426","district",null,"130000","130400",null,"磁县","130427","district",null,"130000","130400",null,"邱县","130430","district",null,"130000","130400",null,"鸡泽县","130431","district",null,"130000","130400",null,"广平县","130432","district",null,"130000","130400",null,"馆陶县","130433","district",null,"130000","130400",null,"魏县","130434","district",null,"130000","130400",null,"曲周县","130435","district",null,"130000","130400",null,"武安市","130481","district",null,"130000","130400",null,"长安区","130102","district",null,"130000","130100",null,"桥西区","130104","district",null,"130000","130100",null,"新华区","130105","district",null,"130000","130100",null,"井陉矿区","130107","district",null,"130000","130100",null,"裕华区","130108","district",null,"130000","130100",null,"藁城区","130109","district",null,"130000","130100",null,"鹿泉区","130110","district",null,"130000","130100",null,"栾城区","130111","district",null,"130000","130100",null,"井陉县","130121","district",null,"130000","130100",null,"正定县","130123","district",null,"130000","130100",null,"行唐县","130125","district",null,"130000","130100",null,"灵寿县","130126","district",null,"130000","130100",null,"高邑县","130127","district",null,"130000","130100",null,"深泽县","130128","district",null,"130000","130100",null,"赞皇县","130129","district",null,"130000","130100",null,"无极县","130130","district",null,"130000","130100",null,"平山县","130131","district",null,"130000","130100",null,"元氏县","130132","district",null,"130000","130100",null,"赵县","130133","district",null,"130000","130100",null,"辛集市","130181","district",null,"130000","130100",null,"晋州市","130183","district",null,"130000","130100",null,"新乐市","130184","district",null,"130000","130100",null,"竞秀区","130602","district",null,"130000","130600",null,"莲池区","130606","district",null,"130000","130600",null,"满城区","130607","district",null,"130000","130600",null,"清苑区","130608","district",null,"130000","130600",null]
15:57:14.291 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1308 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["徐水区","130609","district",null,"130000","130600",null,"涞水县","130623","district",null,"130000","130600",null,"阜平县","130624","district",null,"130000","130600",null,"定兴县","130626","district",null,"130000","130600",null,"唐县","130627","district",null,"130000","130600",null,"高阳县","130628","district",null,"130000","130600",null,"容城县","130629","district",null,"130000","130600",null,"涞源县","130630","district",null,"130000","130600",null,"望都县","130631","district",null,"130000","130600",null,"安新县","130632","district",null,"130000","130600",null,"易县","130633","district",null,"130000","130600",null,"曲阳县","130634","district",null,"130000","130600",null,"蠡县","130635","district",null,"130000","130600",null,"顺平县","130636","district",null,"130000","130600",null,"博野县","130637","district",null,"130000","130600",null,"雄县","130638","district",null,"130000","130600",null,"涿州市","130681","district",null,"130000","130600",null,"定州市","130682","district",null,"130000","130600",null,"安国市","130683","district",null,"130000","130600",null,"高碑店市","130684","district",null,"130000","130600",null,"桥东区","130702","district",null,"130000","130700",null,"桥西区","130703","district",null,"130000","130700",null,"宣化区","130705","district",null,"130000","130700",null,"下花园区","130706","district",null,"130000","130700",null,"万全区","130708","district",null,"130000","130700",null,"崇礼区","130709","district",null,"130000","130700",null,"张北县","130722","district",null,"130000","130700",null,"康保县","130723","district",null,"130000","130700",null,"沽源县","130724","district",null,"130000","130700",null,"尚义县","130725","district",null,"130000","130700",null,"蔚县","130726","district",null,"130000","130700",null,"阳原县","130727","district",null,"130000","130700",null,"怀安县","130728","district",null,"130000","130700",null,"怀来县","130730","district",null,"130000","130700",null,"涿鹿县","130731","district",null,"130000","130700",null,"赤城县","130732","district",null,"130000","130700",null,"峪泉镇","620200","street",null,"620000","620200",null,"新城镇","620200","street",null,"620000","620200",null,"文殊镇","620200","street",null,"620000","620200",null,"钢城街道","620200","street",null,"620000","620200",null,"雄关街道","620200","street",null,"620000","620200",null,"肃州区","620902","district",null,"620000","620900",null,"金塔县","620921","district",null,"620000","620900",null,"瓜州县","620922","district",null,"620000","620900",null,"肃北蒙古族自治县","620923","district",null,"620000","620900",null,"阿克塞哈萨克族自治县","620924","district",null,"620000","620900",null,"玉门市","620981","district",null,"620000","620900",null,"敦煌市","620982","district",null,"620000","620900",null,"城关区","620102","district",null,"620000","620100",null,"七里河区","620103","district",null,"620000","620100",null,"西固区","620104","district",null,"620000","620100",null,"安宁区","620105","district",null,"620000","620100",null,"红古区","620111","district",null,"620000","620100",null,"永登县","620121","district",null,"620000","620100",null,"皋兰县","620122","district",null,"620000","620100",null,"榆中县","620123","district",null,"620000","620100",null,"崆峒区","620802","district",null,"620000","620800",null,"泾川县","620821","district",null,"620000","620800",null,"灵台县","620822","district",null,"620000","620800",null,"崇信县","620823","district",null,"620000","620800",null,"庄浪县","620825","district",null,"620000","620800",null,"静宁县","620826","district",null,"620000","620800",null,"华亭市","620881","district",null,"620000","620800",null,"金川区","620302","district",null,"620000","620300",null,"永昌县","620321","district",null,"620000","620300",null,"秦州区","620502","district",null,"620000","620500",null,"麦积区","620503","district",null,"620000","620500",null,"清水县","620521","district",null,"620000","620500",null,"秦安县","620522","district",null,"620000","620500",null,"甘谷县","620523","district",null,"620000","620500",null,"武山县","620524","district",null,"620000","620500",null,"张家川回族自治县","620525","district",null,"620000","620500",null,"白银区","620402","district",null,"620000","620400",null,"平川区","620403","district",null,"620000","620400",null,"靖远县","620421","district",null,"620000","620400",null,"会宁县","620422","district",null,"620000","620400",null,"景泰县","620423","district",null,"620000","620400",null,"凉州区","620602","district",null,"620000","620600",null,"民勤县","620621","district",null,"620000","620600",null,"古浪县","620622","district",null,"620000","620600",null,"天祝藏族自治县","620623","district",null,"620000","620600",null,"甘州区","620702","district",null,"620000","620700",null,"肃南裕固族自治县","620721","district",null,"620000","620700",null,"民乐县","620722","district",null,"620000","620700",null,"临泽县","620723","district",null,"620000","620700",null,"高台县","620724","district",null,"620000","620700",null,"山丹县","620725","district",null,"620000","620700",null,"安定区","621102","district",null,"620000","621100",null,"通渭县","621121","district",null,"620000","621100",null,"陇西县","621122","district",null,"620000","621100",null,"渭源县","621123","district",null,"620000","621100",null,"临洮县","621124","district",null,"620000","621100",null,"漳县","621125","district",null,"620000","621100",null,"岷县","621126","district",null,"620000","621100",null,"合作市","623001","district",null,"620000","623000",null,"临潭县","623021","district",null,"620000","623000",null,"卓尼县","623022","district",null,"620000","623000",null,"舟曲县","623023","district",null,"620000","623000",null,"迭部县","623024","district",null,"620000","623000",null,"玛曲县","623025","district",null,"620000","623000",null]
15:57:17.027 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2727 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["碌曲县","623026","district",null,"620000","623000",null,"夏河县","623027","district",null,"620000","623000",null,"武都区","621202","district",null,"620000","621200",null,"成县","621221","district",null,"620000","621200",null,"文县","621222","district",null,"620000","621200",null,"宕昌县","621223","district",null,"620000","621200",null,"康县","621224","district",null,"620000","621200",null,"西和县","621225","district",null,"620000","621200",null,"礼县","621226","district",null,"620000","621200",null,"徽县","621227","district",null,"620000","621200",null,"两当县","621228","district",null,"620000","621200",null,"西峰区","621002","district",null,"620000","621000",null,"庆城县","621021","district",null,"620000","621000",null,"环县","621022","district",null,"620000","621000",null,"华池县","621023","district",null,"620000","621000",null,"合水县","621024","district",null,"620000","621000",null,"正宁县","621025","district",null,"620000","621000",null,"宁县","621026","district",null,"620000","621000",null,"镇原县","621027","district",null,"620000","621000",null,"临夏市","622901","district",null,"620000","622900",null,"临夏县","622921","district",null,"620000","622900",null,"康乐县","622922","district",null,"620000","622900",null,"永靖县","622923","district",null,"620000","622900",null,"广河县","622924","district",null,"620000","622900",null,"和政县","622925","district",null,"620000","622900",null,"东乡族自治县","622926","district",null,"620000","622900",null,"积石山保安族东乡族撒拉族自治县","622927","district",null,"620000","622900",null,"利州区","510802","district",null,"510000","510800",null,"昭化区","510811","district",null,"510000","510800",null,"朝天区","510812","district",null,"510000","510800",null,"旺苍县","510821","district",null,"510000","510800",null,"青川县","510822","district",null,"510000","510800",null,"剑阁县","510823","district",null,"510000","510800",null,"苍溪县","510824","district",null,"510000","510800",null,"顺庆区","511302","district",null,"510000","511300",null,"高坪区","511303","district",null,"510000","511300",null,"嘉陵区","511304","district",null,"510000","511300",null,"南部县","511321","district",null,"510000","511300",null,"营山县","511322","district",null,"510000","511300",null,"蓬安县","511323","district",null,"510000","511300",null,"仪陇县","511324","district",null,"510000","511300",null,"西充县","511325","district",null,"510000","511300",null,"阆中市","511381","district",null,"510000","511300",null,"巴州区","511902","district",null,"510000","511900",null,"恩阳区","511903","district",null,"510000","511900",null,"通江县","511921","district",null,"510000","511900",null,"南江县","511922","district",null,"510000","511900",null,"平昌县","511923","district",null,"510000","511900",null,"旌阳区","510603","district",null,"510000","510600",null,"罗江区","510604","district",null,"510000","510600",null,"中江县","510623","district",null,"510000","510600",null,"广汉市","510681","district",null,"510000","510600",null,"什邡市","510682","district",null,"510000","510600",null,"绵竹市","510683","district",null,"510000","510600",null,"涪城区","510703","district",null,"510000","510700",null,"游仙区","510704","district",null,"510000","510700",null,"安州区","510705","district",null,"510000","510700",null,"三台县","510722","district",null,"510000","510700",null,"盐亭县","510723","district",null,"510000","510700",null,"梓潼县","510725","district",null,"510000","510700",null,"北川羌族自治县","510726","district",null,"510000","510700",null,"平武县","510727","district",null,"510000","510700",null,"江油市","510781","district",null,"510000","510700",null,"锦江区","510104","district",null,"510000","510100",null,"青羊区","510105","district",null,"510000","510100",null,"金牛区","510106","district",null,"510000","510100",null,"武侯区","510107","district",null,"510000","510100",null,"成华区","510108","district",null,"510000","510100",null,"龙泉驿区","510112","district",null,"510000","510100",null,"青白江区","510113","district",null,"510000","510100",null,"新都区","510114","district",null,"510000","510100",null,"温江区","510115","district",null,"510000","510100",null,"双流区","510116","district",null,"510000","510100",null,"郫都区","510117","district",null,"510000","510100",null,"新津区","510118","district",null,"510000","510100",null,"金堂县","510121","district",null,"510000","510100",null,"大邑县","510129","district",null,"510000","510100",null,"蒲江县","510131","district",null,"510000","510100",null,"都江堰市","510181","district",null,"510000","510100",null,"彭州市","510182","district",null,"510000","510100",null,"邛崃市","510183","district",null,"510000","510100",null,"崇州市","510184","district",null,"510000","510100",null,"简阳市","510185","district",null,"510000","510100",null,"广安区","511602","district",null,"510000","511600",null,"前锋区","511603","district",null,"510000","511600",null,"岳池县","511621","district",null,"510000","511600",null,"武胜县","511622","district",null,"510000","511600",null,"邻水县","511623","district",null,"510000","511600",null,"华蓥市","511681","district",null,"510000","511600",null,"通川区","511702","district",null,"510000","511700",null,"达川区","511703","district",null,"510000","511700",null,"宣汉县","511722","district",null,"510000","511700",null,"开江县","511723","district",null,"510000","511700",null,"大竹县","511724","district",null,"510000","511700",null,"渠县","511725","district",null,"510000","511700",null,"万源市","511781","district",null,"510000","511700",null,"船山区","510903","district",null,"510000","510900",null,"安居区","510904","district",null,"510000","510900",null,"蓬溪县","510921","district",null,"510000","510900",null,"大英县","510923","district",null,"510000","510900",null]
15:57:23.048 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 6016 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["射洪市","510981","district",null,"510000","510900",null,"雁江区","512002","district",null,"510000","512000",null,"安岳县","512021","district",null,"510000","512000",null,"乐至县","512022","district",null,"510000","512000",null,"东坡区","511402","district",null,"510000","511400",null,"彭山区","511403","district",null,"510000","511400",null,"仁寿县","511421","district",null,"510000","511400",null,"洪雅县","511423","district",null,"510000","511400",null,"丹棱县","511424","district",null,"510000","511400",null,"青神县","511425","district",null,"510000","511400",null,"市中区","511002","district",null,"510000","511000",null,"东兴区","511011","district",null,"510000","511000",null,"威远县","511024","district",null,"510000","511000",null,"资中县","511025","district",null,"510000","511000",null,"隆昌市","511083","district",null,"510000","511000",null,"市中区","511102","district",null,"510000","511100",null,"沙湾区","511111","district",null,"510000","511100",null,"五通桥区","511112","district",null,"510000","511100",null,"金口河区","511113","district",null,"510000","511100",null,"犍为县","511123","district",null,"510000","511100",null,"井研县","511124","district",null,"510000","511100",null,"夹江县","511126","district",null,"510000","511100",null,"沐川县","511129","district",null,"510000","511100",null,"峨边彝族自治县","511132","district",null,"510000","511100",null,"马边彝族自治县","511133","district",null,"510000","511100",null,"峨眉山市","511181","district",null,"510000","511100",null,"自流井区","510302","district",null,"510000","510300",null,"贡井区","510303","district",null,"510000","510300",null,"大安区","510304","district",null,"510000","510300",null,"沿滩区","510311","district",null,"510000","510300",null,"荣县","510321","district",null,"510000","510300",null,"富顺县","510322","district",null,"510000","510300",null,"江阳区","510502","district",null,"510000","510500",null,"纳溪区","510503","district",null,"510000","510500",null,"龙马潭区","510504","district",null,"510000","510500",null,"泸县","510521","district",null,"510000","510500",null,"合江县","510522","district",null,"510000","510500",null,"叙永县","510524","district",null,"510000","510500",null,"古蔺县","510525","district",null,"510000","510500",null,"翠屏区","511502","district",null,"510000","511500",null,"南溪区","511503","district",null,"510000","511500",null,"叙州区","511504","district",null,"510000","511500",null,"江安县","511523","district",null,"510000","511500",null,"长宁县","511524","district",null,"510000","511500",null,"高县","511525","district",null,"510000","511500",null,"珙县","511526","district",null,"510000","511500",null,"筠连县","511527","district",null,"510000","511500",null,"兴文县","511528","district",null,"510000","511500",null,"屏山县","511529","district",null,"510000","511500",null,"西昌市","513401","district",null,"510000","513400",null,"会理市","513402","district",null,"510000","513400",null,"木里藏族自治县","513422","district",null,"510000","513400",null,"盐源县","513423","district",null,"510000","513400",null,"德昌县","513424","district",null,"510000","513400",null,"会东县","513426","district",null,"510000","513400",null,"宁南县","513427","district",null,"510000","513400",null,"普格县","513428","district",null,"510000","513400",null,"布拖县","513429","district",null,"510000","513400",null,"金阳县","513430","district",null,"510000","513400",null,"昭觉县","513431","district",null,"510000","513400",null,"喜德县","513432","district",null,"510000","513400",null,"冕宁县","513433","district",null,"510000","513400",null,"越西县","513434","district",null,"510000","513400",null,"甘洛县","513435","district",null,"510000","513400",null,"美姑县","513436","district",null,"510000","513400",null,"雷波县","513437","district",null,"510000","513400",null,"东区","510402","district",null,"510000","510400",null,"西区","510403","district",null,"510000","510400",null,"仁和区","510411","district",null,"510000","510400",null,"米易县","510421","district",null,"510000","510400",null,"盐边县","510422","district",null,"510000","510400",null,"康定市","513301","district",null,"510000","513300",null,"泸定县","513322","district",null,"510000","513300",null,"丹巴县","513323","district",null,"510000","513300",null,"九龙县","513324","district",null,"510000","513300",null,"雅江县","513325","district",null,"510000","513300",null,"道孚县","513326","district",null,"510000","513300",null,"炉霍县","513327","district",null,"510000","513300",null,"甘孜县","513328","district",null,"510000","513300",null,"新龙县","513329","district",null,"510000","513300",null,"德格县","513330","district",null,"510000","513300",null,"白玉县","513331","district",null,"510000","513300",null,"石渠县","513332","district",null,"510000","513300",null,"色达县","513333","district",null,"510000","513300",null,"理塘县","513334","district",null,"510000","513300",null,"巴塘县","513335","district",null,"510000","513300",null,"乡城县","513336","district",null,"510000","513300",null,"稻城县","513337","district",null,"510000","513300",null,"得荣县","513338","district",null,"510000","513300",null,"雨城区","511802","district",null,"510000","511800",null,"名山区","511803","district",null,"510000","511800",null,"荥经县","511822","district",null,"510000","511800",null,"汉源县","511823","district",null,"510000","511800",null,"石棉县","511824","district",null,"510000","511800",null,"天全县","511825","district",null,"510000","511800",null,"芦山县","511826","district",null,"510000","511800",null,"宝兴县","511827","district",null,"510000","511800",null,"马尔康市","513201","district",null,"510000","513200",null,"汶川县","513221","district",null,"510000","513200",null,"理县","513222","district",null,"510000","513200",null]
15:57:24.960 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1905 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["茂县","513223","district",null,"510000","513200",null,"松潘县","513224","district",null,"510000","513200",null,"九寨沟县","513225","district",null,"510000","513200",null,"金川县","513226","district",null,"510000","513200",null,"小金县","513227","district",null,"510000","513200",null,"黑水县","513228","district",null,"510000","513200",null,"壤塘县","513230","district",null,"510000","513200",null,"阿坝县","513231","district",null,"510000","513200",null,"若尔盖县","513232","district",null,"510000","513200",null,"红原县","513233","district",null,"510000","513200",null,"和平区","120101","district",null,"120000","120100",null,"河东区","120102","district",null,"120000","120100",null,"河西区","120103","district",null,"120000","120100",null,"南开区","120104","district",null,"120000","120100",null,"河北区","120105","district",null,"120000","120100",null,"红桥区","120106","district",null,"120000","120100",null,"东丽区","120110","district",null,"120000","120100",null,"西青区","120111","district",null,"120000","120100",null,"津南区","120112","district",null,"120000","120100",null,"北辰区","120113","district",null,"120000","120100",null,"武清区","120114","district",null,"120000","120100",null,"宝坻区","120115","district",null,"120000","120100",null,"滨海新区","120116","district",null,"120000","120100",null,"宁河区","120117","district",null,"120000","120100",null,"静海区","120118","district",null,"120000","120100",null,"蓟州区","120119","district",null,"120000","120100",null,"东城区","110101","district",null,"110000","110100",null,"西城区","110102","district",null,"110000","110100",null,"朝阳区","110105","district",null,"110000","110100",null,"丰台区","110106","district",null,"110000","110100",null,"石景山区","110107","district",null,"110000","110100",null,"海淀区","110108","district",null,"110000","110100",null,"门头沟区","110109","district",null,"110000","110100",null,"房山区","110111","district",null,"110000","110100",null,"通州区","110112","district",null,"110000","110100",null,"顺义区","110113","district",null,"110000","110100",null,"昌平区","110114","district",null,"110000","110100",null,"大兴区","110115","district",null,"110000","110100",null,"怀柔区","110116","district",null,"110000","110100",null,"平谷区","110117","district",null,"110000","110100",null,"密云区","110118","district",null,"110000","110100",null,"延庆区","110119","district",null,"110000","110100",null,"卡若区","540302","district",null,"540000","540300",null,"江达县","540321","district",null,"540000","540300",null,"贡觉县","540322","district",null,"540000","540300",null,"类乌齐县","540323","district",null,"540000","540300",null,"丁青县","540324","district",null,"540000","540300",null,"察雅县","540325","district",null,"540000","540300",null,"八宿县","540326","district",null,"540000","540300",null,"左贡县","540327","district",null,"540000","540300",null,"芒康县","540328","district",null,"540000","540300",null,"洛隆县","540329","district",null,"540000","540300",null,"边坝县","540330","district",null,"540000","540300",null,"色尼区","540602","district",null,"540000","540600",null,"嘉黎县","540621","district",null,"540000","540600",null,"比如县","540622","district",null,"540000","540600",null,"聂荣县","540623","district",null,"540000","540600",null,"安多县","540624","district",null,"540000","540600",null,"申扎县","540625","district",null,"540000","540600",null,"索县","540626","district",null,"540000","540600",null,"班戈县","540627","district",null,"540000","540600",null,"巴青县","540628","district",null,"540000","540600",null,"尼玛县","540629","district",null,"540000","540600",null,"双湖县","540630","district",null,"540000","540600",null,"城关区","540102","district",null,"540000","540100",null,"堆龙德庆区","540103","district",null,"540000","540100",null,"达孜区","540104","district",null,"540000","540100",null,"林周县","540121","district",null,"540000","540100",null,"当雄县","540122","district",null,"540000","540100",null,"尼木县","540123","district",null,"540000","540100",null,"曲水县","540124","district",null,"540000","540100",null,"墨竹工卡县","540127","district",null,"540000","540100",null,"桑珠孜区","540202","district",null,"540000","540200",null,"南木林县","540221","district",null,"540000","540200",null,"江孜县","540222","district",null,"540000","540200",null,"定日县","540223","district",null,"540000","540200",null,"萨迦县","540224","district",null,"540000","540200",null,"拉孜县","540225","district",null,"540000","540200",null,"昂仁县","540226","district",null,"540000","540200",null,"谢通门县","540227","district",null,"540000","540200",null,"白朗县","540228","district",null,"540000","540200",null,"仁布县","540229","district",null,"540000","540200",null,"康马县","540230","district",null,"540000","540200",null,"定结县","540231","district",null,"540000","540200",null,"仲巴县","540232","district",null,"540000","540200",null,"亚东县","540233","district",null,"540000","540200",null,"吉隆县","540234","district",null,"540000","540200",null,"聂拉木县","540235","district",null,"540000","540200",null,"萨嘎县","540236","district",null,"540000","540200",null,"岗巴县","540237","district",null,"540000","540200",null,"乃东区","540502","district",null,"540000","540500",null,"扎囊县","540521","district",null,"540000","540500",null,"贡嘎县","540522","district",null,"540000","540500",null,"桑日县","540523","district",null,"540000","540500",null,"琼结县","540524","district",null,"540000","540500",null,"曲松县","540525","district",null,"540000","540500",null,"措美县","540526","district",null,"540000","540500",null,"洛扎县","540527","district",null,"540000","540500",null,"加查县","540528","district",null,"540000","540500",null,"隆子县","540529","district",null,"540000","540500",null]
15:57:26.883 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1917 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["浪卡子县","540531","district",null,"540000","540500",null,"错那市","540581","district",null,"540000","540500",null,"巴宜区","540402","district",null,"540000","540400",null,"工布江达县","540421","district",null,"540000","540400",null,"墨脱县","540423","district",null,"540000","540400",null,"波密县","540424","district",null,"540000","540400",null,"察隅县","540425","district",null,"540000","540400",null,"朗县","540426","district",null,"540000","540400",null,"米林市","540481","district",null,"540000","540400",null,"普兰县","542521","district",null,"540000","542500",null,"札达县","542522","district",null,"540000","542500",null,"噶尔县","542523","district",null,"540000","542500",null,"日土县","542524","district",null,"540000","542500",null,"革吉县","542525","district",null,"540000","542500",null,"改则县","542526","district",null,"540000","542500",null,"措勤县","542527","district",null,"540000","542500",null,"昌邑区","220202","district",null,"220000","220200",null,"龙潭区","220203","district",null,"220000","220200",null,"船营区","220204","district",null,"220000","220200",null,"丰满区","220211","district",null,"220000","220200",null,"永吉县","220221","district",null,"220000","220200",null,"蛟河市","220281","district",null,"220000","220200",null,"桦甸市","220282","district",null,"220000","220200",null,"舒兰市","220283","district",null,"220000","220200",null,"磐石市","220284","district",null,"220000","220200",null,"南关区","220102","district",null,"220000","220100",null,"宽城区","220103","district",null,"220000","220100",null,"朝阳区","220104","district",null,"220000","220100",null,"二道区","220105","district",null,"220000","220100",null,"绿园区","220106","district",null,"220000","220100",null,"双阳区","220112","district",null,"220000","220100",null,"九台区","220113","district",null,"220000","220100",null,"农安县","220122","district",null,"220000","220100",null,"榆树市","220182","district",null,"220000","220100",null,"德惠市","220183","district",null,"220000","220100",null,"公主岭市","220184","district",null,"220000","220100",null,"洮北区","220802","district",null,"220000","220800",null,"镇赉县","220821","district",null,"220000","220800",null,"通榆县","220822","district",null,"220000","220800",null,"洮南市","220881","district",null,"220000","220800",null,"大安市","220882","district",null,"220000","220800",null,"宁江区","220702","district",null,"220000","220700",null,"前郭尔罗斯蒙古族自治县","220721","district",null,"220000","220700",null,"长岭县","220722","district",null,"220000","220700",null,"乾安县","220723","district",null,"220000","220700",null,"扶余市","220781","district",null,"220000","220700",null,"延吉市","222401","district",null,"220000","222400",null,"图们市","222402","district",null,"220000","222400",null,"敦化市","222403","district",null,"220000","222400",null,"珲春市","222404","district",null,"220000","222400",null,"龙井市","222405","district",null,"220000","222400",null,"和龙市","222406","district",null,"220000","222400",null,"汪清县","222424","district",null,"220000","222400",null,"安图县","222426","district",null,"220000","222400",null,"铁西区","220302","district",null,"220000","220300",null,"铁东区","220303","district",null,"220000","220300",null,"梨树县","220322","district",null,"220000","220300",null,"伊通满族自治县","220323","district",null,"220000","220300",null,"双辽市","220382","district",null,"220000","220300",null,"浑江区","220602","district",null,"220000","220600",null,"江源区","220605","district",null,"220000","220600",null,"抚松县","220621","district",null,"220000","220600",null,"靖宇县","220622","district",null,"220000","220600",null,"长白朝鲜族自治县","220623","district",null,"220000","220600",null,"临江市","220681","district",null,"220000","220600",null,"龙山区","220402","district",null,"220000","220400",null,"西安区","220403","district",null,"220000","220400",null,"东丰县","220421","district",null,"220000","220400",null,"东辽县","220422","district",null,"220000","220400",null,"东昌区","220502","district",null,"220000","220500",null,"二道江区","220503","district",null,"220000","220500",null,"通化县","220521","district",null,"220000","220500",null,"辉南县","220523","district",null,"220000","220500",null,"柳河县","220524","district",null,"220000","220500",null,"梅河口市","220581","district",null,"220000","220500",null,"集安市","220582","district",null,"220000","220500",null,"城区","140302","district",null,"140000","140300",null,"矿区","140303","district",null,"140000","140300",null,"郊区","140311","district",null,"140000","140300",null,"平定县","140321","district",null,"140000","140300",null,"盂县","140322","district",null,"140000","140300",null,"新荣区","140212","district",null,"140000","140200",null,"平城区","140213","district",null,"140000","140200",null,"云冈区","140214","district",null,"140000","140200",null,"云州区","140215","district",null,"140000","140200",null,"阳高县","140221","district",null,"140000","140200",null,"天镇县","140222","district",null,"140000","140200",null,"广灵县","140223","district",null,"140000","140200",null,"灵丘县","140224","district",null,"140000","140200",null,"浑源县","140225","district",null,"140000","140200",null,"左云县","140226","district",null,"140000","140200",null,"离石区","141102","district",null,"140000","141100",null,"文水县","141121","district",null,"140000","141100",null,"交城县","141122","district",null,"140000","141100",null,"兴县","141123","district",null,"140000","141100",null,"临县","141124","district",null,"140000","141100",null,"柳林县","141125","district",null,"140000","141100",null,"石楼县","141126","district",null,"140000","141100",null,"岚县","141127","district",null,"140000","141100",null,"方山县","141128","district",null,"140000","141100",null]
15:57:29.082 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2193 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["中阳县","141129","district",null,"140000","141100",null,"交口县","141130","district",null,"140000","141100",null,"孝义市","141181","district",null,"140000","141100",null,"汾阳市","141182","district",null,"140000","141100",null,"盐湖区","140802","district",null,"140000","140800",null,"临猗县","140821","district",null,"140000","140800",null,"万荣县","140822","district",null,"140000","140800",null,"闻喜县","140823","district",null,"140000","140800",null,"稷山县","140824","district",null,"140000","140800",null,"新绛县","140825","district",null,"140000","140800",null,"绛县","140826","district",null,"140000","140800",null,"垣曲县","140827","district",null,"140000","140800",null,"夏县","140828","district",null,"140000","140800",null,"平陆县","140829","district",null,"140000","140800",null,"芮城县","140830","district",null,"140000","140800",null,"永济市","140881","district",null,"140000","140800",null,"河津市","140882","district",null,"140000","140800",null,"城区","140502","district",null,"140000","140500",null,"沁水县","140521","district",null,"140000","140500",null,"阳城县","140522","district",null,"140000","140500",null,"陵川县","140524","district",null,"140000","140500",null,"泽州县","140525","district",null,"140000","140500",null,"高平市","140581","district",null,"140000","140500",null,"潞州区","140403","district",null,"140000","140400",null,"上党区","140404","district",null,"140000","140400",null,"屯留区","140405","district",null,"140000","140400",null,"潞城区","140406","district",null,"140000","140400",null,"襄垣县","140423","district",null,"140000","140400",null,"平顺县","140425","district",null,"140000","140400",null,"黎城县","140426","district",null,"140000","140400",null,"壶关县","140427","district",null,"140000","140400",null,"长子县","140428","district",null,"140000","140400",null,"武乡县","140429","district",null,"140000","140400",null,"沁县","140430","district",null,"140000","140400",null,"沁源县","140431","district",null,"140000","140400",null,"尧都区","141002","district",null,"140000","141000",null,"曲沃县","141021","district",null,"140000","141000",null,"翼城县","141022","district",null,"140000","141000",null,"襄汾县","141023","district",null,"140000","141000",null,"洪洞县","141024","district",null,"140000","141000",null,"古县","141025","district",null,"140000","141000",null,"安泽县","141026","district",null,"140000","141000",null,"浮山县","141027","district",null,"140000","141000",null,"吉县","141028","district",null,"140000","141000",null,"乡宁县","141029","district",null,"140000","141000",null,"大宁县","141030","district",null,"140000","141000",null,"隰县","141031","district",null,"140000","141000",null,"永和县","141032","district",null,"140000","141000",null,"蒲县","141033","district",null,"140000","141000",null,"汾西县","141034","district",null,"140000","141000",null,"侯马市","141081","district",null,"140000","141000",null,"霍州市","141082","district",null,"140000","141000",null,"榆次区","140702","district",null,"140000","140700",null,"太谷区","140703","district",null,"140000","140700",null,"榆社县","140721","district",null,"140000","140700",null,"左权县","140722","district",null,"140000","140700",null,"和顺县","140723","district",null,"140000","140700",null,"昔阳县","140724","district",null,"140000","140700",null,"寿阳县","140725","district",null,"140000","140700",null,"祁县","140727","district",null,"140000","140700",null,"平遥县","140728","district",null,"140000","140700",null,"灵石县","140729","district",null,"140000","140700",null,"介休市","140781","district",null,"140000","140700",null,"朔城区","140602","district",null,"140000","140600",null,"平鲁区","140603","district",null,"140000","140600",null,"山阴县","140621","district",null,"140000","140600",null,"应县","140622","district",null,"140000","140600",null,"右玉县","140623","district",null,"140000","140600",null,"怀仁市","140681","district",null,"140000","140600",null,"忻府区","140902","district",null,"140000","140900",null,"定襄县","140921","district",null,"140000","140900",null,"五台县","140922","district",null,"140000","140900",null,"代县","140923","district",null,"140000","140900",null,"繁峙县","140924","district",null,"140000","140900",null,"宁武县","140925","district",null,"140000","140900",null,"静乐县","140926","district",null,"140000","140900",null,"神池县","140927","district",null,"140000","140900",null,"五寨县","140928","district",null,"140000","140900",null,"岢岚县","140929","district",null,"140000","140900",null,"河曲县","140930","district",null,"140000","140900",null,"保德县","140931","district",null,"140000","140900",null,"偏关县","140932","district",null,"140000","140900",null,"原平市","140981","district",null,"140000","140900",null,"小店区","140105","district",null,"140000","140100",null,"迎泽区","140106","district",null,"140000","140100",null,"杏花岭区","140107","district",null,"140000","140100",null,"尖草坪区","140108","district",null,"140000","140100",null,"万柏林区","140109","district",null,"140000","140100",null,"晋源区","140110","district",null,"140000","140100",null,"清徐县","140121","district",null,"140000","140100",null,"阳曲县","140122","district",null,"140000","140100",null,"娄烦县","140123","district",null,"140000","140100",null,"古交市","140181","district",null,"140000","140100",null,"章贡区","360702","district",null,"360000","360700",null,"南康区","360703","district",null,"360000","360700",null,"赣县区","360704","district",null,"360000","360700",null,"信丰县","360722","district",null,"360000","360700",null,"大余县","360723","district",null,"360000","360700",null,"上犹县","360724","district",null,"360000","360700",null,"崇义县","360725","district",null,"360000","360700",null]
15:57:30.444 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1350 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["安远县","360726","district",null,"360000","360700",null,"定南县","360728","district",null,"360000","360700",null,"全南县","360729","district",null,"360000","360700",null,"宁都县","360730","district",null,"360000","360700",null,"于都县","360731","district",null,"360000","360700",null,"兴国县","360732","district",null,"360000","360700",null,"会昌县","360733","district",null,"360000","360700",null,"寻乌县","360734","district",null,"360000","360700",null,"石城县","360735","district",null,"360000","360700",null,"瑞金市","360781","district",null,"360000","360700",null,"龙南市","360783","district",null,"360000","360700",null,"渝水区","360502","district",null,"360000","360500",null,"分宜县","360521","district",null,"360000","360500",null,"吉州区","360802","district",null,"360000","360800",null,"青原区","360803","district",null,"360000","360800",null,"吉安县","360821","district",null,"360000","360800",null,"吉水县","360822","district",null,"360000","360800",null,"峡江县","360823","district",null,"360000","360800",null,"新干县","360824","district",null,"360000","360800",null,"永丰县","360825","district",null,"360000","360800",null,"泰和县","360826","district",null,"360000","360800",null,"遂川县","360827","district",null,"360000","360800",null,"万安县","360828","district",null,"360000","360800",null,"安福县","360829","district",null,"360000","360800",null,"永新县","360830","district",null,"360000","360800",null,"井冈山市","360881","district",null,"360000","360800",null,"临川区","361002","district",null,"360000","361000",null,"东乡区","361003","district",null,"360000","361000",null,"南城县","361021","district",null,"360000","361000",null,"黎川县","361022","district",null,"360000","361000",null,"南丰县","361023","district",null,"360000","361000",null,"崇仁县","361024","district",null,"360000","361000",null,"乐安县","361025","district",null,"360000","361000",null,"宜黄县","361026","district",null,"360000","361000",null,"金溪县","361027","district",null,"360000","361000",null,"资溪县","361028","district",null,"360000","361000",null,"广昌县","361030","district",null,"360000","361000",null,"月湖区","360602","district",null,"360000","360600",null,"余江区","360603","district",null,"360000","360600",null,"贵溪市","360681","district",null,"360000","360600",null,"袁州区","360902","district",null,"360000","360900",null,"奉新县","360921","district",null,"360000","360900",null,"万载县","360922","district",null,"360000","360900",null,"上高县","360923","district",null,"360000","360900",null,"宜丰县","360924","district",null,"360000","360900",null,"靖安县","360925","district",null,"360000","360900",null,"铜鼓县","360926","district",null,"360000","360900",null,"丰城市","360981","district",null,"360000","360900",null,"樟树市","360982","district",null,"360000","360900",null,"高安市","360983","district",null,"360000","360900",null,"安源区","360302","district",null,"360000","360300",null,"湘东区","360313","district",null,"360000","360300",null,"莲花县","360321","district",null,"360000","360300",null,"上栗县","360322","district",null,"360000","360300",null,"芦溪县","360323","district",null,"360000","360300",null,"信州区","361102","district",null,"360000","361100",null,"广丰区","361103","district",null,"360000","361100",null,"广信区","361104","district",null,"360000","361100",null,"玉山县","361123","district",null,"360000","361100",null,"铅山县","361124","district",null,"360000","361100",null,"横峰县","361125","district",null,"360000","361100",null,"弋阳县","361126","district",null,"360000","361100",null,"余干县","361127","district",null,"360000","361100",null,"鄱阳县","361128","district",null,"360000","361100",null,"万年县","361129","district",null,"360000","361100",null,"婺源县","361130","district",null,"360000","361100",null,"德兴市","361181","district",null,"360000","361100",null,"昌江区","360202","district",null,"360000","360200",null,"珠山区","360203","district",null,"360000","360200",null,"浮梁县","360222","district",null,"360000","360200",null,"乐平市","360281","district",null,"360000","360200",null,"濂溪区","360402","district",null,"360000","360400",null,"浔阳区","360403","district",null,"360000","360400",null,"柴桑区","360404","district",null,"360000","360400",null,"武宁县","360423","district",null,"360000","360400",null,"修水县","360424","district",null,"360000","360400",null,"永修县","360425","district",null,"360000","360400",null,"德安县","360426","district",null,"360000","360400",null,"都昌县","360428","district",null,"360000","360400",null,"湖口县","360429","district",null,"360000","360400",null,"彭泽县","360430","district",null,"360000","360400",null,"瑞昌市","360481","district",null,"360000","360400",null,"共青城市","360482","district",null,"360000","360400",null,"庐山市","360483","district",null,"360000","360400",null,"东湖区","360102","district",null,"360000","360100",null,"西湖区","360103","district",null,"360000","360100",null,"青云谱区","360104","district",null,"360000","360100",null,"青山湖区","360111","district",null,"360000","360100",null,"新建区","360112","district",null,"360000","360100",null,"红谷滩区","360113","district",null,"360000","360100",null,"南昌县","360121","district",null,"360000","360100",null,"安义县","360123","district",null,"360000","360100",null,"进贤县","360124","district",null,"360000","360100",null,"商州区","611002","district",null,"610000","611000",null,"洛南县","611021","district",null,"610000","611000",null,"丹凤县","611022","district",null,"610000","611000",null,"商南县","611023","district",null,"610000","611000",null,"山阳县","611024","district",null,"610000","611000",null,"镇安县","611025","district",null,"610000","611000",null,"柞水县","611026","district",null,"610000","611000",null]
15:57:31.808 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1356 millis. INSERT INTO district (name, `adcode`, level, country, province, city, district)
        VALUES
          
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         , 
            (?,?,?,?,?,?,?)
         
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        `adcode` = VALUES(`adcode`),
        level = VALUES(level),
        country = VALUES(country),
        province = VALUES(province),
        city = VALUES(city),
        district = VALUES(district)["汉台区","610702","district",null,"610000","610700",null,"南郑区","610703","district",null,"610000","610700",null,"城固县","610722","district",null,"610000","610700",null,"洋县","610723","district",null,"610000","610700",null,"西乡县","610724","district",null,"610000","610700",null,"勉县","610725","district",null,"610000","610700",null,"宁强县","610726","district",null,"610000","610700",null,"略阳县","610727","district",null,"610000","610700",null,"镇巴县","610728","district",null,"610000","610700",null,"留坝县","610729","district",null,"610000","610700",null,"佛坪县","610730","district",null,"610000","610700",null,"新城区","610102","district",null,"610000","610100",null,"碑林区","610103","district",null,"610000","610100",null,"莲湖区","610104","district",null,"610000","610100",null,"灞桥区","610111","district",null,"610000","610100",null,"未央区","610112","district",null,"610000","610100",null,"雁塔区","610113","district",null,"610000","610100",null,"阎良区","610114","district",null,"610000","610100",null,"临潼区","610115","district",null,"610000","610100",null,"长安区","610116","district",null,"610000","610100",null,"高陵区","610117","district",null,"610000","610100",null,"鄠邑区","610118","district",null,"610000","610100",null,"蓝田县","610122","district",null,"610000","610100",null,"周至县","610124","district",null,"610000","610100",null,"榆阳区","610802","district",null,"610000","610800",null,"横山区","610803","district",null,"610000","610800",null,"府谷县","610822","district",null,"610000","610800",null,"靖边县","610824","district",null,"610000","610800",null,"定边县","610825","district",null,"610000","610800",null,"绥德县","610826","district",null,"610000","610800",null,"米脂县","610827","district",null,"610000","610800",null,"佳县","610828","district",null,"610000","610800",null,"吴堡县","610829","district",null,"610000","610800",null,"清涧县","610830","district",null,"610000","610800",null,"子洲县","610831","district",null,"610000","610800",null,"神木市","610881","district",null,"610000","610800",null,"秦都区","610402","district",null,"610000","610400",null,"杨陵区","610403","district",null,"610000","610400",null,"渭城区","610404","district",null,"610000","610400",null,"三原县","610422","district",null,"610000","610400",null,"泾阳县","610423","district",null,"610000","610400",null,"乾县","610424","district",null,"610000","610400",null,"礼泉县","610425","district",null,"610000","610400",null,"永寿县","610426","district",null,"610000","610400",null,"长武县","610428","district",null,"610000","610400",null,"旬邑县","610429","district",null,"610000","610400",null,"淳化县","610430","district",null,"610000","610400",null,"武功县","610431","district",null,"610000","610400",null,"兴平市","610481","district",null,"610000","610400",null,"彬州市","610482","district",null,"610000","610400",null,"渭滨区","610302","district",null,"610000","610300",null,"金台区","610303","district",null,"610000","610300",null,"陈仓区","610304","district",null,"610000","610300",null,"凤翔区","610305","district",null,"610000","610300",null,"岐山县","610323","district",null,"610000","610300",null,"扶风县","610324","district",null,"610000","610300",null,"眉县","610326","district",null,"610000","610300",null,"陇县","610327","district",null,"610000","610300",null,"千阳县","610328","district",null,"610000","610300",null,"麟游县","610329","district",null,"610000","610300",null,"凤县","610330","district",null,"610000","610300",null,"太白县","610331","district",null,"610000","610300",null,"汉滨区","610902","district",null,"610000","610900",null,"汉阴县","610921","district",null,"610000","610900",null,"石泉县","610922","district",null,"610000","610900",null,"宁陕县","610923","district",null,"610000","610900",null,"紫阳县","610924","district",null,"610000","610900",null,"岚皋县","610925","district",null,"610000","610900",null,"平利县","610926","district",null,"610000","610900",null,"镇坪县","610927","district",null,"610000","610900",null,"白河县","610929","district",null,"610000","610900",null,"旬阳市","610981","district",null,"610000","610900",null,"宝塔区","610602","district",null,"610000","610600",null,"安塞区","610603","district",null,"610000","610600",null,"延长县","610621","district",null,"610000","610600",null,"延川县","610622","district",null,"610000","610600",null,"志丹县","610625","district",null,"610000","610600",null,"吴起县","610626","district",null,"610000","610600",null,"甘泉县","610627","district",null,"610000","610600",null,"富县","610628","district",null,"610000","610600",null,"洛川县","610629","district",null,"610000","610600",null,"宜川县","610630","district",null,"610000","610600",null,"黄龙县","610631","district",null,"610000","610600",null,"黄陵县","610632","district",null,"610000","610600",null,"子长市","610681","district",null,"610000","610600",null,"王益区","610202","district",null,"610000","610200",null,"印台区","610203","district",null,"610000","610200",null,"耀州区","610204","district",null,"610000","610200",null,"宜君县","610222","district",null,"610000","610200",null,"临渭区","610502","district",null,"610000","610500",null,"华州区","610503","district",null,"610000","610500",null,"潼关县","610522","district",null,"610000","610500",null,"大荔县","610523","district",null,"610000","610500",null,"合阳县","610524","district",null,"610000","610500",null,"澄城县","610525","district",null,"610000","610500",null,"蒲城县","610526","district",null,"610000","610500",null,"白水县","610527","district",null,"610000","610500",null,"富平县","610528","district",null,"610000","610500",null,"韩城市","610581","district",null,"610000","610500",null,"华阴市","610582","district",null,"610000","610500",null]
15:58:08.667 [main] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1361 millis. select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark
		from sys_dict_data
     
		where status = '0' and dict_type = ? order by dict_sort asc["blood_pressure_warning"]
16:14:32.137 [http-nio-8080-exec-23] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
16:23:41.006 [http-nio-8080-exec-30] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
16:24:14.071 [http-nio-8080-exec-1] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
16:25:17.700 [http-nio-8080-exec-14] ERROR c.w.f.w.e.GlobalExceptionHandler - [handleException,83] - java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:309)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:272)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:520)
	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1153)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:923)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:346)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:277)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:181)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:82)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:123)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.wanou.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:43)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at com.wanou.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:888)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1597)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:829)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.write0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
	at java.base/sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:113)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:79)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:50)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:462)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:138)
	at org.apache.tomcat.util.net.NioBlockingSelector.write(NioBlockingSelector.java:101)
	at org.apache.tomcat.util.net.NioSelectorPool.write(NioSelectorPool.java:152)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1253)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:764)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:717)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:707)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:572)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:220)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1195)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:305)
	... 106 common frames omitted
16:33:57.650 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748247056252_MisfireHandler] ERROR c.a.d.p.DruidDataSource - [handleFatalError,1859] - discard connection
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 28,012 milliseconds ago. The last packet sent successfully to the server was 28,130 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.setAutoCommit(ConnectionImpl.java:2064)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:738)
	at com.alibaba.druid.filter.logging.LogFilter.connection_setAutoCommit(LogFilter.java:446)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:733)
	at com.alibaba.druid.filter.FilterAdapter.connection_setAutoCommit(FilterAdapter.java:986)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:733)
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.setAutoCommit(ConnectionProxyImpl.java:429)
	at com.alibaba.druid.pool.DruidPooledConnection.setAutoCommit(DruidPooledConnection.java:729)
	at org.quartz.impl.jdbcjobstore.AttributeRestoringConnectionInvocationHandler.setAutoCommit(AttributeRestoringConnectionInvocationHandler.java:98)
	at org.quartz.impl.jdbcjobstore.AttributeRestoringConnectionInvocationHandler.invoke(AttributeRestoringConnectionInvocationHandler.java:66)
	at com.sun.proxy.$Proxy166.setAutoCommit(Unknown Source)
	at org.quartz.impl.jdbcjobstore.JobStoreCMT.getNonManagedTXConnection(JobStoreCMT.java:191)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3246)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 28,012 milliseconds ago. The last packet sent successfully to the server was 28,130 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:544)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:708)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:647)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:946)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:892)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1073)
	at com.mysql.cj.jdbc.ConnectionImpl.setAutoCommit(ConnectionImpl.java:2054)
	... 14 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:484)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1459)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1070)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:538)
	... 20 common frames omitted
16:33:57.655 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748247056252_MisfireHandler] ERROR o.s.s.q.LocalDataSourceJobStore - [manage,4017] - MisfireHandler: Error handling misfires: Database error recovering from misfires.
org.quartz.JobPersistenceException: Database error recovering from misfires.
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3274)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: java.sql.SQLException: connection disabled
	at com.alibaba.druid.pool.DruidPooledConnection.checkStateInternal(DruidPooledConnection.java:1185)
	at com.alibaba.druid.pool.DruidPooledConnection.checkState(DruidPooledConnection.java:1170)
	at com.alibaba.druid.pool.DruidPooledConnection.prepareStatement(DruidPooledConnection.java:353)
	at jdk.internal.reflect.GeneratedMethodAccessor88.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.quartz.impl.jdbcjobstore.AttributeRestoringConnectionInvocationHandler.invoke(AttributeRestoringConnectionInvocationHandler.java:73)
	at com.sun.proxy.$Proxy166.prepareStatement(Unknown Source)
	at org.quartz.impl.jdbcjobstore.StdJDBCDelegate.countMisfiredTriggersInState(StdJDBCDelegate.java:390)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3253)
	... 2 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 28,012 milliseconds ago. The last packet sent successfully to the server was 28,130 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.setAutoCommit(ConnectionImpl.java:2064)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:738)
	at com.alibaba.druid.filter.logging.LogFilter.connection_setAutoCommit(LogFilter.java:446)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:733)
	at com.alibaba.druid.filter.FilterAdapter.connection_setAutoCommit(FilterAdapter.java:986)
	at com.alibaba.druid.filter.FilterChainImpl.connection_setAutoCommit(FilterChainImpl.java:733)
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.setAutoCommit(ConnectionProxyImpl.java:429)
	at com.alibaba.druid.pool.DruidPooledConnection.setAutoCommit(DruidPooledConnection.java:729)
	at org.quartz.impl.jdbcjobstore.AttributeRestoringConnectionInvocationHandler.setAutoCommit(AttributeRestoringConnectionInvocationHandler.java:98)
	at org.quartz.impl.jdbcjobstore.AttributeRestoringConnectionInvocationHandler.invoke(AttributeRestoringConnectionInvocationHandler.java:66)
	at com.sun.proxy.$Proxy166.setAutoCommit(Unknown Source)
	at org.quartz.impl.jdbcjobstore.JobStoreCMT.getNonManagedTXConnection(JobStoreCMT.java:191)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3246)
	... 2 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 28,012 milliseconds ago. The last packet sent successfully to the server was 28,130 milliseconds ago.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:490)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:544)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:708)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:647)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:946)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryString(NativeProtocol.java:892)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1073)
	at com.mysql.cj.jdbc.ConnectionImpl.setAutoCommit(ConnectionImpl.java:2054)
	... 14 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.base/java.net.SocketInputStream.socketRead0(Native Method)
	at java.base/java.net.SocketInputStream.socketRead(SocketInputStream.java:115)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:168)
	at java.base/java.net.SocketInputStream.read(SocketInputStream.java:140)
	at java.base/sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:484)
	at java.base/sun.security.ssl.SSLSocketInputRecord.readHeader(SSLSocketInputRecord.java:478)
	at java.base/sun.security.ssl.SSLSocketInputRecord.bytesInCompletePacket(SSLSocketInputRecord.java:70)
	at java.base/sun.security.ssl.SSLSocketImpl.readApplicationRecord(SSLSocketImpl.java:1459)
	at java.base/sun.security.ssl.SSLSocketImpl$AppInputStream.read(SSLSocketImpl.java:1070)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:538)
	... 20 common frames omitted
16:42:57.895 [RuoyiScheduler_QuartzSchedulerThread] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 2314 millis. SELECT TRIGGER_NAME, TRIGGER_GROUP, NEXT_FIRE_TIME, PRIORITY FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND TRIGGER_STATE = ? AND NEXT_FIRE_TIME <= ? AND (MISFIRE_INSTR = -1 OR (MISFIRE_INSTR != -1 AND NEXT_FIRE_TIME >= ?)) ORDER BY NEXT_FIRE_TIME ASC, PRIORITY DESC["WAITING",1748249005191,1748248963576]
16:43:02.627 [QuartzScheduler_RuoyiScheduler-LAPTOP-JHBER3JS1748247056252_MisfireHandler] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,487] - slow sql 1739 millis. SELECT COUNT(TRIGGER_NAME) FROM QRTZ_TRIGGERS WHERE SCHED_NAME = 'RuoyiScheduler' AND NOT (MISFIRE_INSTR = -1) AND NEXT_FIRE_TIME < ? AND TRIGGER_STATE = ?[1748248968887,"WAITING"]
