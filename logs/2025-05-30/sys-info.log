09:07:47.974 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
09:07:48.004 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 29000 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
09:07:48.006 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
09:07:50.881 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
09:07:50.881 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:07:50.881 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
09:07:51.030 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:07:55.311 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
09:07:55.672 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
09:07:59.534 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
09:07:59.754 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:07:59.765 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:07:59.765 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:07:59.770 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748567279756'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:07:59.770 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
09:07:59.770 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:07:59.771 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@52a3eba3
09:08:05.266 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
09:08:05.295 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 17.626 seconds (JVM running for 19.663)
09:08:06.483 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748567279756 started.
09:09:53.022 [http-nio-8080-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:53.487 [http-nio-8080-exec-3] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:00:45.642 [http-nio-8080-exec-9] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:01:51.231 [http-nio-8080-exec-10] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:02:39.102 [http-nio-8080-exec-11] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:02:59.173 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748567279756 paused.
11:02:59.228 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748567279756 shutting down.
11:02:59.229 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748567279756 paused.
11:02:59.233 [SpringContextShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748567279756 shutdown complete.
11:02:59.236 [SpringContextShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:02:59.259 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2043] - {dataSource-1} closing ...
11:02:59.266 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2116] - {dataSource-1} closed
11:03:08.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
11:03:08.608 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 3100 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
11:03:08.610 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
11:03:13.078 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
11:03:13.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:03:13.079 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
11:03:13.304 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:03:20.424 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
11:03:21.040 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
11:03:29.869 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
11:03:30.224 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:03:30.247 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:03:30.248 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:03:30.255 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748574210228'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:03:30.255 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
11:03:30.255 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:03:30.258 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@d0538b3
11:03:41.666 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
11:03:41.713 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 33.656 seconds (JVM running for 36.023)
11:03:43.091 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748574210228 started.
11:03:44.783 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:03:45.373 [http-nio-8080-exec-1] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
11:31:20.463 [Druid-ConnectionPool-Create-947519212] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
11:31:31.164 [Druid-ConnectionPool-Create-947519212] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
13:34:21.260 [http-nio-8080-exec-23] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
14:06:37.342 [http-nio-8080-exec-19] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
14:49:00.604 [Druid-ConnectionPool-Create-947519212] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1804] - {dataSource-1} failContinuous is true
14:49:09.849 [Druid-ConnectionPool-Create-947519212] INFO  c.a.d.p.DruidAbstractDataSource - [setFailContinuous,1809] - {dataSource-1} failContinuous is false
15:36:28.018 [http-nio-8080-exec-8] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:42:50.342 [http-nio-8080-exec-11] INFO  c.w.p.s.s.i.MiniUserInfoServiceImpl - [login,109] - 请求成功
15:57:41.782 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.22.Final
15:57:41.813 [main] INFO  c.w.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication on LAPTOP-JHBER3JS with PID 15448 (D:\wangoproject\zhyl\retirement_service_djf2\retirement_service\target\classes started by ZJ in D:\wangoproject\zhyl\retirement_service_djf2)
15:57:41.813 [main] INFO  c.w.RuoYiApplication - [logStartupProfileInfo,655] - The following profiles are active: dev
15:57:44.594 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
15:57:44.595 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:57:44.595 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.41]
15:57:44.762 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:57:48.617 [main] INFO  c.a.d.p.DruidDataSource - [init,990] - {dataSource-1} inited
15:57:48.989 [main] INFO  c.w.f.t.DistrictTask - [execute,48] - 更新行政区划任务开始执行
15:57:52.020 [main] INFO  c.w.f.t.DistrictTask - [execute,131] - 更新行政区划任务执行结束
15:57:52.224 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:57:52.242 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:57:52.243 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:57:52.248 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'LAPTOP-JHBER3JS1748591872229'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:57:52.248 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
15:57:52.248 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:57:52.251 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7fb28ed
15:57:57.978 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
15:57:58.010 [main] INFO  c.w.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 16.53 seconds (JVM running for 18.658)
15:57:59.173 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_LAPTOP-JHBER3JS1748591872229 started.
15:59:12.201 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
